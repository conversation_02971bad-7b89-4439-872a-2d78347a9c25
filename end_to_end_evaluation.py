#!/usr/bin/env python3
"""
End-to-End Oracle System Evaluation
===================================

Comprehensive analysis of the complete Oracle prediction pipeline:
- Data flow from enhanced_sessions → Grammar Bridge → RG Scaler → Three-Oracle
- Component integration validation
- Performance measurement and reliability testing
- Error handling and recovery mechanisms
"""

import sys
import time
import json
import traceback
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add current directory to path
sys.path.append('.')

# Import Oracle components
from compartments.calibration import CalibrationCompartment
from compartments.predict import PredictCompartment
from core_predictor.rg_scaler_production import RGScaler

class OracleSystemEvaluator:
    """Comprehensive Oracle system evaluation"""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'data_flow': {},
            'component_integration': {},
            'performance': {},
            'error_handling': {},
            'recommendations': []
        }
        
    def evaluate_data_flow(self) -> Dict[str, Any]:
        """Trace data flow through the complete pipeline"""
        print("🔍 EVALUATING DATA FLOW PIPELINE")
        print("=" * 60)
        
        flow_results = {}
        
        # Step 1: Enhanced Sessions Analysis
        print("\n📊 Step 1: Enhanced Sessions Analysis")
        enhanced_dir = Path("enhanced_sessions")
        if enhanced_dir.exists():
            enhanced_files = list(enhanced_dir.glob("*.json"))
            flow_results['enhanced_sessions'] = {
                'available': len(enhanced_files),
                'status': 'healthy' if enhanced_files else 'missing',
                'sample_sessions': [f.stem for f in enhanced_files[:5]]
            }
            print(f"   ✅ Enhanced sessions: {len(enhanced_files)} files available")
        else:
            flow_results['enhanced_sessions'] = {'status': 'missing', 'available': 0}
            print(f"   ❌ Enhanced sessions directory missing")
        
        # Step 2: Grammar Bridge Analysis
        print("\n🌉 Step 2: Grammar Bridge Analysis")
        grammar_file = Path("grammar_bridge/cascade_events.json")
        if grammar_file.exists():
            with open(grammar_file, 'r') as f:
                grammar_data = json.load(f)
            
            events = grammar_data.get('unified_cascade_events', [])
            sessions = set(event.get('session_id', 'unknown') for event in events)
            
            flow_results['grammar_bridge'] = {
                'total_events': len(events),
                'unique_sessions': len(sessions),
                'status': 'healthy',
                'event_types': len(set(event.get('event_type', '') for event in events)),
                'time_span_minutes': max(event.get('timestamp_minutes', 0) for event in events) - 
                                   min(event.get('timestamp_minutes', 0) for event in events) if events else 0
            }
            print(f"   ✅ Grammar Bridge: {len(events)} events from {len(sessions)} sessions")
            print(f"   📈 Event types: {flow_results['grammar_bridge']['event_types']}")
        else:
            flow_results['grammar_bridge'] = {'status': 'missing'}
            print(f"   ❌ Grammar Bridge data missing")
        
        # Step 3: RG Scaler Integration Test
        print("\n⚖️ Step 3: RG Scaler Integration Test")
        try:
            rg_scaler = RGScaler()
            predict_comp = PredictCompartment()
            
            # Test with single session
            if grammar_file.exists():
                with open(grammar_file, 'r') as f:
                    grammar_data = json.load(f)
                
                # Get single session events
                events = grammar_data.get('unified_cascade_events', [])
                test_session = 'NYAM_Lvl-1_2025_08_04_REAL'
                session_events = [e for e in events if e.get('session_id') == test_session]
                
                if session_events:
                    # Test adapter
                    adapted_data = predict_comp._adapt_cascade_events_to_rg_scaler(session_events)
                    adapted_events = adapted_data.get('micro_timing_analysis', {}).get('cascade_events', [])
                    
                    if adapted_events:
                        # Test RG Scaler
                        result = rg_scaler.transform_session_data(adapted_data)
                        
                        if result:
                            flow_results['rg_scaler'] = {
                                'status': 'healthy',
                                'optimal_scale': result.optimal_scale,
                                'event_density': result.event_density,
                                'scaling_confidence': result.scaling_confidence,
                                'test_session': test_session,
                                'events_processed': len(adapted_events)
                            }
                            print(f"   ✅ RG Scaler: scale {result.optimal_scale:.2f}, density {result.event_density:.3f}")
                        else:
                            flow_results['rg_scaler'] = {'status': 'failed_transform'}
                            print(f"   ❌ RG Scaler transform failed")
                    else:
                        flow_results['rg_scaler'] = {'status': 'adapter_failed'}
                        print(f"   ❌ Grammar Bridge adapter failed")
                else:
                    flow_results['rg_scaler'] = {'status': 'no_test_session'}
                    print(f"   ⚠️ Test session {test_session} not found")
            else:
                flow_results['rg_scaler'] = {'status': 'no_grammar_data'}
                print(f"   ❌ No Grammar Bridge data for RG Scaler test")
                
        except Exception as e:
            flow_results['rg_scaler'] = {'status': 'error', 'error': str(e)}
            print(f"   ❌ RG Scaler error: {e}")
        
        # Step 4: Three-Oracle Pipeline Test
        print("\n🔮 Step 4: Three-Oracle Pipeline Test")
        try:
            predict_comp = PredictCompartment()
            
            # Test prediction generation
            start_time = time.time()
            prediction_result = predict_comp._perform_real_predictions({})
            prediction_latency = (time.time() - start_time) * 1000
            
            predictions = prediction_result.get('predictions', [])
            
            if predictions:
                first_pred = predictions[0]
                flow_results['three_oracle'] = {
                    'status': 'healthy',
                    'predictions_generated': len(predictions),
                    'latency_ms': prediction_latency,
                    'oracle_choice': first_pred.get('oracle_choice', 'unknown'),
                    'confidence': first_pred.get('confidence', 0.0),
                    'timing_window': first_pred.get('timing_window', 'unknown')
                }
                print(f"   ✅ Three-Oracle: {len(predictions)} predictions in {prediction_latency:.2f}ms")
                print(f"   🎯 Oracle choice: {first_pred.get('oracle_choice')}, confidence: {first_pred.get('confidence', 0):.3f}")
            else:
                flow_results['three_oracle'] = {'status': 'no_predictions'}
                print(f"   ❌ No predictions generated")
                
        except Exception as e:
            flow_results['three_oracle'] = {'status': 'error', 'error': str(e)}
            print(f"   ❌ Three-Oracle error: {e}")
        
        self.results['data_flow'] = flow_results
        return flow_results
    
    def evaluate_component_integration(self) -> Dict[str, Any]:
        """Test calibration → predict → output pipeline"""
        print("\n🔧 EVALUATING COMPONENT INTEGRATION")
        print("=" * 60)
        
        integration_results = {}
        
        # Test 1: Calibration Component
        print("\n⚙️ Test 1: Calibration Component")
        try:
            calibration_comp = CalibrationCompartment()
            start_time = time.time()
            calibration_result = calibration_comp.run()
            calibration_latency = (time.time() - start_time) * 1000
            
            if calibration_result and calibration_result.get('output'):
                params = calibration_result['output'].get('calibration_params', {})
                integration_results['calibration'] = {
                    'status': 'healthy',
                    'latency_ms': calibration_latency,
                    'parameters_count': len(params),
                    'has_fisher_params': 'fisher_threshold' in params,
                    'has_grammar_params': 'grammar_threshold' in params
                }
                print(f"   ✅ Calibration: {len(params)} parameters in {calibration_latency:.2f}ms")
            else:
                integration_results['calibration'] = {'status': 'failed'}
                print(f"   ❌ Calibration failed")
                
        except Exception as e:
            integration_results['calibration'] = {'status': 'error', 'error': str(e)}
            print(f"   ❌ Calibration error: {e}")
        
        # Test 2: Predict Component with Calibration
        print("\n🔮 Test 2: Predict Component with Calibration")
        try:
            predict_comp = PredictCompartment()
            
            # Use calibration parameters if available
            calibration_params = integration_results.get('calibration', {}).get('parameters', {})
            
            start_time = time.time()
            prediction_result = predict_comp._perform_real_predictions(calibration_params)
            prediction_latency = (time.time() - start_time) * 1000
            
            predictions = prediction_result.get('predictions', [])
            stats = prediction_result.get('stats', {})
            
            integration_results['predict'] = {
                'status': 'healthy' if predictions else 'no_output',
                'latency_ms': prediction_latency,
                'predictions_count': len(predictions),
                'used_calibration': bool(calibration_params),
                'stats': stats
            }
            
            if predictions:
                print(f"   ✅ Predict: {len(predictions)} predictions in {prediction_latency:.2f}ms")
            else:
                print(f"   ❌ Predict: No predictions generated")
                
        except Exception as e:
            integration_results['predict'] = {'status': 'error', 'error': str(e)}
            print(f"   ❌ Predict error: {e}")
        
        # Test 3: End-to-End Pipeline
        print("\n🔄 Test 3: End-to-End Pipeline")
        try:
            start_time = time.time()
            
            # Full pipeline: calibration → predict
            calibration_comp = CalibrationCompartment()
            predict_comp = PredictCompartment()
            
            # Step 1: Calibration
            calibration_result = calibration_comp.run()
            calibration_params = {}
            if calibration_result and calibration_result.get('output'):
                calibration_params = calibration_result['output'].get('calibration_params', {})
            
            # Step 2: Prediction
            prediction_result = predict_comp._perform_real_predictions(calibration_params)
            
            total_latency = (time.time() - start_time) * 1000
            
            predictions = prediction_result.get('predictions', [])
            
            integration_results['end_to_end'] = {
                'status': 'healthy' if predictions else 'failed',
                'total_latency_ms': total_latency,
                'predictions_generated': len(predictions),
                'pipeline_stages': 2
            }
            
            if predictions:
                print(f"   ✅ End-to-End: {len(predictions)} predictions in {total_latency:.2f}ms")
            else:
                print(f"   ❌ End-to-End: Pipeline failed")
                
        except Exception as e:
            integration_results['end_to_end'] = {'status': 'error', 'error': str(e)}
            print(f"   ❌ End-to-End error: {e}")
        
        self.results['component_integration'] = integration_results
        return integration_results

    def evaluate_performance(self) -> Dict[str, Any]:
        """Measure latency, accuracy, and reliability"""
        print("\n⚡ EVALUATING PERFORMANCE METRICS")
        print("=" * 60)

        performance_results = {}

        # Performance Test 1: Latency Benchmarking
        print("\n⏱️ Test 1: Latency Benchmarking")
        try:
            predict_comp = PredictCompartment()
            latencies = []

            for i in range(5):
                start_time = time.time()
                result = predict_comp._perform_real_predictions({})
                latency = (time.time() - start_time) * 1000
                latencies.append(latency)
                print(f"   Run {i+1}: {latency:.2f}ms")

            performance_results['latency'] = {
                'average_ms': sum(latencies) / len(latencies),
                'min_ms': min(latencies),
                'max_ms': max(latencies),
                'std_dev_ms': (sum((x - sum(latencies)/len(latencies))**2 for x in latencies) / len(latencies))**0.5,
                'samples': len(latencies)
            }

            avg_latency = performance_results['latency']['average_ms']
            if avg_latency < 100:
                print(f"   ✅ Latency: EXCELLENT ({avg_latency:.2f}ms avg)")
            elif avg_latency < 500:
                print(f"   ✅ Latency: GOOD ({avg_latency:.2f}ms avg)")
            else:
                print(f"   ⚠️ Latency: NEEDS OPTIMIZATION ({avg_latency:.2f}ms avg)")

        except Exception as e:
            performance_results['latency'] = {'status': 'error', 'error': str(e)}
            print(f"   ❌ Latency test error: {e}")

        # Performance Test 2: Memory Usage
        print("\n💾 Test 2: Memory Usage Analysis")
        try:
            import psutil
            import os

            process = psutil.Process(os.getpid())
            memory_before = process.memory_info().rss / 1024 / 1024  # MB

            # Run prediction to measure memory impact
            predict_comp = PredictCompartment()
            result = predict_comp._perform_real_predictions({})

            memory_after = process.memory_info().rss / 1024 / 1024  # MB
            memory_delta = memory_after - memory_before

            performance_results['memory'] = {
                'before_mb': memory_before,
                'after_mb': memory_after,
                'delta_mb': memory_delta,
                'status': 'healthy' if memory_delta < 100 else 'high_usage'
            }

            print(f"   📊 Memory: {memory_before:.1f}MB → {memory_after:.1f}MB (Δ{memory_delta:+.1f}MB)")

        except ImportError:
            performance_results['memory'] = {'status': 'psutil_not_available'}
            print(f"   ⚠️ Memory analysis requires psutil package")
        except Exception as e:
            performance_results['memory'] = {'status': 'error', 'error': str(e)}
            print(f"   ❌ Memory test error: {e}")

        # Performance Test 3: Accuracy Validation
        print("\n🎯 Test 3: Accuracy Validation")
        try:
            predict_comp = PredictCompartment()
            result = predict_comp._perform_real_predictions({})

            predictions = result.get('predictions', [])
            if predictions:
                confidences = [p.get('confidence', 0.0) for p in predictions]
                oracle_choices = [p.get('oracle_choice', 'unknown') for p in predictions]

                performance_results['accuracy'] = {
                    'predictions_count': len(predictions),
                    'average_confidence': sum(confidences) / len(confidences),
                    'min_confidence': min(confidences),
                    'max_confidence': max(confidences),
                    'oracle_distribution': {choice: oracle_choices.count(choice) for choice in set(oracle_choices)},
                    'high_confidence_predictions': sum(1 for c in confidences if c > 0.8)
                }

                avg_conf = performance_results['accuracy']['average_confidence']
                print(f"   ✅ Accuracy: {avg_conf:.3f} average confidence")
                print(f"   📊 Oracle distribution: {performance_results['accuracy']['oracle_distribution']}")
            else:
                performance_results['accuracy'] = {'status': 'no_predictions'}
                print(f"   ❌ No predictions to validate accuracy")

        except Exception as e:
            performance_results['accuracy'] = {'status': 'error', 'error': str(e)}
            print(f"   ❌ Accuracy test error: {e}")

        self.results['performance'] = performance_results
        return performance_results

    def evaluate_error_handling(self) -> Dict[str, Any]:
        """Test failure modes and recovery mechanisms"""
        print("\n🛡️ EVALUATING ERROR HANDLING")
        print("=" * 60)

        error_results = {}

        # Error Test 1: Missing Data Handling
        print("\n🚫 Test 1: Missing Data Handling")
        try:
            predict_comp = PredictCompartment()

            # Test with empty session data
            empty_result = predict_comp._adapt_cascade_events_to_rg_scaler([])

            error_results['missing_data'] = {
                'handles_empty_events': bool(empty_result.get('micro_timing_analysis', {}).get('cascade_events') == []),
                'graceful_degradation': True,
                'status': 'healthy'
            }
            print(f"   ✅ Missing data: Handled gracefully")

        except Exception as e:
            error_results['missing_data'] = {'status': 'error', 'error': str(e)}
            print(f"   ❌ Missing data error: {e}")

        # Error Test 2: Invalid Session Handling
        print("\n❌ Test 2: Invalid Session Handling")
        try:
            predict_comp = PredictCompartment()

            # Test with non-existent session
            invalid_events = predict_comp._load_grammar_bridge_events("INVALID_SESSION_ID")

            error_results['invalid_session'] = {
                'returns_empty_list': isinstance(invalid_events, list) and len(invalid_events) == 0,
                'no_exception_thrown': True,
                'status': 'healthy'
            }
            print(f"   ✅ Invalid session: Returns empty list gracefully")

        except Exception as e:
            error_results['invalid_session'] = {'status': 'error', 'error': str(e)}
            print(f"   ❌ Invalid session error: {e}")

        # Error Test 3: Component Failure Recovery
        print("\n🔄 Test 3: Component Failure Recovery")
        try:
            predict_comp = PredictCompartment()

            # Test fallback predictions
            fallback_result = predict_comp._fallback_predictions({})

            fallback_predictions = fallback_result.get('predictions', [])

            error_results['component_failure'] = {
                'has_fallback_mechanism': len(fallback_predictions) > 0,
                'fallback_predictions_count': len(fallback_predictions),
                'maintains_api_contract': all('oracle_choice' in p for p in fallback_predictions),
                'status': 'healthy'
            }
            print(f"   ✅ Component failure: {len(fallback_predictions)} fallback predictions available")

        except Exception as e:
            error_results['component_failure'] = {'status': 'error', 'error': str(e)}
            print(f"   ❌ Component failure test error: {e}")

        self.results['error_handling'] = error_results
        return error_results

    def generate_recommendations(self) -> List[str]:
        """Generate specific recommendations based on evaluation results"""
        recommendations = []

        # Data flow recommendations
        data_flow = self.results.get('data_flow', {})
        if data_flow.get('enhanced_sessions', {}).get('status') != 'healthy':
            recommendations.append("🔧 CRITICAL: Fix enhanced sessions data availability")

        if data_flow.get('grammar_bridge', {}).get('status') != 'healthy':
            recommendations.append("🔧 CRITICAL: Fix Grammar Bridge data pipeline")

        if data_flow.get('rg_scaler', {}).get('status') != 'healthy':
            recommendations.append("🔧 HIGH: Fix RG Scaler integration issues")

        # Performance recommendations
        performance = self.results.get('performance', {})
        latency = performance.get('latency', {})
        if latency.get('average_ms', 0) > 500:
            recommendations.append("⚡ MEDIUM: Optimize prediction latency (>500ms)")

        accuracy = performance.get('accuracy', {})
        if accuracy.get('average_confidence', 0) < 0.7:
            recommendations.append("🎯 HIGH: Improve prediction confidence (<0.7)")

        # Integration recommendations
        integration = self.results.get('component_integration', {})
        if integration.get('calibration', {}).get('status') != 'healthy':
            recommendations.append("⚙️ HIGH: Fix calibration component issues")

        if integration.get('end_to_end', {}).get('status') != 'healthy':
            recommendations.append("🔄 CRITICAL: Fix end-to-end pipeline integration")

        # Error handling recommendations
        error_handling = self.results.get('error_handling', {})
        for test_name, test_result in error_handling.items():
            if test_result.get('status') == 'error':
                recommendations.append(f"🛡️ MEDIUM: Improve error handling for {test_name}")

        # If no issues found
        if not recommendations:
            recommendations.append("✅ EXCELLENT: System is production-ready with no critical issues")

        self.results['recommendations'] = recommendations
        return recommendations

    def run_complete_evaluation(self) -> Dict[str, Any]:
        """Run complete end-to-end evaluation"""
        print("🔍 ORACLE SYSTEM COMPREHENSIVE EVALUATION")
        print("=" * 80)
        print(f"Timestamp: {self.results['timestamp']}")

        # Run all evaluation phases
        self.evaluate_data_flow()
        self.evaluate_component_integration()
        self.evaluate_performance()
        self.evaluate_error_handling()

        # Generate recommendations
        recommendations = self.generate_recommendations()

        # Summary
        print("\n📋 EVALUATION SUMMARY")
        print("=" * 40)

        for rec in recommendations:
            print(f"   {rec}")

        # Overall system health
        critical_issues = [r for r in recommendations if 'CRITICAL' in r]
        high_issues = [r for r in recommendations if 'HIGH' in r]

        if not critical_issues and not high_issues:
            overall_status = "PRODUCTION_READY"
        elif not critical_issues:
            overall_status = "MINOR_ISSUES"
        else:
            overall_status = "NEEDS_FIXES"

        self.results['overall_status'] = overall_status

        print(f"\n🎯 OVERALL STATUS: {overall_status}")

        return self.results

if __name__ == "__main__":
    evaluator = OracleSystemEvaluator()
    results = evaluator.run_complete_evaluation()

    # Save results
    with open('oracle_system_evaluation.json', 'w') as f:
        json.dump(results, f, indent=2)

    print(f"\n💾 Results saved to oracle_system_evaluation.json")
