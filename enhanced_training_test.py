#!/usr/bin/env python3
"""
Enhanced Training Test - Breaking the 93.8% Ceiling
==================================================

Test the system with 1000 synthetic non-cascades to validate >95% accuracy hypothesis.
Compare: 81 real sessions vs 81 real + 1000 synthetic non-cascades.
"""

import json
import numpy as np
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, classification_report
from sklearn.preprocessing import StandardScaler
import time

from xgboost_real_trainer import XGBoostRealTrainer

def test_enhanced_system():
    """Test system performance with synthetic non-cascade augmentation"""
    
    print("🚀 ENHANCED SYSTEM TEST")
    print("=" * 25)
    print("Objective: Break 93.8% ceiling with synthetic non-cascades")
    print("Method: Compare real-only vs real+synthetic training")
    print()
    
    # Load real training data
    trainer = XGBoostRealTrainer()
    sessions = trainer.load_enhanced_sessions()
    examples = trainer.extract_training_examples(sessions)
    X_real, y_real = trainer.prepare_training_data(examples)
    
    print(f"📊 Real dataset:")
    print(f"   Sessions: {len(examples)}")
    print(f"   Non-cascades: {np.sum(y_real == 0)}")
    print(f"   Cascades: {np.sum(y_real == 1)}")
    print(f"   Cascade rate: {np.mean(y_real):.1%}")
    
    # Load synthetic non-cascade data
    try:
        with open('synthetic_training_data.json', 'r') as f:
            synthetic_data = json.load(f)
        
        print(f"\n📈 Synthetic dataset:")
        print(f"   Synthetic scenarios: {len(synthetic_data)}")
        
        # Convert synthetic data to training format
        # For now, create simple feature vectors (can be enhanced later)
        X_synthetic = []
        y_synthetic = []
        
        for scenario in synthetic_data:
            # Simple feature extraction from synthetic scenarios
            events = scenario.get('events', [])
            
            if events:
                # Basic features: magnitude statistics, event counts, etc.
                magnitudes = [abs(e.get('magnitude', 0)) for e in events]
                
                features = [
                    len(events),                    # Event count
                    np.mean(magnitudes),            # Avg magnitude
                    np.std(magnitudes),             # Magnitude variance
                    max(magnitudes),                # Peak magnitude
                    min(magnitudes),                # Min magnitude
                    scenario.get('confidence_threshold', 0.3),  # Expected confidence
                ]
                
                # Pad to match real data feature count (13 features)
                while len(features) < 13:
                    features.append(0.0)
                
                X_synthetic.append(features[:13])
                y_synthetic.append(0)  # All synthetic data are non-cascades
        
        X_synthetic = np.array(X_synthetic)
        y_synthetic = np.array(y_synthetic)
        
        print(f"   Features extracted: {X_synthetic.shape}")
        print(f"   All non-cascades: {len(y_synthetic)} examples")
        
    except FileNotFoundError:
        print("⚠️ Synthetic training data not found, using basic synthetic data")
        
        # Generate basic synthetic non-cascades
        n_synthetic = 500  # Reduced for demo
        X_synthetic = np.random.normal(0, 0.5, (n_synthetic, 13))  # Lower variance features
        y_synthetic = np.zeros(n_synthetic)  # All non-cascades
        
        print(f"   Generated {n_synthetic} basic synthetic non-cascades")
    
    # Test 1: Real data only (baseline)
    print(f"\n🧪 Test 1: Real data only (baseline)")
    start_time = time.time()
    
    scaler1 = StandardScaler()
    X_real_scaled = scaler1.fit_transform(X_real)
    
    model1 = GradientBoostingClassifier(
        n_estimators=100,
        max_depth=4,
        learning_rate=0.05,
        random_state=42
    )
    
    cv_scores1 = cross_val_score(model1, X_real_scaled, y_real, cv=5, scoring='accuracy')
    
    time1 = time.time() - start_time
    
    print(f"   CV Accuracy: {np.mean(cv_scores1):.1%} ± {np.std(cv_scores1):.1%}")
    print(f"   Training time: {time1:.1f}s")
    
    # Test 2: Real + Synthetic data (enhanced)
    print(f"\n🚀 Test 2: Real + Synthetic data (enhanced)")
    start_time = time.time()
    
    # Combine datasets
    X_combined = np.vstack([X_real, X_synthetic])
    y_combined = np.hstack([y_real, y_synthetic])
    
    print(f"   Combined dataset:")
    print(f"     Total samples: {len(y_combined)}")
    print(f"     Non-cascades: {np.sum(y_combined == 0)} ({np.sum(y_combined == 0)/len(y_combined):.1%})")
    print(f"     Cascades: {np.sum(y_combined == 1)} ({np.sum(y_combined == 1)/len(y_combined):.1%})")
    
    scaler2 = StandardScaler()
    X_combined_scaled = scaler2.fit_transform(X_combined)
    
    model2 = GradientBoostingClassifier(
        n_estimators=100,
        max_depth=4,
        learning_rate=0.05,
        random_state=42
    )
    
    cv_scores2 = cross_val_score(model2, X_combined_scaled, y_combined, cv=5, scoring='accuracy')
    
    time2 = time.time() - start_time
    
    print(f"   CV Accuracy: {np.mean(cv_scores2):.1%} ± {np.std(cv_scores2):.1%}")
    print(f"   Training time: {time2:.1f}s")
    
    # Test 3: Validate on real data only
    print(f"\n✅ Test 3: Validate enhanced model on real data")
    
    # Train enhanced model on combined data
    model2.fit(X_combined_scaled, y_combined)
    
    # Test on real data only
    real_predictions = model2.predict(scaler2.transform(X_real))
    real_accuracy = accuracy_score(y_real, real_predictions)
    
    print(f"   Real-world validation: {real_accuracy:.1%}")
    print(f"   Improvement: {real_accuracy - np.mean(cv_scores1):.1%}")
    
    # Analysis
    print(f"\n📈 BREAKTHROUGH ANALYSIS")
    print("=" * 25)
    
    baseline_acc = np.mean(cv_scores1)
    enhanced_acc = np.mean(cv_scores2)
    improvement = enhanced_acc - baseline_acc
    
    print(f"📊 Performance Comparison:")
    print(f"   Baseline (real only): {baseline_acc:.1%} ± {np.std(cv_scores1):.1%}")
    print(f"   Enhanced (real+synthetic): {enhanced_acc:.1%} ± {np.std(cv_scores2):.1%}")
    print(f"   Improvement: {improvement:.1%}")
    print(f"   Real-world validation: {real_accuracy:.1%}")
    
    if enhanced_acc > 0.95:
        print(f"\n🎉 BREAKTHROUGH ACHIEVED!")
        print(f"   >95% accuracy target: ✅ {enhanced_acc:.1%}")
        print(f"   Synthetic augmentation successful")
    elif improvement > 0.02:  # >2% improvement
        print(f"\n🎯 SIGNIFICANT IMPROVEMENT")
        print(f"   +{improvement:.1%} accuracy gain")
        print(f"   Synthetic augmentation effective")
    else:
        print(f"\n🔍 MARGINAL IMPROVEMENT")
        print(f"   Synthetic data quality needs refinement")
        print(f"   Consider more sophisticated generation methods")
    
    # Training time comparison
    efficiency_gain = time1 / time2 if time2 > 0 else 1.0
    print(f"\n⏱️ Training Efficiency:")
    print(f"   Real only: {time1:.1f}s")
    print(f"   Real+Synthetic: {time2:.1f}s")
    print(f"   Efficiency ratio: {efficiency_gain:.1f}×")
    
    # Next steps recommendation
    if enhanced_acc > 0.95:
        print(f"\n🚀 NEXT STEPS:")
        print(f"   1. Deploy enhanced model to production")
        print(f"   2. Retrain ensemble with synthetic data")
        print(f"   3. Monitor real-world performance")
    else:
        print(f"\n🔧 OPTIMIZATION NEEDED:")
        print(f"   1. Improve synthetic data quality")
        print(f"   2. Use GANs or more sophisticated generation")
        print(f"   3. Validate synthetic patterns match real market behavior")
    
    return {
        'baseline_accuracy': baseline_acc,
        'enhanced_accuracy': enhanced_acc,
        'improvement': improvement,
        'real_world_validation': real_accuracy,
        'breakthrough_achieved': enhanced_acc > 0.95,
        'significant_improvement': improvement > 0.02
    }

if __name__ == "__main__":
    results = test_enhanced_system()