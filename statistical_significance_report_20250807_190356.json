{"report_metadata": {"analysis_timestamp": "2025-08-07T19:03:56.549488", "analyzer_version": "1.0", "confidence_level": 0.95}, "sample_analysis": {"current_sample_size": 23, "target_sample_size_range": [80, 100], "sample_adequacy_ratio": 0.2875}, "statistical_power": {"overall_power": 0.2875, "production_readiness_score": 20.25, "power_analysis": {"overall_power": 0.2875, "margin_of_error_ratio": 1.8650096164806276, "min_detectable_effect": 0.3730019232961255, "pattern_power_ratio": 0.0, "sample_adequacy_ratio": 0.2875, "confidence_scaling_factor": 0.5361902647381804}}, "pattern_statistics": [{"pattern_name": "fpfvg_lifecycle_13:31:00", "occurrences": 2, "observed_probability": 0.08695652173913043, "confidence_interval": [0, 1], "margin_of_error": 0.5, "statistical_power": 0.0, "sample_adequacy_score": 0.0}, {"pattern_name": "fpfvg_lifecycle_00:02:00", "occurrences": 1, "observed_probability": 0.043478260869565216, "confidence_interval": [0, 1], "margin_of_error": 0.5, "statistical_power": 0.0, "sample_adequacy_score": 0.0}, {"pattern_name": "fpfvg_lifecycle_12:16:00", "occurrences": 1, "observed_probability": 0.043478260869565216, "confidence_interval": [0, 1], "margin_of_error": 0.5, "statistical_power": 0.0, "sample_adequacy_score": 0.0}, {"pattern_name": "fpfvg_lifecycle_02:01:00", "occurrences": 1, "observed_probability": 0.043478260869565216, "confidence_interval": [0, 1], "margin_of_error": 0.5, "statistical_power": 0.0, "sample_adequacy_score": 0.0}, {"pattern_name": "fpfvg_lifecycle_09:37:00", "occurrences": 1, "observed_probability": 0.043478260869565216, "confidence_interval": [0, 1], "margin_of_error": 0.5, "statistical_power": 0.0, "sample_adequacy_score": 0.0}, {"pattern_name": "fpfvg_lifecycle_5.0", "occurrences": 1, "observed_probability": 0.043478260869565216, "confidence_interval": [0, 1], "margin_of_error": 0.5, "statistical_power": 0.0, "sample_adequacy_score": 0.0}, {"pattern_name": "fpfvg_lifecycle_09:35:00", "occurrences": 1, "observed_probability": 0.043478260869565216, "confidence_interval": [0, 1], "margin_of_error": 0.5, "statistical_power": 0.0, "sample_adequacy_score": 0.0}, {"pattern_name": "consolidation_breakout_10:02:00", "occurrences": 1, "observed_probability": 0.043478260869565216, "confidence_interval": [0, 1], "margin_of_error": 0.5, "statistical_power": 0.0, "sample_adequacy_score": 0.0}, {"pattern_name": "consolidation_breakout_10:04:00", "occurrences": 1, "observed_probability": 0.043478260869565216, "confidence_interval": [0, 1], "margin_of_error": 0.5, "statistical_power": 0.0, "sample_adequacy_score": 0.0}, {"pattern_name": "consolidation_breakout_10:06:00", "occurrences": 1, "observed_probability": 0.043478260869565216, "confidence_interval": [0, 1], "margin_of_error": 0.5, "statistical_power": 0.0, "sample_adequacy_score": 0.0}, {"pattern_name": "consolidation_breakout_10:17:00", "occurrences": 1, "observed_probability": 0.043478260869565216, "confidence_interval": [0, 1], "margin_of_error": 0.5, "statistical_power": 0.0, "sample_adequacy_score": 0.0}, {"pattern_name": "consolidation_breakout_10:19:00", "occurrences": 1, "observed_probability": 0.043478260869565216, "confidence_interval": [0, 1], "margin_of_error": 0.5, "statistical_power": 0.0, "sample_adequacy_score": 0.0}, {"pattern_name": "consolidation_breakout_10:21:00", "occurrences": 1, "observed_probability": 0.043478260869565216, "confidence_interval": [0, 1], "margin_of_error": 0.5, "statistical_power": 0.0, "sample_adequacy_score": 0.0}, {"pattern_name": "fpfvg_lifecycle_7.0", "occurrences": 1, "observed_probability": 0.043478260869565216, "confidence_interval": [0, 1], "margin_of_error": 0.5, "statistical_power": 0.0, "sample_adequacy_score": 0.0}, {"pattern_name": "fpfvg_lifecycle_00:07:00", "occurrences": 1, "observed_probability": 0.043478260869565216, "confidence_interval": [0, 1], "margin_of_error": 0.5, "statistical_power": 0.0, "sample_adequacy_score": 0.0}], "session_type_analysis": [{"session_type": "NYPM", "session_count": 4, "unique_patterns": 1, "cascade_probability_mean": 0.6549336566659147, "statistical_power": 0.4}, {"session_type": "NYAM", "session_count": 4, "unique_patterns": 9, "cascade_probability_mean": 0.6017400831624515, "statistical_power": 0.4}, {"session_type": "MIDNIGHT", "session_count": 3, "unique_patterns": 3, "cascade_probability_mean": 0.6066285714285714, "statistical_power": 0.3}, {"session_type": "ASIA", "session_count": 3, "unique_patterns": 0, "cascade_probability_mean": 0.6464411020688302, "statistical_power": 0.3}, {"session_type": "PREMARKET", "session_count": 3, "unique_patterns": 0, "cascade_probability_mean": 0.62212077294686, "statistical_power": 0.3}, {"session_type": "LUNCH", "session_count": 3, "unique_patterns": 1, "cascade_probability_mean": 0.6206260869565217, "statistical_power": 0.3}, {"session_type": "LONDON", "session_count": 3, "unique_patterns": 1, "cascade_probability_mean": 0.6110180799592563, "statistical_power": 0.3}], "error_analysis": {"type_i_error_rate": 0.050000000000000044, "type_ii_error_rate": 0.7, "effect_size_detection_threshold": 0.3730019232961255}, "production_recommendation": "❌ NOT READY: Significant additional data required\n\nSpecific Recommendations:\n• Increase sample size from N=23 to N≥50 (minimum)\n• Focus on sessions with high pattern density\n• Improve pattern detection to reduce false negatives", "confidence_assessment": {"confidence_level": 0.95, "z_critical": 1.959963984540054, "margin_scaling": 1.8650096164806276}}