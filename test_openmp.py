#!/usr/bin/env python3
"""Test OpenMP and XGBoost integration"""

import os
import sys
import time

# Set paths for MacPorts libomp
os.environ['DYLD_LIBRARY_PATH'] = '/opt/local/lib/libomp:' + os.environ.get('DYLD_LIBRARY_PATH', '')
os.environ['OMP_NUM_THREADS'] = '4'

print("Testing Project Oracle Native Integration...")
print("=" * 50)

# Test 1: Load OpenMP
import ctypes
try:
    libomp = ctypes.CDLL('/opt/local/lib/libomp/libomp.dylib')
    print("✅ OpenMP library loaded successfully")
except OSError as e:
    print(f"❌ OpenMP failed: {e}")
    sys.exit(1)

# Test 2: Import XGBoost
try:
    import xgboost as xgb
    print(f"✅ XGBoost imported (version {xgb.__version__})")
except ImportError as e:
    print(f"❌ XGBoost import failed: {e}")
    sys.exit(1)

# Test 3: Run XGBoost with OpenMP
import numpy as np
print("\nTesting XGBoost with OpenMP parallelization...")
X = np.random.randn(1000, 3)  # Feature vector: [density, Fisher_info, σ]
y = np.random.randint(0, 2, 1000)

start_time = time.time()
model = xgb.XGBClassifier(
    n_jobs=4,
    tree_method='hist',
    n_estimators=100
)
model.fit(X, y)
elapsed = time.time() - start_time

print(f"✅ XGBoost training completed in {elapsed:.3f}s")
print(f"   Model uses {model.n_jobs} threads")

# Test 4: Check if we can import oracle modules
try:
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    import rg_scaler
    import fisher_monitor
    import three_oracle_system
    print("\n✅ All Oracle modules importable")
except ImportError as e:
    print(f"\n⚠️ Oracle module import issue: {e}")

print("\n" + "=" * 50)
print("Native integration test complete!")
