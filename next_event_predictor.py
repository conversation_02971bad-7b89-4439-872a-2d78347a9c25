#!/usr/bin/env python3
"""
Next-Event Prediction with Timing Windows
=========================================

Predicts the next expected event in cascade pattern recognition with timing
probability windows based on historical patterns and grammatical state analysis.

Features:
- Temporal probability modeling for next events
- Confidence intervals for timing predictions
- Integration with grammatical state tracker
- Historical pattern analysis for timing calibration
- Real-time prediction updates
"""

import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import pandas as pd
from scipy import stats
from collections import defaultdict, deque

@dataclass
class TimingWindow:
    """Probability window for event timing"""
    event_type: str
    mean_minutes: float
    std_minutes: float
    confidence_95_lower: float
    confidence_95_upper: float
    probability_peak: float
    historical_count: int

@dataclass
class NextEventPrediction:
    """Complete next event prediction"""
    predicted_event: str
    probability: float
    timing_windows: List[TimingWindow]
    confidence_score: float
    alternative_events: List[Tuple[str, float]]
    pattern_momentum: float
    risk_factors: List[str]

class NextEventPredictor:
    """Next-event prediction system with timing windows"""
    
    def __init__(self, historical_data_file: str = None):
        self.historical_patterns = self._load_historical_patterns(historical_data_file)
        self.transition_matrix = self._build_transition_matrix()
        self.timing_calibration = self._calibrate_timing_windows()
        
        print("🔮 Next-Event Predictor initialized")
        print(f"   Historical patterns: {len(self.historical_patterns)}")
        print(f"   State transitions: {sum(len(v) for v in self.transition_matrix.values())}")
        print(f"   Timing calibrations: {len(self.timing_calibration)}")
    
    def _load_historical_patterns(self, data_file: Optional[str]) -> List[Dict]:
        """Load historical cascade patterns for timing analysis"""
        if data_file and os.path.exists(data_file):
            try:
                with open(data_file, 'r') as f:
                    return json.load(f)
            except:
                print("⚠️ Could not load historical data file")
        
        # Default historical patterns for demo/calibration
        return [
            {
                'pattern_id': 'cascade_1',
                'events': [
                    {'type': 'expansion_high', 'timestamp': '09:30:15', 'minutes_from_start': 0},
                    {'type': 'liquidity_grab', 'timestamp': '09:35:22', 'minutes_from_start': 5.12},
                    {'type': 'consolidation_break', 'timestamp': '09:42:10', 'minutes_from_start': 11.92},
                    {'type': 'cascade_trigger', 'timestamp': '09:45:30', 'minutes_from_start': 15.25}
                ],
                'total_duration': 15.25,
                'success': True
            },
            {
                'pattern_id': 'cascade_2',
                'events': [
                    {'type': 'expansion_high', 'timestamp': '10:15:00', 'minutes_from_start': 0},
                    {'type': 'liquidity_grab', 'timestamp': '10:18:45', 'minutes_from_start': 3.75},
                    {'type': 'consolidation_break', 'timestamp': '10:28:12', 'minutes_from_start': 13.20},
                    {'type': 'cascade_trigger', 'timestamp': '10:31:55', 'minutes_from_start': 16.92}
                ],
                'total_duration': 16.92,
                'success': True
            },
            {
                'pattern_id': 'cascade_3',
                'events': [
                    {'type': 'expansion_high', 'timestamp': '14:22:30', 'minutes_from_start': 0},
                    {'type': 'liquidity_grab', 'timestamp': '14:29:15', 'minutes_from_start': 6.75},
                    {'type': 'consolidation_break', 'timestamp': '14:35:45', 'minutes_from_start': 13.25},
                    {'type': 'cascade_trigger', 'timestamp': '14:38:20', 'minutes_from_start': 15.83}
                ],
                'total_duration': 15.83,
                'success': True
            },
            # Add failed patterns for comparison
            {
                'pattern_id': 'failed_1',
                'events': [
                    {'type': 'expansion_high', 'timestamp': '11:30:00', 'minutes_from_start': 0},
                    {'type': 'liquidity_grab', 'timestamp': '11:34:30', 'minutes_from_start': 4.5}
                ],
                'total_duration': 25.0,
                'success': False,
                'failure_reason': 'timeout'
            },
            {
                'pattern_id': 'failed_2',
                'events': [
                    {'type': 'expansion_high', 'timestamp': '15:45:00', 'minutes_from_start': 0},
                    {'type': 'liquidity_grab', 'timestamp': '15:52:15', 'minutes_from_start': 7.25},
                    {'type': 'consolidation_break', 'timestamp': '16:08:30', 'minutes_from_start': 23.5}
                ],
                'total_duration': 30.0,
                'success': False,
                'failure_reason': 'pattern_break'
            }
        ]
    
    def _build_transition_matrix(self) -> Dict[str, Dict[str, float]]:
        """Build state transition probability matrix"""
        transitions = defaultdict(lambda: defaultdict(int))
        totals = defaultdict(int)
        
        # Count transitions from successful patterns
        for pattern in self.historical_patterns:
            if pattern.get('success', False):
                events = pattern['events']
                for i in range(len(events) - 1):
                    current_event = events[i]['type']
                    next_event = events[i + 1]['type']
                    transitions[current_event][next_event] += 1
                    totals[current_event] += 1
        
        # Convert to probabilities
        transition_matrix = {}
        for current_state in transitions:
            transition_matrix[current_state] = {}
            for next_state in transitions[current_state]:
                transition_matrix[current_state][next_state] = (
                    transitions[current_state][next_state] / totals[current_state]
                )
        
        return transition_matrix
    
    def _calibrate_timing_windows(self) -> Dict[Tuple[str, str], TimingWindow]:
        """Calibrate timing windows for state transitions"""
        timing_data = defaultdict(list)
        
        # Collect timing data for each transition
        for pattern in self.historical_patterns:
            if pattern.get('success', False):
                events = pattern['events']
                for i in range(len(events) - 1):
                    current_event = events[i]['type']
                    next_event = events[i + 1]['type']
                    
                    current_time = events[i]['minutes_from_start']
                    next_time = events[i + 1]['minutes_from_start']
                    time_diff = next_time - current_time
                    
                    timing_data[(current_event, next_event)].append(time_diff)
        
        # Calculate timing windows
        timing_windows = {}
        for transition, times in timing_data.items():
            if len(times) >= 2:  # Need at least 2 samples for statistics
                times_array = np.array(times)
                mean_time = np.mean(times_array)
                std_time = np.std(times_array)
                
                # 95% confidence interval
                confidence_interval = stats.t.interval(
                    0.95, len(times_array) - 1,
                    loc=mean_time, scale=stats.sem(times_array)
                )
                
                timing_windows[transition] = TimingWindow(
                    event_type=transition[1],
                    mean_minutes=mean_time,
                    std_minutes=std_time,
                    confidence_95_lower=confidence_interval[0],
                    confidence_95_upper=confidence_interval[1],
                    probability_peak=mean_time,
                    historical_count=len(times)
                )
        
        return timing_windows
    
    def predict_next_event(
        self, 
        current_state: str,
        pattern_buffer: List[Dict],
        confidence: float = 0.8
    ) -> NextEventPrediction:
        """Predict next event with timing windows"""
        
        print(f"🔮 Predicting next event from state: {current_state}")
        print(f"   Pattern buffer length: {len(pattern_buffer)}")
        print(f"   Current confidence: {confidence:.1%}")
        
        # Get possible next events from transition matrix
        possible_events = self.transition_matrix.get(current_state, {})
        
        if not possible_events:
            print(f"⚠️ No historical transitions from state: {current_state}")
            return self._default_prediction(current_state)
        
        # Find most likely next event
        best_event = max(possible_events.items(), key=lambda x: x[1])
        predicted_event = best_event[0]
        base_probability = best_event[1]
        
        print(f"   Most likely next event: {predicted_event} ({base_probability:.1%})")
        
        # Get timing windows for this transition
        timing_windows = self._get_timing_windows_for_transition(
            current_state, predicted_event, pattern_buffer
        )
        
        # Calculate confidence score
        confidence_score = self._calculate_confidence_score(
            base_probability, confidence, len(pattern_buffer)
        )
        
        # Get alternative events (sorted by probability)
        alternatives = sorted(
            [(event, prob) for event, prob in possible_events.items() 
             if event != predicted_event],
            key=lambda x: x[1],
            reverse=True
        )[:3]  # Top 3 alternatives
        
        # Calculate pattern momentum
        pattern_momentum = self._calculate_pattern_momentum(pattern_buffer)
        
        # Identify risk factors
        risk_factors = self._identify_risk_factors(
            current_state, pattern_buffer, confidence
        )
        
        prediction = NextEventPrediction(
            predicted_event=predicted_event,
            probability=base_probability,
            timing_windows=timing_windows,
            confidence_score=confidence_score,
            alternative_events=alternatives,
            pattern_momentum=pattern_momentum,
            risk_factors=risk_factors
        )
        
        self._print_prediction_summary(prediction)
        
        return prediction
    
    def _get_timing_windows_for_transition(
        self, 
        current_state: str, 
        next_event: str, 
        pattern_buffer: List[Dict]
    ) -> List[TimingWindow]:
        """Get timing windows for the predicted transition"""
        
        transition_key = (current_state, next_event)
        base_window = self.timing_calibration.get(transition_key)
        
        if not base_window:
            # Default timing window if no historical data
            base_window = TimingWindow(
                event_type=next_event,
                mean_minutes=8.0,  # Default expectation
                std_minutes=4.0,
                confidence_95_lower=2.0,
                confidence_95_upper=15.0,
                probability_peak=8.0,
                historical_count=0
            )
        
        # Adjust timing based on current pattern momentum
        momentum_adjustment = self._calculate_timing_adjustment(pattern_buffer)
        
        # Create adjusted timing windows
        adjusted_window = TimingWindow(
            event_type=base_window.event_type,
            mean_minutes=base_window.mean_minutes * momentum_adjustment,
            std_minutes=base_window.std_minutes,
            confidence_95_lower=base_window.confidence_95_lower * momentum_adjustment,
            confidence_95_upper=base_window.confidence_95_upper * momentum_adjustment,
            probability_peak=base_window.probability_peak * momentum_adjustment,
            historical_count=base_window.historical_count
        )
        
        # Add probability distribution windows
        windows = [adjusted_window]
        
        # Add 50% confidence window (tighter)
        windows.append(TimingWindow(
            event_type=f"{next_event}_50pct",
            mean_minutes=adjusted_window.mean_minutes,
            std_minutes=adjusted_window.std_minutes * 0.6,
            confidence_95_lower=adjusted_window.mean_minutes - adjusted_window.std_minutes * 0.6,
            confidence_95_upper=adjusted_window.mean_minutes + adjusted_window.std_minutes * 0.6,
            probability_peak=adjusted_window.mean_minutes,
            historical_count=adjusted_window.historical_count
        ))
        
        # Add 80% confidence window (medium)
        windows.append(TimingWindow(
            event_type=f"{next_event}_80pct",
            mean_minutes=adjusted_window.mean_minutes,
            std_minutes=adjusted_window.std_minutes * 0.8,
            confidence_95_lower=adjusted_window.mean_minutes - adjusted_window.std_minutes * 0.8,
            confidence_95_upper=adjusted_window.mean_minutes + adjusted_window.std_minutes * 0.8,
            probability_peak=adjusted_window.mean_minutes,
            historical_count=adjusted_window.historical_count
        ))
        
        return windows
    
    def _calculate_confidence_score(
        self, 
        base_probability: float, 
        state_confidence: float, 
        buffer_length: int
    ) -> float:
        """Calculate overall prediction confidence"""
        
        # Base confidence from transition probability
        transition_confidence = base_probability
        
        # Boost from grammatical state confidence
        state_boost = state_confidence * 0.3
        
        # Boost from pattern completeness (more events = higher confidence)
        completeness_boost = min(0.2, buffer_length * 0.05)
        
        # Historical data boost
        historical_boost = 0.1 if len(self.historical_patterns) >= 3 else 0.05
        
        total_confidence = min(1.0, 
            transition_confidence + state_boost + completeness_boost + historical_boost
        )
        
        return total_confidence
    
    def _calculate_pattern_momentum(self, pattern_buffer: List[Dict]) -> float:
        """Calculate pattern momentum score"""
        if len(pattern_buffer) < 2:
            return 0.5  # Neutral momentum
        
        # Simple momentum based on event timing consistency
        # In real implementation, would analyze spacing patterns
        return 0.75  # Moderate positive momentum
    
    def _calculate_timing_adjustment(self, pattern_buffer: List[Dict]) -> float:
        """Calculate timing adjustment factor based on current pattern"""
        if len(pattern_buffer) < 2:
            return 1.0  # No adjustment
        
        # Analyze recent event spacing to predict acceleration/deceleration
        # For demo, use slight acceleration
        return 0.9  # 10% faster than historical average
    
    def _identify_risk_factors(
        self, 
        current_state: str, 
        pattern_buffer: List[Dict], 
        confidence: float
    ) -> List[str]:
        """Identify potential risk factors for prediction failure"""
        risks = []
        
        if confidence < 0.7:
            risks.append("low_grammatical_confidence")
        
        if len(pattern_buffer) == 0:
            risks.append("no_pattern_history")
        
        if current_state in ['START', 'EXPANSION_PHASE']:
            risks.append("early_stage_uncertainty")
        
        # Check for timing anomalies (simplified)
        if len(pattern_buffer) >= 2:
            # In real implementation, would analyze timing patterns
            pass
        
        if len(self.historical_patterns) < 5:
            risks.append("limited_historical_data")
        
        return risks
    
    def _default_prediction(self, current_state: str) -> NextEventPrediction:
        """Default prediction when no historical data available"""
        # Fallback logic based on grammatical structure
        fallback_events = {
            'START': 'expansion_high',
            'EXPANSION_PHASE': 'expansion_high',
            'EXPANSION_HIGH': 'liquidity_grab',
            'LIQUIDITY_GRAB': 'consolidation_break',
            'CONSOLIDATION_BREAK': 'cascade_trigger',
            'CASCADE_TRIGGER': 'ACCEPT'
        }
        
        predicted_event = fallback_events.get(current_state, 'unknown')
        
        return NextEventPrediction(
            predicted_event=predicted_event,
            probability=0.6,  # Conservative default
            timing_windows=[TimingWindow(
                event_type=predicted_event,
                mean_minutes=10.0,
                std_minutes=5.0,
                confidence_95_lower=3.0,
                confidence_95_upper=20.0,
                probability_peak=10.0,
                historical_count=0
            )],
            confidence_score=0.5,
            alternative_events=[],
            pattern_momentum=0.5,
            risk_factors=['no_historical_data', 'fallback_prediction']
        )
    
    def _print_prediction_summary(self, prediction: NextEventPrediction):
        """Print formatted prediction summary"""
        print(f"\n📊 NEXT EVENT PREDICTION")
        print(f"=" * 30)
        print(f"🎯 Predicted Event: {prediction.predicted_event}")
        print(f"📈 Probability: {prediction.probability:.1%}")
        print(f"🎗️ Confidence: {prediction.confidence_score:.1%}")
        print(f"⚡ Pattern Momentum: {prediction.pattern_momentum:.1%}")
        
        print(f"\n⏰ TIMING WINDOWS:")
        for window in prediction.timing_windows:
            if not window.event_type.endswith(('_50pct', '_80pct')):
                print(f"   95% Confidence: {window.confidence_95_lower:.1f} - {window.confidence_95_upper:.1f} minutes")
                print(f"   Most likely: {window.probability_peak:.1f} minutes")
                print(f"   Historical samples: {window.historical_count}")
        
        if prediction.alternative_events:
            print(f"\n🔄 Alternative Events:")
            for event, prob in prediction.alternative_events:
                print(f"   {event}: {prob:.1%}")
        
        if prediction.risk_factors:
            print(f"\n⚠️ Risk Factors:")
            for risk in prediction.risk_factors:
                print(f"   • {risk.replace('_', ' ').title()}")
    
    def get_timing_probability(
        self, 
        event_type: str, 
        minutes_ahead: float,
        prediction: NextEventPrediction
    ) -> float:
        """Calculate probability of event occurring at specific time"""
        
        # Find relevant timing window
        relevant_window = None
        for window in prediction.timing_windows:
            if window.event_type == event_type:
                relevant_window = window
                break
        
        if not relevant_window:
            return 0.0
        
        # Calculate probability using normal distribution
        probability = stats.norm.pdf(
            minutes_ahead,
            loc=relevant_window.mean_minutes,
            scale=relevant_window.std_minutes
        )
        
        # Normalize to 0-1 range (approximate)
        max_probability = stats.norm.pdf(
            relevant_window.mean_minutes,
            loc=relevant_window.mean_minutes,
            scale=relevant_window.std_minutes
        )
        
        return probability / max_probability if max_probability > 0 else 0.0

def demo_next_event_prediction():
    """Demonstrate next-event prediction system"""
    print("🔮 NEXT EVENT PREDICTION DEMO")
    print("=" * 35)
    
    # Initialize predictor
    predictor = NextEventPredictor()
    
    # Simulate grammatical states and predict next events
    test_scenarios = [
        {
            'current_state': 'START',
            'pattern_buffer': [],
            'confidence': 0.6,
            'description': 'Pattern initiation'
        },
        {
            'current_state': 'expansion_high',
            'pattern_buffer': [
                {'type': 'expansion_high', 'timestamp': '09:30:15'}
            ],
            'confidence': 0.85,
            'description': 'After expansion high detected'
        },
        {
            'current_state': 'liquidity_grab',
            'pattern_buffer': [
                {'type': 'expansion_high', 'timestamp': '09:30:15'},
                {'type': 'liquidity_grab', 'timestamp': '09:35:22'}
            ],
            'confidence': 0.9,
            'description': 'Mid-pattern momentum'
        },
        {
            'current_state': 'consolidation_break',
            'pattern_buffer': [
                {'type': 'expansion_high', 'timestamp': '09:30:15'},
                {'type': 'liquidity_grab', 'timestamp': '09:35:22'},
                {'type': 'consolidation_break', 'timestamp': '09:42:10'}
            ],
            'confidence': 0.95,
            'description': 'Pre-cascade state'
        }
    ]
    
    for i, scenario in enumerate(test_scenarios):
        print(f"\n{'='*50}")
        print(f"SCENARIO {i+1}: {scenario['description']}")
        print(f"{'='*50}")
        
        prediction = predictor.predict_next_event(
            current_state=scenario['current_state'],
            pattern_buffer=scenario['pattern_buffer'],
            confidence=scenario['confidence']
        )
        
        # Demo timing probability calculation
        if prediction.timing_windows:
            print(f"\n📈 TIMING PROBABILITY ANALYSIS:")
            for minutes in [2, 5, 8, 12, 20]:
                prob = predictor.get_timing_probability(
                    prediction.predicted_event, 
                    minutes, 
                    prediction
                )
                print(f"   {minutes:2d} minutes: {prob:.1%} probability")

if __name__ == "__main__":
    import os
    demo_next_event_prediction()