{"training_data": {"cascade_count": 42, "non_cascade_count": 59, "minority_ratio": 0.5841584158415841, "total_samples": 67, "feature_count": 7}, "metrics": {"out_of_time_accuracy": 0.7142857142857143, "train_accuracy": 0.7547169811320755, "accuracy_ci_lower": 0.5855529149338707, "accuracy_ci_upper": 0.6979635685826129, "cv_mean": 0.6417582417582418, "cv_std": 0.028102663412185525, "temporal_split": "80/20"}, "model_details": {"model_type": "XGBoost", "feature_names": ["grammatical_event_density", "pattern_completion_probability", "cross_session_influence_score", "fpfvg_interaction_strength", "session_type_encoded", "pattern_sequence_complexity", "cascade_trigger_proximity"], "confusion_matrix": [[9, 0], [4, 1]], "classification_report": {"0": {"precision": 0.6923076923076923, "recall": 1.0, "f1-score": 0.8181818181818182, "support": 9.0}, "1": {"precision": 1.0, "recall": 0.2, "f1-score": 0.3333333333333333, "support": 5.0}, "accuracy": 0.7142857142857143, "macro avg": {"precision": 0.8461538461538461, "recall": 0.6, "f1-score": 0.5757575757575758, "support": 14.0}, "weighted avg": {"precision": 0.8021978021978021, "recall": 0.7142857142857143, "f1-score": 0.645021645021645, "support": 14.0}}}, "ablations": {"temporal_features_delta": 0.0, "price_features_delta": 0.0, "cascade_features_delta": 0.0, "metadata_features_delta": 0.0}, "scaler": "StandardScaler()", "training_time": 0.3177011013031006, "notes": ["Real XGBoost training on enhanced sessions", "Trained on 67 samples with 7 features"]}