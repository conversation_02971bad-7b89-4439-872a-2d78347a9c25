"""
Test Integrated Cascade Classification System
Verify that cascade analysis is properly integrated with Three-Oracle architecture
"""

import json
import sys
import os
import logging

# Setup environment for XGBoost
os.environ['DYLD_LIBRARY_PATH'] = '/opt/local/lib/libomp:' + os.environ.get('DYLD_LIBRARY_PATH', '')
os.environ['OMP_NUM_THREADS'] = '4'

sys.path.append(os.path.dirname(__file__))
from three_oracle_architecture import create_three_oracle_system

def test_integrated_cascade_system():
    """Test the integrated cascade classification system with Three-Oracle architecture"""
    
    print("🏛️ TESTING INTEGRATED CASCADE CLASSIFICATION SYSTEM")
    print("=" * 70)
    
    # Load actual session data with rich cascade events
    print("📁 Loading PM session data with cascade events...")
    try:
        with open('/Users/<USER>/grok-claude-automation/data/sessions/level_1/NYPM_Lvl-1_2025_08_05_COMPLETE.json', 'r') as f:
            pm_data = json.load(f)
    except FileNotFoundError:
        print("❌ PM session file not found - using synthetic data")
        pm_data = create_synthetic_cascade_data()
    
    # Extract session data
    level1_data = pm_data['level1_json']
    
    # Create test input with rich cascade event data
    test_input = {
        'session_metadata': level1_data['session_metadata'],
        'micro_timing_analysis': {
            'cascade_events': [
                # Convert price movements to cascade events
                {
                    'timestamp': '13:31:00',
                    'price_level': 23208.75,
                    'event_type': 'pm_fpfvg_formation',
                    'magnitude': 0.8,
                    'context': 'primer_cascade_formation'
                },
                {
                    'timestamp': '13:46:00', 
                    'price_level': 23225.25,
                    'event_type': 'lunch_session_high_takeout',
                    'magnitude': 1.6,
                    'context': 'standard_cascade_trigger'
                },
                {
                    'timestamp': '13:58:00',
                    'price_level': 23252.0,
                    'event_type': 'session_high_reversal_point',
                    'magnitude': 2.1,
                    'context': 'major_cascade_peak'
                },
                {
                    'timestamp': '14:30:00',
                    'price_level': 23153.75,
                    'event_type': 'pm_lunch_fpfvg_rebalance',
                    'magnitude': 1.9,
                    'context': 'standard_cascade_rebalance'
                },
                {
                    'timestamp': '14:53:00',
                    'price_level': 23115.0,
                    'event_type': 'session_low_expansion_end',
                    'magnitude': 2.3,
                    'context': 'major_cascade_completion'
                }
            ]
        },
        'price_data': {
            'session_high': 23252.0,
            'session_low': 23115.0,
            'session_range': 137.0,
            'session_character': 'high_energy_expansion_cascade_sequence'
        }
    }
    
    print(f"📊 Test Input Summary:")
    print(f"   Session: {test_input['session_metadata']['session_type']}")
    print(f"   Duration: {test_input['session_metadata']['session_duration']} minutes")
    print(f"   Cascade Events: {len(test_input['micro_timing_analysis']['cascade_events'])}")
    print(f"   Price Range: {test_input['price_data']['session_range']} points")
    
    # Initialize Three-Oracle system with cascade analysis enabled
    print(f"\n🚀 INITIALIZING ENHANCED THREE-ORACLE SYSTEM...")
    three_oracle = create_three_oracle_system({
        'log_level': 'INFO',
        'enable_enhancement': True,
        'enable_cascade_analysis': True,
        'enable_vqe_optimization': True
    })
    
    # Generate prediction with cascade analysis
    print(f"\n🎯 GENERATING PREDICTION WITH CASCADE ANALYSIS...")
    
    try:
        result = three_oracle.predict_cascade_timing(test_input, optimize_parameters=True)
        
        # Extract results
        if isinstance(result, dict) and 'timing_decision' in result:
            # New format with separate timing and energy results
            timing_decision = result['timing_decision']
            energy_results = result.get('energy_results')
        else:
            # Legacy format
            timing_decision = result
            energy_results = None
        
        # Display timing results
        print(f"\n📊 TIMING PREDICTION RESULTS:")
        print("=" * 50)
        print(f"🕐 Predicted Time: {timing_decision.final_prediction:.1f} minutes")
        print(f"⚖️ Oracle Choice: {timing_decision.chosen_oracle.upper()}")
        print(f"📈 Confidence: {timing_decision.prediction_confidence:.1%}")
        print(f"🔍 Echo Strength: {timing_decision.echo_strength:.1f} minutes")
        print(f"🏥 System Health: {timing_decision.system_health['status']}")
        print(f"🧠 Reasoning: {timing_decision.arbiter_reasoning}")
        
        # Display cascade analysis results (if available)
        cascade_analysis_found = False
        
        # Check Virgin Oracle for cascade analysis
        if hasattr(timing_decision, 'virgin_prediction_details') and 'cascade_analysis' in timing_decision.virgin_prediction_details:
            virgin_analysis = timing_decision.virgin_prediction_details['cascade_analysis']
            print(f"\n🧊 VIRGIN ORACLE CASCADE ANALYSIS:")
            display_cascade_analysis(virgin_analysis, "Virgin")
            cascade_analysis_found = True
        
        # Check Contaminated Oracle for cascade analysis
        if hasattr(timing_decision, 'contaminated_prediction_details') and 'cascade_analysis' in timing_decision.contaminated_prediction_details:
            contaminated_analysis = timing_decision.contaminated_prediction_details['cascade_analysis']
            print(f"\n🧬 CONTAMINATED ORACLE CASCADE ANALYSIS:")
            display_cascade_analysis(contaminated_analysis, "Contaminated")
            cascade_analysis_found = True
        
        # Try to access cascade analysis from other sources
        if not cascade_analysis_found:
            print(f"\n🔍 SEARCHING FOR CASCADE ANALYSIS...")
            print(f"   Available attributes: {[attr for attr in dir(timing_decision) if not attr.startswith('_')]}")
            
        # Energy validation results
        if energy_results:
            print(f"\n🔋 ENERGY VALIDATION RESULTS:")
            print(f"   Energy Predictions: {len(energy_results['predictions'])} checkpoints")
            print(f"   Predictions: {energy_results['predictions']}")
            print(f"   Divergence Detected: {energy_results['divergence_detected']}")
        
        # System integration assessment
        print(f"\n🔗 INTEGRATION ASSESSMENT:")
        integration_success = {
            'three_oracle_active': True,
            'timing_prediction_generated': hasattr(timing_decision, 'final_prediction'),
            'cascade_analysis_integrated': cascade_analysis_found,
            'energy_validation_active': energy_results is not None,
            'system_health_monitored': hasattr(timing_decision, 'system_health')
        }
        
        for component, status in integration_success.items():
            status_icon = "✅" if status else "❌"
            print(f"   {status_icon} {component.replace('_', ' ').title()}")
        
        # Overall success rate
        success_rate = sum(integration_success.values()) / len(integration_success)
        print(f"\n🎯 INTEGRATION SUCCESS RATE: {success_rate:.1%}")
        
        if success_rate >= 0.8:
            print(f"🎉 SUCCESS: Cascade classification system successfully integrated!")
        elif success_rate >= 0.6:
            print(f"⚠️ PARTIAL: Integration mostly successful, minor issues detected")
        else:
            print(f"❌ FAILURE: Integration issues require attention")
        
        return {
            'success': success_rate >= 0.6,
            'timing_prediction': timing_decision.final_prediction,
            'oracle_choice': timing_decision.chosen_oracle,
            'cascade_analysis_found': cascade_analysis_found,
            'integration_components': integration_success
        }
        
    except Exception as e:
        print(f"❌ INTEGRATION TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

def display_cascade_analysis(analysis, oracle_name):
    """Display cascade analysis results"""
    
    if not analysis or analysis.get('no_events'):
        print(f"   No cascade events analyzed")
        return
    
    print(f"   Total Events: {analysis.get('total_events', 0)}")
    print(f"   Cascade Types: {', '.join(analysis.get('cascade_types_detected', []))}")
    
    # Type distribution
    type_dist = analysis.get('type_distribution', {})
    if type_dist:
        print(f"   Type Distribution:")
        for cascade_type, count in type_dist.items():
            print(f"      {cascade_type}: {count}")
    
    # Dominant cascade type
    dominant = analysis.get('dominant_cascade_type')
    if dominant:
        percentage = analysis.get('dominant_type_percentage', 0) * 100
        print(f"   Dominant Type: {dominant} ({percentage:.1f}%)")
    
    # Sequences detected
    sequences = analysis.get('sequences_detected', 0)
    if sequences > 0:
        patterns = analysis.get('sequence_patterns', [])
        print(f"   Sequences: {sequences} ({', '.join(patterns)})")

def create_synthetic_cascade_data():
    """Create synthetic data for testing if real data not available"""
    
    return {
        'level1_json': {
            'session_metadata': {
                'session_type': 'ny_pm',
                'session_duration': 159,
                'session_start': '13:30:00',
                'session_end': '16:09:00'
            }
        }
    }

if __name__ == "__main__":
    # Configure logging for cleaner output
    logging.basicConfig(level=logging.WARNING)
    
    results = test_integrated_cascade_system()
    
    print("\n" + "=" * 70)
    print("🎯 INTEGRATION TEST SUMMARY:")
    
    if results.get('success'):
        print(f"   ✅ Status: SUCCESS")
        print(f"   🎯 Timing Prediction: {results.get('timing_prediction', 'N/A'):.1f} min")
        print(f"   ⚖️ Oracle Choice: {results.get('oracle_choice', 'N/A').upper()}")
        print(f"   🔍 Cascade Analysis: {'✅ Found' if results.get('cascade_analysis_found') else '❌ Missing'}")
        
        # Component status
        components = results.get('integration_components', {})
        successful_components = sum(components.values())
        total_components = len(components)
        print(f"   🔗 Components: {successful_components}/{total_components} integrated")
        
    else:
        print(f"   ❌ Status: FAILED")
        if 'error' in results:
            print(f"   Error: {results['error']}")
    
    print(f"\n🚀 Cascade classification system integration {'complete' if results.get('success') else 'requires fixes'}!")