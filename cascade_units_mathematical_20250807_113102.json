{
  "extraction_metadata": {
    "timestamp": "2025-08-07T11:31:02.610008",
    "source": "project_oracle_91.1%_baseline",
    "methodology": "cascade_as_mathematical_primitive",
    "total_units": 4
  },
  "cascade_units": [
    {
      "unit_id": "volume_spike_reversal",
      "trigger_vector": [
        1.0,
        0.9,
        0.8,
        0.0,
        0.0,
        0.0,
        0.0,
        0.0,
        0.0,
        0.0,
        0.0,
        0.0
      ],
      "propagation_matrix": [
        [
          0.0,
          0.3333333333333333,
          0.0
        ],
        [
          0.0,
          0.0,
          0.3333333333333333
        ],
        [
          0.0,
          0.0,
          0.0
        ]
      ],
      "resolution_state": [
        1.0,
        0.0,
        0.0
      ],
      "frequency": 0.32,
      "signature_pattern": "Volume spike \u2192 Price reversal \u2192 Momentum shift",
      "mathematical_form": "C = \u03bb\u2081(1.57) \u2297 P(0.00) \u2192 R(1.00)"
    },
    {
      "unit_id": "liquidity_vacuum_cascade",
      "trigger_vector": [
        0.0,
        0.0,
        0.0,
        1.0,
        0.9,
        0.8,
        0.0,
        0.0,
        0.0,
        0.0,
        0.0,
        0.0
      ],
      "propagation_matrix": [
        [
          0.0,
          0.3333333333333333,
          0.0
        ],
        [
          0.0,
          0.0,
          0.0
        ],
        [
          0.3333333333333333,
          0.0,
          0.0
        ]
      ],
      "resolution_state": [
        0.0,
        1.0,
        0.0
      ],
      "frequency": 0.28,
      "signature_pattern": "Liquidity vacuum \u2192 Stop run \u2192 FPFVG redelivery",
      "mathematical_form": "C = \u03bb\u2081(1.57) \u2297 P(0.00) \u2192 R(1.00)"
    },
    {
      "unit_id": "session_boundary_cascade",
      "trigger_vector": [
        0.0,
        0.0,
        0.0,
        0.0,
        0.0,
        0.0,
        1.0,
        0.9,
        0.8,
        0.0,
        0.0,
        0.0
      ],
      "propagation_matrix": [
        [
          0.0,
          0.0,
          0.0
        ],
        [
          0.0,
          0.0,
          0.0
        ],
        [
          0.3333333333333333,
          0.0,
          0.3333333333333333
        ]
      ],
      "resolution_state": [
        0.0,
        0.0,
        1.0
      ],
      "frequency": 0.25,
      "signature_pattern": "Session boundary \u2192 HTF activation \u2192 Cascade execution",
      "mathematical_form": "C = \u03bb\u2081(1.57) \u2297 P(0.33) \u2192 R(1.00)"
    },
    {
      "unit_id": "crystallization_cascade",
      "trigger_vector": [
        0.0,
        0.0,
        0.0,
        0.0,
        0.0,
        0.0,
        0.0,
        0.0,
        0.0,
        1.0,
        0.9,
        0.8
      ],
      "propagation_matrix": [
        [
          0.0,
          0.3333333333333333,
          0.0
        ],
        [
          0.0,
          0.0,
          0.3333333333333333
        ],
        [
          0.0,
          0.0,
          0.0
        ]
      ],
      "resolution_state": [
        0.5,
        0.5,
        1.0
      ],
      "frequency": 0.15,
      "signature_pattern": "Fisher spike \u2192 Deterministic regime \u2192 Immediate execution",
      "mathematical_form": "C = \u03bb\u2081(1.57) \u2297 P(0.00) \u2192 R(1.22)"
    }
  ],
  "operator_system": {
    "cascade_operators": {
      "\u0108_volume_spike_reversal": {
        "trigger_eigenvalues": 