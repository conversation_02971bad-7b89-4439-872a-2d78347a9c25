{
  "timestamp": "2025-08-09T02:08:18.842049",
  "fisher_information": {
    "status": "operational",
    "sample_scores": [
      500.0,
      500.0,
      500.0
    ],
    "average_score": 500.0,
    "score_range": [
      500.0,
      500.0
    ],
    "threshold_analysis": {
      "current_threshold": 200.0,
      "scores_above_threshold": 3,
      "deterministic_mode_triggered": true
    },
    "calibration_needed": false,
    "recommendation": "Fisher Information calibration is optimal"
  },
  "grammar_bridge": {
    "status": "operational",
    "total_events": 799,
    "unique_event_types": 82,
    "event_type_distribution": {
      "expansion_high": 128,
      "retracement_low": 91,
      "liquidity_redelivery": 89,
      "expansion_low": 52,
      "retracement_high": 51,
      "liquidity_rebalance": 43,
      "retracement": 32,
      "fpfvg_formation": 29,
      "retracement_start": 26,
      "liquidity_sweep": 26
    },
    "pattern_recognition_quality": {
      "average_completion": 1.0,
      "completion_range": [
        1.0,
        1.0
      ],
      "unique_patterns_detected": 2,
      "sample_results": [
        {
          "sample_size": 50,
          "completion_probability": 1.0,
          "pattern_type": "fpfvg_formation_liquidity_redelivery",
          "high_value_events": 8
        },
        {
          "sample_size": 100,
          "completion_probability": 1.0,
          "pattern_type": "expansion_high_liquidity_sweep",
          "high_value_events": 29
        },
        {
          "sample_size": 200,
          "completion_probability": 1.0,
          "pattern_type": "expansion_high_liquidity_sweep",
          "high_value_events": 49
        },
        {
          "sample_size": 400,
          "completion_probability": 1.0,
          "pattern_type": "expansion_high_liquidity_sweep",
          "high_value_events": 80
        }
      ]
    },
    "training_needed": true,
    "recommendation": "MEDIUM: Limited pattern diversity - expand training patterns"
  },
  "three_oracle_consensus": {
    "status": "operational",
    "total_predictions": 15,
    "oracle_distribution": {
      "fallback": 15
    },
    "confidence_stats": {
      "average": 0.88,
      "min": 0.85,
      "max": 0.9099999999999999,
      "std_dev": 0.024494897427831754
    },
    "consensus_quality": {
      "dominant_oracle": "fallback",
      "oracle_diversity": 1,
      "confidence_consistency": 