"""OpenMP path fix for MacPorts - import this before XGBoost"""
import os
import ctypes

# Fix MacPorts libomp path for XGBoost
os.environ['DYLD_LIBRARY_PATH'] = '/opt/local/lib/libomp:' + os.environ.get('DYLD_LIBRARY_PATH', '')
os.environ['OMP_NUM_THREADS'] = '4'

# Pre-load OpenMP library
try:
    libomp = ctypes.CDLL('/opt/local/lib/libomp/libomp.dylib')
    print("✓ OpenMP loaded for XGBoost parallel processing")
except OSError:
    try:
        libiomp5 = ctypes.CDLL('/opt/local/lib/libomp/libiomp5.dylib')
        print("✓ OpenMP (Intel) loaded for XGBoost")
    except:
        print("⚠️ OpenMP not loaded - XGBoost will run single-threaded")
