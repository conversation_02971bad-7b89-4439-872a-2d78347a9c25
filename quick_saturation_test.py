#!/usr/bin/env python3
"""
Quick Information Saturation Test
=================================

Simplified version to test the core hypothesis: Does n=45 achieve 90%+ accuracy?
Uses direct sklearn models to avoid trainer dependency issues.
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score
from sklearn.preprocessing import StandardScaler
import time

from xgboost_real_trainer import XGBoostRealTrainer

def quick_saturation_test():
    """Quick test of information saturation hypothesis"""
    
    print("🔬 QUICK INFORMATION SATURATION TEST")
    print("=" * 40)
    print("Hypothesis: n=45 sessions achieve 90%+ accuracy")
    print("Method: Direct sklearn comparison")
    print()
    
    # Load data
    trainer = XGBoostRealTrainer()
    sessions = trainer.load_enhanced_sessions()
    examples = trainer.extract_training_examples(sessions)
    X_full, y_full = trainer.prepare_training_data(examples)
    
    print(f"📊 Dataset loaded:")
    print(f"   Total sessions: {len(examples)}")
    print(f"   Cascade rate: {np.mean(y_full):.1%}")
    print(f"   Non-cascades: {np.sum(y_full == 0)}")
    print(f"   Cascades: {np.sum(y_full == 1)}")
    
    # Test different training set sizes
    test_sizes = [20, 30, 40, 45, 50, len(examples)]
    results = []
    
    for n in test_sizes:
        print(f"\n🎯 Testing n={n} sessions...")
        
        # Strategic sampling: preserve ALL non-cascades
        non_cascade_indices = np.where(y_full == 0)[0]
        cascade_indices = np.where(y_full == 1)[0]
        
        # Include all non-cascades
        selected_indices = list(non_cascade_indices)
        
        # Add cascades to reach target size
        n_cascades_needed = n - len(non_cascade_indices)
        if n_cascades_needed > 0:
            np.random.seed(42)  # Reproducible
            selected_cascades = np.random.choice(
                cascade_indices, 
                size=min(n_cascades_needed, len(cascade_indices)), 
                replace=False
            )
            selected_indices.extend(selected_cascades)
        
        # Get subset
        X_subset = X_full[selected_indices]
        y_subset = y_full[selected_indices]
        
        print(f"   Subset: {len(y_subset)} sessions")
        print(f"   Non-cascades: {np.sum(y_subset == 0)} (100% preserved)")
        print(f"   Cascades: {np.sum(y_subset == 1)}")
        
        # Train model
        start_time = time.time()
        
        # Conservative model to prevent overfitting
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X_subset)
        
        model = GradientBoostingClassifier(
            n_estimators=50,        # Conservative
            max_depth=3,            # Shallow
            learning_rate=0.05,     # Slow
            random_state=42
        )
        
        # Cross-validation to get robust accuracy estimate
        cv_scores = cross_val_score(model, X_scaled, y_subset, cv=5, scoring='accuracy')
        
        training_time = time.time() - start_time
        
        # Results
        mean_accuracy = np.mean(cv_scores)
        std_accuracy = np.std(cv_scores)
        
        result = {
            'n_sessions': n,
            'accuracy': mean_accuracy,
            'std': std_accuracy,
            'training_time': training_time,
            'non_cascade_coverage': np.sum(y_subset == 0) / np.sum(y_full == 0)
        }
        results.append(result)
        
        print(f"   CV Accuracy: {mean_accuracy:.1%} ± {std_accuracy:.1%}")
        print(f"   Training time: {training_time:.1f}s")
        print(f"   Target met: {'✅' if mean_accuracy >= 0.90 else '❌'} (≥90%)")
    
    # Analysis
    print(f"\n📈 LEARNING CURVE ANALYSIS")
    print("=" * 30)
    
    for i, result in enumerate(results):
        status = "✅" if result['accuracy'] >= 0.90 else "❌"
        print(f"{status} n={result['n_sessions']:2d}: "
              f"{result['accuracy']:.1%} ± {result['std']:.1%} "
              f"({result['training_time']:.1f}s)")
        
        if i > 0:
            prev_acc = results[i-1]['accuracy'] 
            marginal_gain = result['accuracy'] - prev_acc
            sessions_added = result['n_sessions'] - results[i-1]['n_sessions']
            gain_per_session = marginal_gain / sessions_added
            
            if abs(gain_per_session) < 0.001:  # <0.1% per session
                print(f"     📊 Marginal gain: {gain_per_session:.4f} per session (SATURATED)")
            else:
                print(f"     📊 Marginal gain: {gain_per_session:.4f} per session")
    
    # Hypothesis validation
    print(f"\n🎯 HYPOTHESIS VALIDATION")
    print("=" * 25)
    
    # Find if any subset achieves ≥90%
    successful_subsets = [r for r in results if r['accuracy'] >= 0.90]
    
    if successful_subsets:
        optimal = min(successful_subsets, key=lambda x: x['n_sessions'])
        print(f"✅ HYPOTHESIS VALIDATED")
        print(f"   Minimum viable set: {optimal['n_sessions']} sessions")
        print(f"   Achieved accuracy: {optimal['accuracy']:.1%} ± {optimal['std']:.1%}")
        
        # Calculate savings vs full dataset
        full_result = results[-1]  # Last entry is full dataset
        computational_savings = 1.0 - (optimal['training_time'] / full_result['training_time'])
        accuracy_preservation = optimal['accuracy'] / full_result['accuracy']
        
        print(f"   Computational savings: {computational_savings:.1%}")
        print(f"   Accuracy preservation: {accuracy_preservation:.1%}")
        
        # Mathematical justification
        effective_sample_size = 2 * np.sqrt(np.sum(y_full == 1) * np.sum(y_full == 0))
        print(f"   Effective sample size: {effective_sample_size:.1f}")
        print(f"   Theory prediction: Information plateaus at n ≈ {10 * np.sum(y_full == 0)}")
        
    else:
        print(f"❌ HYPOTHESIS REJECTED")
        print(f"   No subset achieved ≥90% accuracy")
        print(f"   Best subset: {max(results, key=lambda x: x['accuracy'])['n_sessions']} sessions")
        print(f"   Best accuracy: {max(results, key=lambda x: x['accuracy'])['accuracy']:.1%}")
        print(f"   Recommendation: Use full dataset")
    
    return results

if __name__ == "__main__":
    results = quick_saturation_test()