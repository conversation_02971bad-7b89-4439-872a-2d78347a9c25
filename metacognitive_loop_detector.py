"""Metacognitive Loop Detection System

Monitors echo strength patterns across multiple predictions to detect metacognitive loops
where Oracle predictions become self-referential or exhibit systematic bias.

Key Features:
- Tracks echo strength history across sessions
- Detects sustained echo strength > 20 patterns 
- Implements adaptive countermeasures
- Real-time loop strength monitoring
"""

import numpy as np
import json
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
from collections import deque

@dataclass
class LoopDetectionResult:
    """Result of metacognitive loop detection"""
    loop_detected: bool
    loop_strength: float
    loop_duration_sessions: int
    pattern_type: str  # 'sustained_echo', 'oscillating', 'escalating'
    confidence: float
    recommended_action: str
    historical_echo_pattern: List[float]

@dataclass
class MetacognitivePredictionEntry:
    """Single prediction entry for metacognitive tracking"""
    timestamp: datetime
    session_id: str
    echo_strength: float
    chosen_oracle: str
    virgin_prediction: float
    contaminated_prediction: float
    final_prediction: float
    confidence: float

class MetacognitiveLoopDetector:
    """
    Advanced metacognitive loop detection system
    
    Monitors Three-Oracle system for metacognitive loops where:
    1. Echo strength > 20 sustained across multiple sessions
    2. Systematic bias in Oracle selection patterns
    3. Self-referential prediction spirals
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize metacognitive loop detector"""
        
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Detection parameters
        self.echo_threshold_high = self.config.get('echo_threshold_high', 20.0)
        self.echo_threshold_critical = self.config.get('echo_threshold_critical', 40.0)
        self.loop_detection_window = self.config.get('loop_detection_window', 10)  # sessions
        self.sustained_threshold = self.config.get('sustained_threshold', 0.6)  # 60% of sessions
        
        # Historical tracking
        self.prediction_history = deque(maxlen=100)  # Last 100 predictions
        self.loop_detection_history = []
        
        # Loop state tracking
        self.current_loop_state = {
            'active_loop': False,
            'loop_start_time': None,
            'loop_sessions': 0,
            'max_echo_in_loop': 0.0
        }
        
        # Performance metrics
        self.detection_stats = {
            'total_predictions_monitored': 0,
            'loops_detected': 0,
            'false_alarms': 0,
            'average_echo_strength': 0.0,
            'high_echo_sessions': 0
        }
        
        self.logger.info("🧠 METACOGNITIVE LOOP DETECTOR: Initialized")
        self.logger.info(f"   Echo Threshold High: {self.echo_threshold_high}")
        self.logger.info(f"   Echo Threshold Critical: {self.echo_threshold_critical}")
        self.logger.info(f"   Detection Window: {self.loop_detection_window} sessions")
        self.logger.info(f"   Sustained Threshold: {self.sustained_threshold:.1%}")
    
    def monitor_prediction(self, three_oracle_decision, session_metadata: Dict) -> LoopDetectionResult:
        """
        Monitor a single Three-Oracle prediction for metacognitive loop patterns
        
        Args:
            three_oracle_decision: ThreeOracleDecision result
            session_metadata: Session context information
            
        Returns:
            LoopDetectionResult with detection analysis
        """
        
        self.logger.info("🔍 METACOGNITIVE MONITORING: Analyzing prediction...")
        
        # Create prediction entry
        prediction_entry = MetacognitivePredictionEntry(
            timestamp=datetime.now(),
            session_id=session_metadata.get('session_id', 'unknown'),
            echo_strength=three_oracle_decision.echo_strength,
            chosen_oracle=three_oracle_decision.chosen_oracle,
            virgin_prediction=three_oracle_decision.virgin_prediction,
            contaminated_prediction=three_oracle_decision.contaminated_prediction,
            final_prediction=three_oracle_decision.final_prediction,
            confidence=three_oracle_decision.prediction_confidence
        )
        
        # Add to history
        self.prediction_history.append(prediction_entry)
        self.detection_stats['total_predictions_monitored'] += 1
        
        # Update echo statistics
        self._update_echo_statistics(prediction_entry.echo_strength)
        
        # Perform loop detection analysis
        loop_result = self._detect_metacognitive_loop(prediction_entry)
        
        # Update loop state
        self._update_loop_state(loop_result)
        
        # Log detection results
        self._log_detection_results(loop_result, prediction_entry)
        
        return loop_result
    
    def _detect_metacognitive_loop(self, current_prediction: MetacognitivePredictionEntry) -> LoopDetectionResult:
        """Core metacognitive loop detection logic"""
        
        if len(self.prediction_history) < self.loop_detection_window:
            return LoopDetectionResult(
                loop_detected=False,
                loop_strength=0.0,
                loop_duration_sessions=0,
                pattern_type='insufficient_data',
                confidence=0.0,
                recommended_action='continue_monitoring',
                historical_echo_pattern=[]
            )
        
        # Analyze recent echo pattern
        recent_predictions = list(self.prediction_history)[-self.loop_detection_window:]
        echo_pattern = [p.echo_strength for p in recent_predictions]
        
        # Detection criteria
        loop_detected = False
        loop_strength = 0.0
        pattern_type = 'normal'
        confidence = 0.0
        recommended_action = 'continue_monitoring'
        
        # 1. Sustained High Echo Detection (primary criterion)
        high_echo_count = sum(1 for echo in echo_pattern if echo > self.echo_threshold_high)
        sustained_ratio = high_echo_count / len(echo_pattern)
        
        if sustained_ratio >= self.sustained_threshold:
            loop_detected = True
            loop_strength = np.mean([echo for echo in echo_pattern if echo > self.echo_threshold_high])
            pattern_type = 'sustained_echo'
            confidence = min(0.95, sustained_ratio)
            
            if loop_strength > self.echo_threshold_critical:
                recommended_action = 'emergency_reset'
            else:
                recommended_action = 'virgin_oracle_priority'
        
        # 2. Escalating Pattern Detection
        elif len(echo_pattern) >= 5:
            # Check for escalating echo strength
            recent_trend = np.polyfit(range(len(echo_pattern)), echo_pattern, 1)[0]
            if recent_trend > 2.0 and echo_pattern[-1] > self.echo_threshold_high:
                loop_detected = True
                loop_strength = echo_pattern[-1]
                pattern_type = 'escalating'
                confidence = min(0.90, recent_trend / 10.0)
                recommended_action = 'intervention_required'
        
        # 3. Oscillating Pattern Detection
        elif len(echo_pattern) >= 6:
            # Check for high-frequency oscillations above threshold
            oscillations = 0
            for i in range(1, len(echo_pattern)):
                if ((echo_pattern[i] > self.echo_threshold_high and echo_pattern[i-1] < self.echo_threshold_high) or
                    (echo_pattern[i] < self.echo_threshold_high and echo_pattern[i-1] > self.echo_threshold_high)):
                    oscillations += 1
            
            if oscillations >= 3 and max(echo_pattern) > self.echo_threshold_high:
                loop_detected = True
                loop_strength = np.std(echo_pattern)
                pattern_type = 'oscillating'
                confidence = min(0.85, oscillations / len(echo_pattern))
                recommended_action = 'stabilization_required'
        
        # Calculate loop duration if detected
        loop_duration = 0
        if loop_detected:
            # Count consecutive sessions with high echo
            for i in range(len(echo_pattern) - 1, -1, -1):
                if echo_pattern[i] > self.echo_threshold_high:
                    loop_duration += 1
                else:
                    break
        
        return LoopDetectionResult(
            loop_detected=loop_detected,
            loop_strength=loop_strength,
            loop_duration_sessions=loop_duration,
            pattern_type=pattern_type,
            confidence=confidence,
            recommended_action=recommended_action,
            historical_echo_pattern=echo_pattern.copy()
        )
    
    def _update_echo_statistics(self, echo_strength: float) -> None:
        """Update running echo strength statistics"""
        
        # Update average
        current_avg = self.detection_stats['average_echo_strength']
        total_predictions = self.detection_stats['total_predictions_monitored']
        
        self.detection_stats['average_echo_strength'] = (
            (current_avg * (total_predictions - 1) + echo_strength) / total_predictions
        )
        
        # Track high echo sessions
        if echo_strength > self.echo_threshold_high:
            self.detection_stats['high_echo_sessions'] += 1
    
    def _update_loop_state(self, loop_result: LoopDetectionResult) -> None:
        """Update internal loop state tracking"""
        
        if loop_result.loop_detected and not self.current_loop_state['active_loop']:
            # Starting new loop
            self.current_loop_state['active_loop'] = True
            self.current_loop_state['loop_start_time'] = datetime.now()
            self.current_loop_state['loop_sessions'] = loop_result.loop_duration_sessions
            self.current_loop_state['max_echo_in_loop'] = loop_result.loop_strength
            self.detection_stats['loops_detected'] += 1
            
        elif loop_result.loop_detected and self.current_loop_state['active_loop']:
            # Continuing existing loop
            self.current_loop_state['loop_sessions'] = loop_result.loop_duration_sessions
            self.current_loop_state['max_echo_in_loop'] = max(
                self.current_loop_state['max_echo_in_loop'], 
                loop_result.loop_strength
            )
            
        elif not loop_result.loop_detected and self.current_loop_state['active_loop']:
            # Loop ended
            self.current_loop_state['active_loop'] = False
            self.current_loop_state['loop_start_time'] = None
            self.current_loop_state['loop_sessions'] = 0
            self.current_loop_state['max_echo_in_loop'] = 0.0
    
    def _log_detection_results(self, loop_result: LoopDetectionResult, 
                             prediction_entry: MetacognitivePredictionEntry) -> None:
        """Log detection results with appropriate severity"""
        
        echo_strength = prediction_entry.echo_strength
        
        if loop_result.loop_detected:
            if loop_result.loop_strength > self.echo_threshold_critical:
                self.logger.critical("🚨🧠 CRITICAL METACOGNITIVE LOOP DETECTED 🧠🚨")
                self.logger.critical(f"   Pattern: {loop_result.pattern_type.upper()}")
                self.logger.critical(f"   Loop Strength: {loop_result.loop_strength:.1f}")
                self.logger.critical(f"   Duration: {loop_result.loop_duration_sessions} sessions")
                self.logger.critical(f"   Confidence: {loop_result.confidence:.2f}")
                self.logger.critical(f"   Action Required: {loop_result.recommended_action.upper()}")
            else:
                self.logger.warning("⚠️🧠 METACOGNITIVE LOOP DETECTED")
                self.logger.warning(f"   Pattern: {loop_result.pattern_type}")
                self.logger.warning(f"   Loop Strength: {loop_result.loop_strength:.1f}")
                self.logger.warning(f"   Duration: {loop_result.loop_duration_sessions} sessions")
                self.logger.warning(f"   Recommended Action: {loop_result.recommended_action}")
        else:
            if echo_strength > self.echo_threshold_high:
                self.logger.info(f"🔍 High Echo Detected: {echo_strength:.1f} (monitoring)")
            else:
                self.logger.debug(f"🔍 Normal Echo Level: {echo_strength:.1f}")
    
    def get_detection_statistics(self) -> Dict[str, Any]:
        """Get comprehensive detection statistics"""
        
        return {
            'detection_stats': self.detection_stats.copy(),
            'current_loop_state': self.current_loop_state.copy(),
            'history_length': len(self.prediction_history),
            'recent_echo_pattern': [p.echo_strength for p in list(self.prediction_history)[-10:]],
            'configuration': {
                'echo_threshold_high': self.echo_threshold_high,
                'echo_threshold_critical': self.echo_threshold_critical,
                'detection_window': self.loop_detection_window,
                'sustained_threshold': self.sustained_threshold
            }
        }
    
    def save_detection_report(self, filepath: Optional[str] = None) -> str:
        """Save comprehensive detection report"""
        
        if filepath is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filepath = f"metacognitive_detection_report_{timestamp}.json"
        
        report_data = {
            'report_metadata': {
                'timestamp': datetime.now().isoformat(),
                'detector_version': '1.0',
                'total_predictions_analyzed': len(self.prediction_history)
            },
            'detection_statistics': self.get_detection_statistics(),
            'recent_predictions': [
                {
                    'timestamp': entry.timestamp.isoformat(),
                    'session_id': entry.session_id,
                    'echo_strength': entry.echo_strength,
                    'chosen_oracle': entry.chosen_oracle,
                    'predictions': {
                        'virgin': entry.virgin_prediction,
                        'contaminated': entry.contaminated_prediction,
                        'final': entry.final_prediction
                    },
                    'confidence': entry.confidence
                }
                for entry in list(self.prediction_history)[-20:]  # Last 20 entries
            ],
            'loop_detection_history': self.loop_detection_history
        }
        
        output_path = Path(filepath)
        with open(output_path, 'w') as f:
            json.dump(report_data, f, indent=2)
        
        self.logger.info(f"💾 Detection report saved: {output_path}")
        return str(output_path)


def create_metacognitive_loop_detector(config: Optional[Dict] = None) -> MetacognitiveLoopDetector:
    """
    Factory function to create production-ready metacognitive loop detector
    
    Args:
        config: Optional configuration overrides
        
    Returns:
        MetacognitiveLoopDetector: Configured detector ready for monitoring
    """
    
    # Production configuration
    detector_config = {
        'echo_threshold_high': 20.0,      # Primary threshold from user requirement  
        'echo_threshold_critical': 40.0,  # Critical intervention threshold
        'loop_detection_window': 10,      # Sessions to analyze for patterns
        'sustained_threshold': 0.6        # 60% of sessions above threshold = loop
    }
    
    if config:
        detector_config.update(config)
    
    return MetacognitiveLoopDetector(detector_config)


if __name__ == "__main__":
    """Test metacognitive loop detection system"""
    
    print("🧠 METACOGNITIVE LOOP DETECTOR: Testing & Validation")
    print("=" * 60)
    
    # Create detector
    detector = create_metacognitive_loop_detector()
    
    # Simulate prediction sequence with metacognitive loop
    print("\n🎯 Testing Loop Detection:")
    
    # Create mock three-oracle decisions
    from dataclasses import dataclass
    
    @dataclass
    class MockThreeOracleDecision:
        echo_strength: float
        chosen_oracle: str
        virgin_prediction: float
        contaminated_prediction: float
        final_prediction: float
        prediction_confidence: float
        
    # Normal predictions (should not trigger)
    normal_predictions = [5.2, 8.1, 12.3, 7.8, 9.4]
    print(f"   Normal Echo Pattern: {normal_predictions}")
    
    for i, echo in enumerate(normal_predictions):
        decision = MockThreeOracleDecision(
            echo_strength=echo,
            chosen_oracle='contaminated',
            virgin_prediction=45.0 + i,
            contaminated_prediction=43.0 + i, 
            final_prediction=44.0 + i,
            prediction_confidence=0.85
        )
        result = detector.monitor_prediction(decision, {'session_id': f'normal_{i}'})
        
    print(f"   Loop Detected: {result.loop_detected}")
    
    # High echo predictions (should trigger loop detection)
    print(f"\n🚨 Testing High Echo Pattern:")
    high_echo_predictions = [25.3, 28.1, 32.4, 27.9, 31.2, 29.8, 33.1]
    print(f"   High Echo Pattern: {high_echo_predictions}")
    
    for i, echo in enumerate(high_echo_predictions):
        decision = MockThreeOracleDecision(
            echo_strength=echo,
            chosen_oracle='virgin',  
            virgin_prediction=50.0 + i,
            contaminated_prediction=48.0 + i,
            final_prediction=50.0 + i,  # Using virgin
            prediction_confidence=0.75
        )
        result = detector.monitor_prediction(decision, {'session_id': f'high_echo_{i}'})
    
    print(f"   Loop Detected: {result.loop_detected}")
    print(f"   Pattern Type: {result.pattern_type}")
    print(f"   Loop Strength: {result.loop_strength:.1f}")
    print(f"   Recommended Action: {result.recommended_action}")
    
    # Get statistics
    stats = detector.get_detection_statistics()
    print(f"\n📊 Detection Statistics:")
    print(f"   Total Predictions: {stats['detection_stats']['total_predictions_monitored']}")
    print(f"   Loops Detected: {stats['detection_stats']['loops_detected']}")
    print(f"   High Echo Sessions: {stats['detection_stats']['high_echo_sessions']}")
    print(f"   Average Echo: {stats['detection_stats']['average_echo_strength']:.1f}")
    
    # Save report
    report_file = detector.save_detection_report()
    print(f"\n💾 Detection report saved: {report_file}")
    
    print("\n✅ Metacognitive Loop Detection validation complete")
    print("🔗 Ready for integration with Three-Oracle system")