#!/usr/bin/env python3
"""
NYAM Prediction Protocol - Time-Critical Execution
=================================================

Implements the 14-minute critical window protocol for NY AM cascade prediction
using the correct session times and grammar-integrated prediction system.

CRITICAL TIMING:
- 09:15 ET: Protocol activation (14 minutes to market open)
- 09:20 ET: Grammar bridge completion (9 minutes remaining)
- 09:25 ET: Prediction generation (5 minutes remaining)
- 09:29 ET: Final prediction ready (1 minute buffer)
- 09:30 ET: NY AM market open

CORRECT SESSION SEQUENCE:
- ASIA: 19:00-23:59 ET (previous day)
- LONDON: 02:00-04:59 ET (current day)
- PREMARKET: 07:00-09:29 ET (current day)
- NY AM: 09:30-11:59 ET (target session)
"""

import json
import logging
import subprocess
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger(__name__)

class NYAMPredictionProtocol:
    """
    Time-critical NYAM prediction protocol with grammar integration
    
    Executes the complete prediction pipeline within the 14-minute window
    from 09:15 to 09:29 ET, ensuring prediction readiness before market open.
    """
    
    def __init__(self):
        self.start_time = None
        self.protocol_stages = {
            "data_collection": {"duration": 300, "status": "pending"},      # 5 minutes
            "grammar_bridge": {"duration": 300, "status": "pending"},       # 5 minutes  
            "prediction_generation": {"duration": 180, "status": "pending"}, # 3 minutes
            "validation": {"duration": 60, "status": "pending"}             # 1 minute
        }
        
        # Session time mappings (CORRECTED - COMPLETE SEQUENCE)
        self.session_times = {
            "asia": "19:00-23:59",      # Previous day
            "london": "02:00-04:59",    # Current day
            "premarket": "07:00-09:29", # Current day
            "ny_am": "09:30-11:59",     # Morning session
            "lunch": "12:00-12:59",     # Lunch session (59 minutes)
            "ny_pm": "13:30-16:09"      # Afternoon session (159 minutes)
        }
        
        logger.info("🎯 NYAM Prediction Protocol initialized")
        logger.info(f"   Session times: {self.session_times}")
        logger.info(f"   Total protocol duration: {sum(stage['duration'] for stage in self.protocol_stages.values())} seconds")
    
    def execute_protocol(self) -> Dict[str, Any]:
        """
        Execute the complete NYAM prediction protocol
        
        Returns:
            Protocol execution results with prediction and timing metrics
        """
        self.start_time = time.time()
        protocol_start = datetime.now()
        
        logger.info("🚨 CRITICAL: NYAM Prediction Protocol ACTIVATED")
        logger.info(f"   Protocol start: {protocol_start.strftime('%H:%M:%S')} ET")
        logger.info(f"   Market open: 09:30:00 ET")
        logger.info(f"   Time remaining: {self._calculate_time_to_market_open()} minutes")
        
        try:
            # Stage 1: Data Collection (5 minutes max)
            logger.info("📊 STAGE 1: Data Collection Pipeline")
            data_result = self._execute_data_collection()
            self._update_stage_status("data_collection", data_result["success"])
            
            if not data_result["success"]:
                return self._create_failure_result("Data collection failed", data_result)
            
            # Stage 2: Grammar Bridge (5 minutes max)
            logger.info("🌉 STAGE 2: Grammar Bridge Transformation")
            bridge_result = self._execute_grammar_bridge()
            self._update_stage_status("grammar_bridge", bridge_result["success"])
            
            if not bridge_result["success"]:
                return self._create_failure_result("Grammar bridge failed", bridge_result)
            
            # Stage 3: Prediction Generation (3 minutes max)
            logger.info("🎯 STAGE 3: Grammar-Integrated Prediction")
            prediction_result = self._execute_prediction_generation()
            self._update_stage_status("prediction_generation", prediction_result["success"])
            
            if not prediction_result["success"]:
                return self._create_failure_result("Prediction generation failed", prediction_result)
            
            # Stage 4: Validation (1 minute max)
            logger.info("✅ STAGE 4: Prediction Validation")
            validation_result = self._execute_validation(prediction_result["prediction"])
            self._update_stage_status("validation", validation_result["success"])
            
            # Calculate final timing
            total_runtime = time.time() - self.start_time
            completion_time = datetime.now()
            
            logger.info("🎉 NYAM PREDICTION PROTOCOL COMPLETED")
            logger.info(f"   Completion time: {completion_time.strftime('%H:%M:%S')} ET")
            logger.info(f"   Total runtime: {total_runtime:.1f} seconds")
            logger.info(f"   Time to market open: {self._calculate_time_to_market_open():.1f} minutes")
            
            return {
                "protocol_status": "SUCCESS",
                "prediction": prediction_result["prediction"],
                "execution_metrics": {
                    "total_runtime_seconds": total_runtime,
                    "completion_time": completion_time.isoformat(),
                    "time_to_market_open_minutes": self._calculate_time_to_market_open(),
                    "stage_results": self.protocol_stages
                },
                "validation_results": validation_result,
                "grammar_integration": {
                    "bridge_events_generated": bridge_result.get("events_generated", 0),
                    "grammar_patterns_found": bridge_result.get("patterns_found", 0),
                    "fisher_regime_detected": prediction_result.get("fisher_regime", "probabilistic")
                }
            }
            
        except Exception as e:
            logger.error(f"🚨 CRITICAL FAILURE: Protocol execution failed: {e}")
            return self._create_failure_result("Protocol execution failed", {"error": str(e)})
    
    def _execute_data_collection(self) -> Dict[str, Any]:
        """Execute data collection pipeline"""
        try:
            stage_start = time.time()
            
            # Run lvl1_enhance for recent sessions
            cmd = [
                "python3", "run_compartments.py", 
                "--sequence", "lvl1_enhance"
            ]
            
            logger.info(f"   Executing: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            stage_duration = time.time() - stage_start
            
            if result.returncode == 0:
                logger.info(f"   ✅ Data collection completed in {stage_duration:.1f}s")
                return {
                    "success": True,
                    "duration": stage_duration,
                    "enhanced_sessions": self._count_enhanced_sessions()
                }
            else:
                logger.error(f"   ❌ Data collection failed: {result.stderr}")
                return {
                    "success": False,
                    "duration": stage_duration,
                    "error": result.stderr
                }
                
        except subprocess.TimeoutExpired:
            logger.error("   ❌ Data collection timed out (5 minutes)")
            return {"success": False, "duration": 300, "error": "Timeout"}
        except Exception as e:
            logger.error(f"   ❌ Data collection error: {e}")
            return {"success": False, "duration": 0, "error": str(e)}
    
    def _execute_grammar_bridge(self) -> Dict[str, Any]:
        """Execute grammar bridge transformation"""
        try:
            stage_start = time.time()
            
            # Run grammar bridge compartment
            cmd = [
                "python3", "run_compartments.py",
                "--sequence", "grammar_bridge"
            ]
            
            logger.info(f"   Executing: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            stage_duration = time.time() - stage_start
            
            if result.returncode == 0:
                # Load bridge results
                bridge_stats = self._load_grammar_bridge_stats()
                logger.info(f"   ✅ Grammar bridge completed in {stage_duration:.1f}s")
                logger.info(f"   Events generated: {bridge_stats.get('events_generated', 0)}")
                logger.info(f"   Patterns found: {bridge_stats.get('patterns_found', 0)}")
                
                return {
                    "success": True,
                    "duration": stage_duration,
                    "events_generated": bridge_stats.get("events_generated", 0),
                    "patterns_found": bridge_stats.get("patterns_found", 0)
                }
            else:
                logger.error(f"   ❌ Grammar bridge failed: {result.stderr}")
                return {
                    "success": False,
                    "duration": stage_duration,
                    "error": result.stderr
                }
                
        except subprocess.TimeoutExpired:
            logger.error("   ❌ Grammar bridge timed out (5 minutes)")
            return {"success": False, "duration": 300, "error": "Timeout"}
        except Exception as e:
            logger.error(f"   ❌ Grammar bridge error: {e}")
            return {"success": False, "duration": 0, "error": str(e)}
    
    def _execute_prediction_generation(self) -> Dict[str, Any]:
        """Execute grammar-integrated prediction generation"""
        try:
            stage_start = time.time()
            
            # Run enhanced predict compartment
            cmd = [
                "python3", "run_compartments.py",
                "--sequence", "predict"
            ]
            
            logger.info(f"   Executing: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
            
            stage_duration = time.time() - stage_start
            
            if result.returncode == 0:
                # Load prediction results
                prediction_data = self._load_prediction_results()
                fisher_regime = self._detect_fisher_regime_from_output(result.stdout)
                
                logger.info(f"   ✅ Prediction generation completed in {stage_duration:.1f}s")
                logger.info(f"   Regime: {fisher_regime}")
                
                return {
                    "success": True,
                    "duration": stage_duration,
                    "prediction": prediction_data,
                    "fisher_regime": fisher_regime
                }
            else:
                logger.error(f"   ❌ Prediction generation failed: {result.stderr}")
                return {
                    "success": False,
                    "duration": stage_duration,
                    "error": result.stderr
                }
                
        except subprocess.TimeoutExpired:
            logger.error("   ❌ Prediction generation timed out (3 minutes)")
            return {"success": False, "duration": 180, "error": "Timeout"}
        except Exception as e:
            logger.error(f"   ❌ Prediction generation error: {e}")
            return {"success": False, "duration": 0, "error": str(e)}
    
    def _execute_validation(self, prediction: Dict[str, Any]) -> Dict[str, Any]:
        """Execute prediction validation"""
        try:
            stage_start = time.time()
            
            # Basic validation checks
            validation_results = {
                "prediction_present": prediction is not None,
                "timing_reasonable": False,
                "confidence_valid": False,
                "regime_detected": False
            }
            
            if prediction:
                # Check timing reasonableness (0.5 to 120 minutes)
                timing = prediction.get("timing_window", "0min")
                timing_value = float(timing.replace("min", ""))
                validation_results["timing_reasonable"] = 0.5 <= timing_value <= 120
                
                # Check confidence validity (0.0 to 1.0)
                confidence = prediction.get("confidence", 0.0)
                validation_results["confidence_valid"] = 0.0 <= confidence <= 1.0
                
                # Check regime detection
                oracle_choice = prediction.get("oracle_choice", "")
                validation_results["regime_detected"] = "deterministic" in oracle_choice.lower()
            
            stage_duration = time.time() - stage_start
            validation_score = sum(validation_results.values()) / len(validation_results)
            
            logger.info(f"   ✅ Validation completed in {stage_duration:.1f}s")
            logger.info(f"   Validation score: {validation_score:.2f}")
            
            return {
                "success": validation_score >= 0.75,
                "duration": stage_duration,
                "validation_score": validation_score,
                "checks": validation_results
            }
            
        except Exception as e:
            logger.error(f"   ❌ Validation error: {e}")
            return {"success": False, "duration": 0, "error": str(e)}
    
    def _calculate_time_to_market_open(self) -> float:
        """Calculate minutes remaining until 09:30 ET market open"""
        now = datetime.now()
        market_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
        
        # If past market open, assume next day
        if now > market_open:
            market_open += timedelta(days=1)
        
        time_diff = market_open - now
        return time_diff.total_seconds() / 60.0
    
    def _update_stage_status(self, stage: str, success: bool):
        """Update protocol stage status"""
        if stage in self.protocol_stages:
            self.protocol_stages[stage]["status"] = "completed" if success else "failed"
    
    def _count_enhanced_sessions(self) -> int:
        """Count enhanced session files"""
        try:
            enhanced_dir = Path("enhanced_sessions")
            if enhanced_dir.exists():
                return len(list(enhanced_dir.glob("enhanced_*.json")))
            return 0
        except:
            return 0
    
    def _load_grammar_bridge_stats(self) -> Dict[str, Any]:
        """Load grammar bridge statistics"""
        try:
            bridge_file = Path("grammar_bridge/cascade_events.json")
            if bridge_file.exists():
                with open(bridge_file, 'r') as f:
                    data = json.load(f)
                return {
                    "events_generated": data.get("total_events", 0),
                    "patterns_found": len(data.get("session_metadata", {}))
                }
            return {}
        except:
            return {}
    
    def _load_prediction_results(self) -> Dict[str, Any]:
        """Load prediction results"""
        try:
            pred_file = Path("predictions/predictions.json")
            if pred_file.exists():
                with open(pred_file, 'r') as f:
                    data = json.load(f)
                predictions = data.get("predictions", [])
                return predictions[0] if predictions else {}
            return {}
        except:
            return {}
    
    def _detect_fisher_regime_from_output(self, output: str) -> str:
        """Detect Fisher regime from command output"""
        if "DETERMINISTIC MODE" in output:
            return "deterministic"
        elif "PROBABILISTIC MODE" in output:
            return "probabilistic"
        elif "Fisher spike detected" in output:
            return "deterministic_spike"
        else:
            return "unknown"
    
    def _create_failure_result(self, reason: str, details: Dict[str, Any]) -> Dict[str, Any]:
        """Create failure result"""
        return {
            "protocol_status": "FAILED",
            "failure_reason": reason,
            "failure_details": details,
            "execution_metrics": {
                "total_runtime_seconds": time.time() - self.start_time if self.start_time else 0,
                "stage_results": self.protocol_stages
            }
        }


def main():
    """Main execution function"""
    protocol = NYAMPredictionProtocol()
    result = protocol.execute_protocol()
    
    # Save protocol results
    output_file = Path("nyam_protocol_results.json")
    with open(output_file, 'w') as f:
        json.dump(result, f, indent=2)
    
    logger.info(f"📋 Protocol results saved to {output_file}")
    
    # Print summary
    if result["protocol_status"] == "SUCCESS":
        prediction = result["prediction"]
        logger.info("🎯 NYAM PREDICTION READY:")
        logger.info(f"   Cascade timing: {prediction.get('timing_window', 'unknown')}")
        logger.info(f"   Confidence: {prediction.get('confidence', 0.0):.3f}")
        logger.info(f"   Oracle choice: {prediction.get('oracle_choice', 'unknown')}")
        logger.info(f"   Time to market: {result['execution_metrics']['time_to_market_open_minutes']:.1f} minutes")
    else:
        logger.error(f"🚨 PROTOCOL FAILED: {result['failure_reason']}")


if __name__ == "__main__":
    main()
