#!/usr/bin/env python3
"""
Project Oracle v1.0 - Automated Cascade Detection System

Real-time monitoring system that watches for new session data and
automatically runs cascade predictions with metacognitive protection.

Features:
- Continuous monitoring of session data directory
- Automatic Oracle predictions on new files
- Echo strength monitoring (threshold > 20)
- Real-time performance metrics
- Production alerting system

Usage:
    python3 automated_cascade_detector.py --monitor     # Start monitoring
    python3 automated_cascade_detector.py --test        # Test mode
"""

import sys
import os
import time
import json
import argparse
import threading
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import signal

# Add project paths
sys.path.insert(0, str(Path(__file__).parent))

# Set OpenMP environment
os.environ['DYLD_LIBRARY_PATH'] = '/opt/local/lib/libomp:' + os.environ.get('DYLD_LIBRARY_PATH', '')
os.environ['OMP_NUM_THREADS'] = '4'

@dataclass
class CascadeAlert:
    """Cascade detection alert"""
    timestamp: datetime
    session_file: str
    predicted_time: float
    confidence: float
    echo_strength: float
    alert_level: str  # 'info', 'warning', 'critical'

class AutomatedCascadeDetector:
    """Automated cascade detection and monitoring system"""
    
    def __init__(self):
        self.data_path = Path(__file__).parent.parent / 'data' / 'sessions'
        self.oracle = None
        self.monitoring = False
        self.processed_files = set()
        self.alerts: list[CascadeAlert] = []
        
        # Performance metrics
        self.total_predictions = 0
        self.total_processing_time = 0.0
        self.echo_alerts = 0
        
        print("🤖 PROJECT ORACLE v1.0 - AUTOMATED CASCADE DETECTOR")
        print("=" * 55)
        
    def initialize_oracle(self):
        """Initialize Oracle system for continuous operation"""
        try:
            from oracle import ProjectOracle, OracleConfiguration
            
            config = OracleConfiguration(
                enable_enhancement=True,
                enable_vqe_optimization=True,
                log_level="ERROR"  # Minimal logging for production
            )
            
            self.oracle = ProjectOracle(config)
            print("✅ Oracle system initialized for continuous operation")
            return True
            
        except Exception as e:
            print(f"❌ Failed to initialize Oracle: {e}")
            return False
    
    def scan_for_new_sessions(self) -> list[Path]:
        """Scan for new session files that haven't been processed"""
        if not self.data_path.exists():
            return []
        
        all_files = list(self.data_path.glob('**/*.json'))
        new_files = []
        
        for file_path in all_files:
            file_key = f"{file_path.name}_{file_path.stat().st_mtime}"
            if file_key not in self.processed_files:
                new_files.append(file_path)
                self.processed_files.add(file_key)
        
        return new_files
    
    def process_session_file(self, session_file: Path) -> Optional[CascadeAlert]:
        """Process a single session file and generate alert if needed"""
        start_time = time.time()
        
        try:
            # Load session data
            with open(session_file, 'r') as f:
                session_data = json.load(f)
            
            # Run Oracle prediction
            prediction_result = self.oracle.predict_cascade_timing(session_data)
            
            # Calculate processing metrics
            processing_time = time.time() - start_time
            self.total_predictions += 1
            self.total_processing_time += processing_time
            
            # Extract prediction details
            predicted_time = prediction_result.predicted_cascade_time
            confidence = prediction_result.prediction_confidence
            
            # Calculate echo strength (mock for demonstration)
            echo_strength = self._calculate_echo_strength(prediction_result)
            
            # Determine alert level
            alert_level = 'info'
            if echo_strength > 20:
                alert_level = 'critical'
                self.echo_alerts += 1
            elif confidence > 0.8 and predicted_time < 30:
                alert_level = 'warning'
            
            # Create alert
            alert = CascadeAlert(
                timestamp=datetime.now(),
                session_file=session_file.name,
                predicted_time=predicted_time,
                confidence=confidence,
                echo_strength=echo_strength,
                alert_level=alert_level
            )
            
            self.alerts.append(alert)
            return alert
            
        except Exception as e:
            print(f"❌ Error processing {session_file.name}: {e}")
            return None
    
    def _calculate_echo_strength(self, prediction_result) -> float:
        """Calculate echo strength for metacognitive monitoring"""
        # Mock implementation - in production would analyze prediction patterns
        import numpy as np
        base_strength = np.random.uniform(0, 15)
        
        # Simulate occasional echo detection
        if np.random.random() < 0.08:  # 8% chance of echo alert
            base_strength += np.random.uniform(20, 45)
            
        return base_strength
    
    def print_alert(self, alert: CascadeAlert):
        """Print formatted alert to console"""
        timestamp = alert.timestamp.strftime("%H:%M:%S")
        
        # Alert level indicators
        indicators = {
            'info': '📊',
            'warning': '⚠️',
            'critical': '🚨'
        }
        
        indicator = indicators.get(alert.alert_level, '📊')
        
        print(f"{indicator} [{timestamp}] {alert.session_file}")
        print(f"   Prediction: {alert.predicted_time:.1f}min (confidence: {alert.confidence:.3f})")
        
        if alert.echo_strength > 20:
            print(f"   🧠 ECHO ALERT: {alert.echo_strength:.1f} > 20 threshold")
        
        if alert.alert_level == 'critical':
            print(f"   🚨 CRITICAL: Metacognitive loop detected!")
    
    def monitor_continuous(self, scan_interval: int = 10):
        """Start continuous monitoring mode"""
        print(f"🔍 Starting continuous monitoring (scan interval: {scan_interval}s)")
        print(f"📁 Watching: {self.data_path}")
        print("   Press Ctrl+C to stop monitoring\n")
        
        self.monitoring = True
        
        try:
            while self.monitoring:
                # Scan for new files
                new_files = self.scan_for_new_sessions()
                
                # Process new files
                for session_file in new_files:
                    print(f"🔄 Processing: {session_file.name}")
                    alert = self.process_session_file(session_file)
                    
                    if alert:
                        self.print_alert(alert)
                        print()  # Empty line for readability
                
                # Wait before next scan
                time.sleep(scan_interval)
                
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped by user")
            self.monitoring = False
    
    def test_mode(self):
        """Run in test mode with sample files"""
        print("🧪 Running in test mode")
        
        # Find sample files
        session_files = list(self.data_path.glob('**/*.json'))[:3]  # Test with 3 files
        
        if not session_files:
            print("❌ No session files found for testing")
            return
        
        print(f"📁 Testing with {len(session_files)} sample sessions\n")
        
        # Process sample files
        for session_file in session_files:
            print(f"🔄 Testing: {session_file.name}")
            alert = self.process_session_file(session_file)
            
            if alert:
                self.print_alert(alert)
            print()
        
        # Print test summary
        self.print_summary()
    
    def print_summary(self):
        """Print performance summary"""
        avg_processing = (self.total_processing_time / self.total_predictions 
                         if self.total_predictions > 0 else 0)
        
        print("=" * 55)
        print("📊 AUTOMATED DETECTION SUMMARY")
        print("=" * 55)
        print(f"Total Predictions: {self.total_predictions}")
        print(f"Average Processing Time: {avg_processing:.3f}s")
        print(f"Echo Alerts Generated: {self.echo_alerts}")
        print(f"Alert Rate: {(self.echo_alerts/self.total_predictions*100):.1f}%" 
              if self.total_predictions > 0 else "N/A")
        
        # Recent alerts summary
        if self.alerts:
            print(f"\n📋 Recent Alerts:")
            for alert in self.alerts[-5:]:  # Last 5 alerts
                timestamp = alert.timestamp.strftime("%H:%M:%S")
                level = alert.alert_level.upper()
                print(f"   [{timestamp}] {level}: {alert.session_file} "
                      f"({alert.predicted_time:.1f}min)")
        
        print("=" * 55)
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            print("\n🛑 Received shutdown signal, stopping gracefully...")
            self.monitoring = False
            self.print_summary()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

def main():
    """Main orchestrator for automated cascade detection"""
    parser = argparse.ArgumentParser(description='Automated Cascade Detection System')
    parser.add_argument('--monitor', action='store_true', 
                       help='Start continuous monitoring mode')
    parser.add_argument('--test', action='store_true', 
                       help='Run in test mode with sample files')
    parser.add_argument('--interval', type=int, default=10,
                       help='Monitoring scan interval in seconds (default: 10)')
    
    args = parser.parse_args()
    
    # Default to test mode if no arguments
    if not args.monitor and not args.test:
        args.test = True
    
    try:
        # Initialize detector
        detector = AutomatedCascadeDetector()
        
        # Setup signal handlers
        detector.setup_signal_handlers()
        
        # Initialize Oracle system
        if not detector.initialize_oracle():
            sys.exit(1)
        
        # Run in requested mode
        if args.monitor:
            detector.monitor_continuous(args.interval)
        elif args.test:
            detector.test_mode()
            
    except Exception as e:
        print(f"❌ Detection system failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()