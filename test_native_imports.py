#!/usr/bin/env python3
"""
Test Native Oracle Imports - No Subprocess Fallback

Tests the complete import chain with MacPorts OpenMP configuration
to achieve native performance without subprocess isolation.
"""

print("🔍 TESTING NATIVE ORACLE IMPORTS")
print("=" * 50)

import sys
from pathlib import Path
import time

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

# Test 1: Basic Environment Setup
print("1️⃣ Environment Setup:")
start_time = time.time()

try:
    import os
    print(f"   ✅ DYLD_LIBRARY_PATH: {os.environ.get('DYLD_LIBRARY_PATH', 'Not set')}")
    print(f"   ✅ OMP_NUM_THREADS: {os.environ.get('OMP_NUM_THREADS', 'Not set')}")
except Exception as e:
    print(f"   ❌ Environment setup failed: {e}")

# Test 2: XGBoost with OpenMP
print("\n2️⃣ XGBoost Import Test:")
try:
    # Set MacPorts OpenMP path
    os.environ['DYLD_LIBRARY_PATH'] = '/opt/local/lib/libomp:' + os.environ.get('DYLD_LIBRARY_PATH', '')
    os.environ['OMP_NUM_THREADS'] = '4'
    
    import xgboost as xgb
    print("   ✅ XGBoost imported successfully")
    
    # Test model loading
    model_path = Path(__file__).parent.parent / 'ml_models' / 'final_regime_classifier.xgb'
    if model_path.exists():
        print(f"   ✅ XGBoost model found: {model_path}")
        # Use xgb.Booster() instead of XGBRegressor for .xgb files
        booster = xgb.Booster()
        booster.load_model(str(model_path))
        print(f"   ✅ Model loaded successfully")
    else:
        print(f"   ⚠️ Model not found at: {model_path}")
        
except Exception as e:
    print(f"   ❌ XGBoost test failed: {e}")
    import traceback
    traceback.print_exc()

# Test 3: Core Component Imports
print("\n3️⃣ Core Component Imports:")

components_to_test = [
    ("RG Scaler", "core_predictor.rg_scaler_production", "RGScaler"),
    ("Fisher Monitor", "core_predictor.fisher_information_monitor", "FisherInformationMonitor"),
    ("Hawkes Engine", "core_predictor.hawkes_engine", "EnhancedHawkesEngine"),
    ("Three Oracle", "three_oracle_architecture", "ThreeOracleSystem"),
    ("VQE Optimizer", "optimization_shell.optimization_shell", "VQEOptimizationShell")
]

for name, module_path, class_name in components_to_test:
    try:
        module = __import__(module_path, fromlist=[class_name])
        cls = getattr(module, class_name)
        print(f"   ✅ {name}: {class_name} imported from {module_path}")
    except Exception as e:
        print(f"   ❌ {name}: {e}")

# Test 4: Main Oracle Import (Critical Test)
print("\n4️⃣ Main Oracle Import (Critical Test):")
try:
    from oracle import ProjectOracle, OracleConfiguration
    print("   ✅ ProjectOracle imported successfully")
    print("   ✅ No circular import detected!")
    
    # Test Oracle instantiation
    config = OracleConfiguration(log_level="WARNING")  # Reduce logging for test
    oracle = ProjectOracle(config)
    print("   ✅ Oracle instantiated successfully")
    
except Exception as e:
    print(f"   ❌ Oracle import/instantiation failed: {e}")
    import traceback
    traceback.print_exc()

# Test 5: Data Path Verification
print("\n5️⃣ Data Path Verification:")
try:
    data_path = Path(__file__).parent.parent / 'data' / 'sessions'
    if data_path.exists():
        json_files = list(data_path.glob('**/*.json'))
        print(f"   ✅ Found {len(json_files)} JSON session files")
        if json_files:
            print(f"   📁 Example file: {json_files[0].name}")
    else:
        print(f"   ❌ Data path not found: {data_path}")
except Exception as e:
    print(f"   ❌ Data path check failed: {e}")

# Performance Summary
total_time = time.time() - start_time
print(f"\n⏱️ Total Import Time: {total_time:.3f}s")
if total_time < 1.0:
    print("🚀 SUCCESS: Native import speed achieved (<1s)")
else:
    print("⚠️ WARNING: Import time above target (>1s)")

print("\n" + "=" * 50)
print("Native import test complete!")

if 'oracle' in sys.modules:
    print("🎯 CRITICAL SUCCESS: Oracle module loaded natively!")
else:
    print("❌ Oracle module not in sys.modules")