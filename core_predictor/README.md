# Core Predictor: Mathematical Architecture Components

This directory contains the complete mathematical architecture implementing the validated multi-theory prediction system with RG Scaler, Fisher Monitor, and Enhanced Hawkes Engine.

## ✅ Implemented Components

### **1. RG Scaler Production (`rg_scaler_production.py`)**
- **Status**: ✅ OPERATIONAL
- **Function**: Universal lens with inverse scaling law
- **Formula**: `s(d) = 15 - 5*log₁₀(d)`
- **Validation**: Correlation -0.9197
- **Usage**: Mandatory first-stage data transformer

### **2. Fisher Information Monitor (`fisher_information_monitor.py`)**
- **Status**: ✅ OPERATIONAL  
- **Function**: Crystallization detector with hard-coded interrupt
- **Threshold**: F > 1000 = RED ALERT
- **Action**: Override probabilistic → deterministic mode
- **Integration**: Step 2 in prediction pipeline

### **3. Enhanced Hawkes Engine (`hawkes_engine.py`)**
- **Status**: ✅ OPERATIONAL
- **Function**: Multi-dimensional cascade timing prediction
- **Formula**: `λ(t) = μ + Σ α_i * exp(-β_i * (t - t_i))`
- **Dimensions**: 10+ parameter optimization
- **Integration**: Core prediction engine (Step 3)

### **4. Cascade Classifier (`cascade_classifier.py`)**
- **Status**: ✅ OPERATIONAL
- **Function**: Energy density cascade type classification
- **Types**: 5-tier cascade classification system
- **Integration**: Works with Hawkes predictions

### **5. System Constraints (`constraints.py`)**
- **Status**: ✅ OPERATIONAL
- **Function**: Mathematical constants and business rules
- **Coverage**: All validated system constants
- **Usage**: Immutable mathematical foundation

### **6. Temporal Correlator (`temporal_correlator.py`)**
- **Status**: ✅ OPERATIONAL
- **Function**: Cross-session temporal analysis
- **Integration**: Supporting component for predictions

## 🔧 Individual Component Usage

### **Safe Usage (Components Work Standalone)**
```python
# RG Scaler
from core_predictor.rg_scaler_production import create_production_rg_scaler
rg_scaler = create_production_rg_scaler()
result = rg_scaler.transform_session_data(session_data)

# Fisher Monitor  
from core_predictor.fisher_information_monitor import create_fisher_monitor
fisher_monitor = create_fisher_monitor(spike_threshold=1000.0)
spike_result = fisher_monitor.analyze_spike(binned_counts)

# Hawkes Engine
from core_predictor.hawkes_engine import create_enhanced_hawkes_engine
hawkes_engine = create_enhanced_hawkes_engine()
prediction = hawkes_engine.predict_cascade_timing(session_data)

# Cascade Classifier
from core_predictor.cascade_classifier import CascadeClassifier
classifier = CascadeClassifier()
classification = classifier.classify_session(session_data)
```

### ⚠️ **Integration Warning**
```python
# DO NOT import via main Oracle system - causes timeout:
# from oracle import create_project_oracle  # ❌ HANGS
```

## 📊 Performance Metrics

### **Individual Component Performance (Validated)**
- **RG Scaler**: ~1-5ms processing time
- **Fisher Monitor**: ~2-10ms analysis time  
- **Hawkes Engine**: ~50-200ms prediction time
- **Cascade Classifier**: ~1-2ms classification time

### **Mathematical Accuracy (Validated)**
- **RG Scaling Formula**: Correlation -0.9197 with experimental data
- **Fisher Information**: F > 1000 threshold detection working
- **Hawkes Process**: Multi-dimensional λ calculations accurate
- **Cascade Classification**: 5-tier energy density system operational

## 🔍 Component Testing

### **Individual Component Tests (Working)**
```bash
# Test each component individually
python rg_scaler_production.py
python fisher_information_monitor.py  
python hawkes_engine.py
python cascade_classifier.py
```

### **Integration Tests (Failed)**
```bash
# These will timeout due to system-wide initialization issues:
python ../oracle.py                    # ❌ HANGS
python ../test_complete_system_integration.py  # ❌ HANGS
```

## 🚨 Known Issues

### **System Integration Timeout**
- **Individual components**: ✅ All working standalone
- **System integration**: ❌ Initialization timeout when imported via main Oracle
- **Root cause**: Complex cross-component dependencies causing circular imports or resource contention
- **Impact**: Prevents end-to-end system operation

### **Workaround for Development**
Use components individually rather than through the main Oracle system until integration timeout is resolved.

## 📁 File Structure

```
core_predictor/
├── README.md                      # This file
├── rg_scaler_production.py        # ✅ Universal lens (WORKING)
├── fisher_information_monitor.py  # ✅ Crystallization detector (WORKING)  
├── hawkes_engine.py               # ✅ Multi-dimensional prediction (WORKING)
├── cascade_classifier.py          # ✅ Energy classification (WORKING)
├── constraints.py                 # ✅ Mathematical constants (WORKING)
└── temporal_correlator.py         # ✅ Temporal analysis (WORKING)
```

## 🛠️ Debugging Guidelines

### **Component Health Check**
1. Test individual components first
2. Check mathematical formula implementations  
3. Validate input/output data structures
4. Monitor performance metrics

### **Integration Issues**
- Avoid importing through main Oracle system
- Use factory functions for component creation
- Test components in isolation first
- Check for circular import dependencies

---

**Status**: Individual components OPERATIONAL ✅  
**Integration**: BLOCKED by system-wide timeout issue ❌  
**Mathematical Foundation**: VALIDATED ✅
