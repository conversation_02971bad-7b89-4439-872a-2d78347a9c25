"""
Cascade Classification and Sequential Analysis System
Advanced system to detect, classify, and correlate cascade types across temporal sequences
"""

import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from enum import Enum

class CascadeType(Enum):
    PRIMER = "primer_cascade"
    STANDARD = "standard_cascade" 
    MAJOR = "major_cascade"
    MICRO = "micro"  # Keep for compatibility
    UNCLASSIFIED = "unclassified"

@dataclass
class CascadeEvent:
    """Individual cascade event with full context"""
    timestamp: str
    price_level: float
    cascade_type: CascadeType
    magnitude: float
    event_context: str
    prediction_time: Optional[str] = None
    validation_status: Optional[str] = None
    sequence_id: Optional[str] = None
    correlation_strength: float = 0.0

@dataclass
class CascadeSequence:
    """Sequence of related cascade events"""
    sequence_id: str
    events: List[CascadeEvent]
    start_time: str
    end_time: str
    duration_minutes: float
    sequence_type: str
    prediction_accuracy: float = 0.0
    validation_correlation: float = 0.0

class CascadeClassificationSystem:
    """Advanced cascade detection and classification system"""
    
    def __init__(self):
        # Using your established energy density thresholds from research/energy_density_overlay.py
        self.classification_rules = {
            'primer_cascade': {
                'energy_density_range': (0.5, 1.0),  # PRIMER_CASCADE_DENSITY_MIN/MAX
                'magnitude_range': (0.5, 1.0),
                'duration_max_minutes': 15,
                'price_movement_threshold': 25.0,
                'keywords': ['primer', 'initial', 'trigger', 'setup']
            },
            'standard_cascade': {
                'energy_density_range': (1.5, 2.0),  # CASCADE_TRIGGER_DENSITY
                'magnitude_range': (1.5, 2.0), 
                'duration_max_minutes': 30,
                'price_movement_threshold': 50.0,
                'keywords': ['standard', 'cascade', 'triggered']
            },
            'major_cascade': {
                'energy_density_range': (2.0, float('inf')),  # MAJOR_CASCADE_DENSITY+
                'magnitude_range': (2.0, 20.0),
                'duration_max_minutes': 60,
                'price_movement_threshold': 100.0,
                'keywords': ['major', 'large', 'significant', 'extreme']
            },
            'micro': {  # Legacy compatibility
                'energy_density_range': (0.1, 0.5),
                'magnitude_range': (0.1, 0.5),
                'duration_max_minutes': 5,
                'price_movement_threshold': 10.0,
                'keywords': ['micro', 'small', 'minor']
            }
        }
        
        self.sequence_patterns = {
            'primer_to_major': ['primer_cascade', 'standard_cascade', 'major_cascade'],
            'escalation_sequence': ['primer_cascade', 'standard_cascade', 'major_cascade'],
            'reversal_pattern': ['major_cascade', 'standard_cascade', 'primer_cascade'],
            'dual_cascade': ['standard_cascade', 'standard_cascade'],
            'complex_multi': ['micro', 'primer_cascade', 'standard_cascade', 'major_cascade']
        }
        
        self.temporal_correlation_engine = TemporalCorrelationEngine()
        self.sequence_analyzer = SequencePatternAnalyzer()
        
    def classify_cascade_event(self, event_data: Dict[str, Any]) -> CascadeEvent:
        """Classify a single cascade event using advanced heuristics"""
        
        # Extract basic event information
        timestamp = event_data.get('timestamp', '')
        price_level = event_data.get('price_level', 0.0)
        movement_type = event_data.get('movement_type', '').lower()
        event_context = event_data.get('event_context', movement_type)
        
        # Calculate magnitude from price and context
        magnitude = self._calculate_event_magnitude(event_data)
        
        # Classify using multiple criteria
        cascade_type = self._determine_cascade_type(movement_type, magnitude, event_data)
        
        return CascadeEvent(
            timestamp=timestamp,
            price_level=price_level,
            cascade_type=cascade_type,
            magnitude=magnitude,
            event_context=event_context
        )
    
    def _calculate_event_magnitude(self, event_data: Dict[str, Any]) -> float:
        """Calculate event magnitude using multiple factors"""
        
        magnitude = 1.0  # Base magnitude
        
        # Price-based magnitude
        price_level = event_data.get('price_level', 23000)
        if price_level > 0:
            # Normalize around typical futures price levels
            price_factor = abs(price_level - 23000) / 1000
            magnitude *= (1.0 + price_factor * 0.1)
        
        # Context-based magnitude multipliers
        movement_type = event_data.get('movement_type', '').lower()
        context_multipliers = {
            'session_high': 3.0,
            'session_low': 3.0,
            'fpfvg_rebalance': 2.5,
            'liquidity_takeout': 2.0,
            'redelivery': 1.8,
            'consolidation': 1.0,
            'expansion': 1.5
        }
        
        for context, multiplier in context_multipliers.items():
            if context in movement_type:
                magnitude *= multiplier
                break
        
        # Temporal context (if available)
        if 'temporal_context' in event_data:
            temporal_factor = event_data['temporal_context'].get('intensity', 1.0)
            magnitude *= temporal_factor
        
        return magnitude
    
    def _determine_cascade_type(self, movement_type: str, magnitude: float, event_data: Dict[str, Any]) -> CascadeType:
        """Determine cascade type using classification rules"""
        
        # Keyword-based classification first
        for cascade_name, rules in self.classification_rules.items():
            for keyword in rules['keywords']:
                if keyword in movement_type:
                    return CascadeType(cascade_name)
        
        # Magnitude-based classification using your energy density thresholds
        for cascade_name, rules in self.classification_rules.items():
            min_mag, max_mag = rules['magnitude_range']
            if min_mag <= magnitude <= max_mag:
                if cascade_name == 'primer_cascade':
                    return CascadeType.PRIMER
                elif cascade_name == 'standard_cascade':
                    return CascadeType.STANDARD
                elif cascade_name == 'major_cascade':
                    return CascadeType.MAJOR
                elif cascade_name == 'micro':
                    return CascadeType.MICRO
        
        # Event-specific classification using your energy density thresholds
        if any(term in movement_type for term in ['fpfvg', 'rebalance', 'redelivery']):
            if magnitude >= 2.0:  # MAJOR_CASCADE_DENSITY
                return CascadeType.MAJOR
            elif magnitude >= 1.5:  # CASCADE_TRIGGER_DENSITY
                return CascadeType.STANDARD
            else:
                return CascadeType.PRIMER
                
        if any(term in movement_type for term in ['session_high', 'session_low']):
            return CascadeType.MAJOR if magnitude >= 2.0 else CascadeType.STANDARD
        
        # Default classification based on your established energy density thresholds
        if magnitude >= 2.0:  # MAJOR_CASCADE_DENSITY
            return CascadeType.MAJOR
        elif magnitude >= 1.5:  # CASCADE_TRIGGER_DENSITY
            return CascadeType.STANDARD
        elif magnitude >= 0.5:  # PRIMER_CASCADE_DENSITY_MIN
            return CascadeType.PRIMER
        else:
            return CascadeType.MICRO
    
    def detect_cascade_sequences(self, cascade_events: List[CascadeEvent]) -> List[CascadeSequence]:
        """Detect and classify sequences of related cascade events"""
        
        if not cascade_events:
            return []
        
        # Sort events by timestamp
        sorted_events = sorted(cascade_events, key=lambda x: x.timestamp)
        
        sequences = []
        current_sequence_events = [sorted_events[0]]
        sequence_start_time = sorted_events[0].timestamp
        
        for i in range(1, len(sorted_events)):
            current_event = sorted_events[i]
            previous_event = sorted_events[i-1]
            
            # Calculate time difference
            time_diff = self._calculate_time_difference(previous_event.timestamp, current_event.timestamp)
            
            # Determine if events belong to same sequence (within 30 minutes)
            if time_diff <= 30:
                current_sequence_events.append(current_event)
            else:
                # Finalize current sequence
                if len(current_sequence_events) >= 2:
                    sequence = self._create_sequence(current_sequence_events, sequence_start_time)
                    if sequence:
                        sequences.append(sequence)
                
                # Start new sequence
                current_sequence_events = [current_event]
                sequence_start_time = current_event.timestamp
        
        # Handle final sequence
        if len(current_sequence_events) >= 2:
            sequence = self._create_sequence(current_sequence_events, sequence_start_time)
            if sequence:
                sequences.append(sequence)
        
        return sequences
    
    def _create_sequence(self, events: List[CascadeEvent], start_time: str) -> Optional[CascadeSequence]:
        """Create cascade sequence from events"""
        
        if len(events) < 2:
            return None
        
        end_time = events[-1].timestamp
        duration = self._calculate_time_difference(start_time, end_time)
        
        # Determine sequence type
        event_types = [event.cascade_type.value for event in events]
        sequence_type = self._classify_sequence_pattern(event_types)
        
        # Generate unique sequence ID
        sequence_id = f"seq_{start_time.replace(':', '')}_{len(events)}events"
        
        return CascadeSequence(
            sequence_id=sequence_id,
            events=events,
            start_time=start_time,
            end_time=end_time,
            duration_minutes=duration,
            sequence_type=sequence_type
        )
    
    def _classify_sequence_pattern(self, event_types: List[str]) -> str:
        """Classify sequence pattern based on cascade type progression"""
        
        # Check against known patterns
        for pattern_name, pattern_sequence in self.sequence_patterns.items():
            if self._matches_pattern(event_types, pattern_sequence):
                return pattern_name
        
        # Analyze progression characteristics
        if len(set(event_types)) == 1:
            return f"uniform_{event_types[0]}"
        
        # Check for escalation (increasing magnitude)
        magnitude_order = ['micro', 'primer', 'medium', 'larger']
        if self._is_escalating_sequence(event_types, magnitude_order):
            return "escalation_sequence"
        
        # Check for de-escalation (decreasing magnitude)
        if self._is_escalating_sequence(event_types[::-1], magnitude_order):
            return "deescalation_sequence"
        
        return "complex_mixed"
    
    def _matches_pattern(self, event_types: List[str], pattern: List[str]) -> bool:
        """Check if event types match a known pattern"""
        if len(event_types) != len(pattern):
            return False
        return all(et == pt for et, pt in zip(event_types, pattern))
    
    def _is_escalating_sequence(self, event_types: List[str], magnitude_order: List[str]) -> bool:
        """Check if sequence shows escalating magnitude"""
        if len(event_types) < 2:
            return False
            
        for i in range(len(event_types) - 1):
            current_idx = magnitude_order.index(event_types[i]) if event_types[i] in magnitude_order else -1
            next_idx = magnitude_order.index(event_types[i+1]) if event_types[i+1] in magnitude_order else -1
            
            if current_idx >= next_idx:  # Not escalating
                return False
        return True
    
    def _calculate_time_difference(self, time1: str, time2: str) -> float:
        """Calculate time difference in minutes between two timestamps"""
        try:
            # Handle HH:MM or HH:MM:SS format
            if len(time1.split(':')) == 2:
                time1 += ':00'
            if len(time2.split(':')) == 2:
                time2 += ':00'
                
            dt1 = datetime.strptime(time1, '%H:%M:%S')
            dt2 = datetime.strptime(time2, '%H:%M:%S')
            
            diff = (dt2 - dt1).total_seconds() / 60
            
            # Handle day rollover
            if diff < 0:
                diff += 24 * 60
                
            return diff
        except:
            return 0.0

try:
    from .temporal_correlator import TemporalCorrelationEngine, SequencePatternAnalyzer
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(__file__))
    from temporal_correlator import TemporalCorrelationEngine, SequencePatternAnalyzer

def demonstrate_cascade_classification():
    """Demonstrate the cascade classification system"""
    
    print("🎯 CASCADE CLASSIFICATION AND SEQUENTIAL ANALYSIS SYSTEM")
    print("=" * 70)
    
    # Create sample cascade events
    sample_events = [
        {
            'timestamp': '09:37:00',
            'price_level': 23341.75,
            'movement_type': 'am_native_fpfvg_formation',
            'event_context': 'primer_setup'
        },
        {
            'timestamp': '10:18:00', 
            'price_level': 23292.50,
            'movement_type': 'three_day_london_fpfvg_redelivery',
            'event_context': 'major_cascade'
        },
        {
            'timestamp': '10:25:00',
            'price_level': 23280.00,
            'movement_type': 'micro_retracement',
            'event_context': 'minor_adjustment'
        },
        {
            'timestamp': '11:41:00',
            'price_level': 23113.50,
            'movement_type': 'session_low_extreme',
            'event_context': 'larger_cascade'
        }
    ]
    
    # Initialize classification system
    classifier = CascadeClassificationSystem()
    
    # Classify individual events
    print("🔍 INDIVIDUAL CASCADE CLASSIFICATION:")
    classified_events = []
    for event_data in sample_events:
        cascade_event = classifier.classify_cascade_event(event_data)
        classified_events.append(cascade_event)
        
        print(f"   {cascade_event.timestamp}: {cascade_event.cascade_type.value.upper()}")
        print(f"      Price: {cascade_event.price_level}")
        print(f"      Magnitude: {cascade_event.magnitude:.2f}")
        print(f"      Context: {cascade_event.event_context}")
        print()
    
    # Detect sequences
    print("🔗 SEQUENCE DETECTION:")
    sequences = classifier.detect_cascade_sequences(classified_events)
    
    for sequence in sequences:
        print(f"   Sequence: {sequence.sequence_id}")
        print(f"   Type: {sequence.sequence_type}")
        print(f"   Duration: {sequence.duration_minutes:.1f} minutes")
        print(f"   Events: {len(sequence.events)}")
        event_chain = " → ".join([e.cascade_type.value for e in sequence.events])
        print(f"   Chain: {event_chain}")
        print()
    
    # Demonstrate prediction correlation
    print("📊 PREDICTION-VALIDATION CORRELATION:")
    correlation_engine = TemporalCorrelationEngine()
    
    # Sample prediction
    prediction_time = "10:15:00"
    correlation = correlation_engine.correlate_prediction_validation(prediction_time, classified_events)
    
    print(f"   Prediction Time: {prediction_time}")
    if correlation['closest_event']:
        print(f"   Closest Event: {correlation['closest_event'].timestamp}")
        print(f"   Time Error: ±{correlation['time_error_minutes']:.1f} minutes")
        print(f"   Correlation Strength: {correlation['correlation_strength']:.1%}")
        print(f"   Validation Status: {correlation['validation_status'].upper()}")
    
    return {
        'classified_events': classified_events,
        'sequences': sequences,
        'correlation': correlation
    }

if __name__ == "__main__":
    results = demonstrate_cascade_classification()
    
    print(f"\n🎉 CLASSIFICATION SYSTEM DEMONSTRATION COMPLETE")
    print(f"   Events Classified: {len(results['classified_events'])}")
    print(f"   Sequences Detected: {len(results['sequences'])}")
    print(f"   Correlation Analysis: {'✅ Success' if results['correlation']['closest_event'] else '❌ No correlation'}")