"""
File Quality Standards Assessment for Machine Learning Optimization
Analyze all Level-1 JSON files for ML readiness and identify quality issues
"""

import json
import os
from pathlib import Path
from datetime import datetime
import pandas as pd
from typing import Dict, List, Tuple, Any

class FileQualityAssessor:
    """Assess quality standards of Level-1 files for ML optimization"""
    
    def __init__(self):
        self.quality_standards = {
            'required_structure': [
                'level1_json',
                'session_metadata', 
                'price_movements',
                'session_liquidity_events'
            ],
            'optional_enhanced': [
                'energy_state',
                'contamination_analysis', 
                'session_fpfvg'
            ],
            'ml_requirements': {
                'min_price_movements': 5,
                'min_session_duration': 30,
                'required_event_types': ['session_high', 'session_low', 'fpfvg', 'cascade'],
                'cascade_classifications': ['micro', 'primer', 'medium', 'larger']
            }
        }
        
        self.assessment_results = {
            'total_files': 0,
            'valid_files': 0,
            'quality_scores': [],
            'issues_found': [],
            'ml_ready_files': 0,
            'cascade_type_distribution': {},
            'file_details': []
        }
    
    def find_all_level1_files(self) -> List[Path]:
        """Find all Level-1 JSON files in project"""
        project_root = Path("/Users/<USER>/grok-claude-automation")
        
        level1_files = []
        for pattern in ["*Lvl-1*.json", "*Level-1*.json", "*level_1*.json"]:
            level1_files.extend(project_root.rglob(pattern))
        
        return sorted(level1_files)
    
    def assess_file_structure(self, file_path: Path) -> Dict[str, Any]:
        """Assess structural quality of a single file"""
        
        assessment = {
            'file_path': str(file_path),
            'file_size_mb': file_path.stat().st_size / (1024 * 1024),
            'structure_score': 0,
            'content_score': 0,
            'ml_readiness_score': 0,
            'issues': [],
            'cascade_types_found': [],
            'event_count': 0,
            'session_duration': 0
        }
        
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            # Structure assessment
            structure_points = 0
            for required in self.quality_standards['required_structure']:
                if self._check_nested_key(data, required):
                    structure_points += 25
                else:
                    assessment['issues'].append(f"Missing required: {required}")
            
            assessment['structure_score'] = structure_points
            
            # Content assessment if structure is valid
            if structure_points >= 75:  # At least 3/4 required elements
                assessment.update(self._assess_content_quality(data))
            
            return assessment
            
        except json.JSONDecodeError as e:
            assessment['issues'].append(f"JSON parse error: {e}")
            return assessment
        except Exception as e:
            assessment['issues'].append(f"File read error: {e}")
            return assessment
    
    def _check_nested_key(self, data: dict, key_path: str) -> bool:
        """Check if nested key exists in data structure"""
        
        if key_path == 'level1_json':
            return 'level1_json' in data
        
        if 'level1_json' not in data:
            return False
            
        level1 = data['level1_json']
        
        key_mappings = {
            'session_metadata': 'session_metadata',
            'price_movements': 'price_movements', 
            'session_liquidity_events': 'session_liquidity_events',
            'energy_state': 'energy_state',
            'contamination_analysis': 'contamination_analysis',
            'session_fpfvg': 'session_fpfvg'
        }
        
        return key_mappings.get(key_path, key_path) in level1
    
    def _assess_content_quality(self, data: dict) -> Dict[str, Any]:
        """Assess content quality for ML readiness"""
        
        level1 = data['level1_json']
        content_assessment = {
            'content_score': 0,
            'ml_readiness_score': 0,
            'cascade_types_found': [],
            'event_count': 0,
            'session_duration': 0
        }
        
        content_points = 0
        ml_points = 0
        
        # Price movements analysis
        price_movements = level1.get('price_movements', [])
        content_assessment['event_count'] = len(price_movements)
        
        if len(price_movements) >= self.quality_standards['ml_requirements']['min_price_movements']:
            content_points += 25
            ml_points += 25
        
        # Session duration analysis  
        metadata = level1.get('session_metadata', {})
        duration = metadata.get('session_duration', 0)
        content_assessment['session_duration'] = duration
        
        if duration >= self.quality_standards['ml_requirements']['min_session_duration']:
            content_points += 25
            ml_points += 25
        
        # Event type analysis
        event_types_found = set()
        cascade_types = set()
        
        for movement in price_movements:
            movement_type = movement.get('movement_type', '').lower()
            event_types_found.add(movement_type)
            
            # Classify cascade types
            if 'cascade' in movement_type:
                if 'micro' in movement_type:
                    cascade_types.add('micro')
                elif 'primer' in movement_type:
                    cascade_types.add('primer') 
                elif 'medium' in movement_type:
                    cascade_types.add('medium')
                elif 'large' in movement_type:
                    cascade_types.add('larger')
                else:
                    cascade_types.add('unclassified_cascade')
        
        content_assessment['cascade_types_found'] = list(cascade_types)
        
        # Check for required event types
        required_found = 0
        for req_type in self.quality_standards['ml_requirements']['required_event_types']:
            if any(req_type in et for et in event_types_found):
                required_found += 1
        
        if required_found >= 3:  # At least 3 of 4 required types
            content_points += 25
            ml_points += 25
        
        # Enhanced features bonus
        if 'energy_state' in level1:
            content_points += 15
            ml_points += 15
            
        if 'contamination_analysis' in level1:
            content_points += 10
            ml_points += 10
        
        content_assessment['content_score'] = min(content_points, 100)
        content_assessment['ml_readiness_score'] = min(ml_points, 100)
        
        return content_assessment
    
    def run_comprehensive_assessment(self) -> Dict[str, Any]:
        """Run comprehensive quality assessment on all files"""
        
        print("📊 COMPREHENSIVE FILE QUALITY ASSESSMENT")
        print("=" * 60)
        
        level1_files = self.find_all_level1_files()
        self.assessment_results['total_files'] = len(level1_files)
        
        print(f"📁 Found {len(level1_files)} Level-1 files")
        print(f"🔍 Assessing quality standards...")
        
        cascade_distribution = {}
        
        for i, file_path in enumerate(level1_files, 1):
            print(f"   [{i:2d}/{len(level1_files)}] {file_path.name}")
            
            assessment = self.assess_file_structure(file_path)
            self.assessment_results['file_details'].append(assessment)
            
            # Calculate overall quality score
            overall_score = (
                assessment['structure_score'] * 0.4 +
                assessment['content_score'] * 0.4 +
                assessment['ml_readiness_score'] * 0.2
            )
            
            self.assessment_results['quality_scores'].append(overall_score)
            
            if assessment['structure_score'] >= 75:
                self.assessment_results['valid_files'] += 1
                
            if assessment['ml_readiness_score'] >= 75:
                self.assessment_results['ml_ready_files'] += 1
            
            # Track cascade types
            for cascade_type in assessment['cascade_types_found']:
                cascade_distribution[cascade_type] = cascade_distribution.get(cascade_type, 0) + 1
        
        self.assessment_results['cascade_type_distribution'] = cascade_distribution
        
        return self.assessment_results
    
    def generate_quality_report(self) -> str:
        """Generate comprehensive quality report"""
        
        results = self.assessment_results
        avg_quality = sum(results['quality_scores']) / len(results['quality_scores']) if results['quality_scores'] else 0
        
        report = f"""
🏆 FILE QUALITY ASSESSMENT REPORT
================================

📊 OVERALL STATISTICS:
   Total Files Analyzed: {results['total_files']}
   Valid Structure: {results['valid_files']} ({results['valid_files']/results['total_files']*100:.1f}%)
   ML Ready: {results['ml_ready_files']} ({results['ml_ready_files']/results['total_files']*100:.1f}%)
   Average Quality Score: {avg_quality:.1f}/100

🎯 CASCADE TYPE DISTRIBUTION:
"""
        
        for cascade_type, count in results['cascade_type_distribution'].items():
            percentage = count / results['total_files'] * 100
            report += f"   {cascade_type.title()}: {count} files ({percentage:.1f}%)\n"
        
        # Quality tiers
        excellent = sum(1 for score in results['quality_scores'] if score >= 90)
        good = sum(1 for score in results['quality_scores'] if 75 <= score < 90)
        fair = sum(1 for score in results['quality_scores'] if 50 <= score < 75)
        poor = sum(1 for score in results['quality_scores'] if score < 50)
        
        report += f"""
📈 QUALITY DISTRIBUTION:
   Excellent (90-100): {excellent} files ({excellent/results['total_files']*100:.1f}%)
   Good (75-89): {good} files ({good/results['total_files']*100:.1f}%)
   Fair (50-74): {fair} files ({fair/results['total_files']*100:.1f}%)
   Poor (<50): {poor} files ({poor/results['total_files']*100:.1f}%)

💡 ML OPTIMIZATION READINESS:
   Files Ready for XGBoost: {results['ml_ready_files']}
   Files Needing Enhancement: {results['total_files'] - results['ml_ready_files']}
   
🎯 RECOMMENDATIONS:
   ✅ {results['ml_ready_files']} files ready for immediate ML processing
   🔧 {results['total_files'] - results['ml_ready_files']} files need structure/content improvements
   📊 Cascade classification coverage: {len(results['cascade_type_distribution'])} types detected
"""
        
        return report
    
    def identify_problematic_files(self) -> List[Dict[str, Any]]:
        """Identify files with quality issues"""
        
        problematic = []
        
        for detail in self.assessment_results['file_details']:
            overall_score = (
                detail['structure_score'] * 0.4 +
                detail['content_score'] * 0.4 + 
                detail['ml_readiness_score'] * 0.2
            )
            
            if overall_score < 75 or detail['issues']:
                problematic.append({
                    'file': Path(detail['file_path']).name,
                    'score': overall_score,
                    'issues': detail['issues'],
                    'structure_score': detail['structure_score'],
                    'content_score': detail['content_score'],
                    'ml_score': detail['ml_readiness_score']
                })
        
        return sorted(problematic, key=lambda x: x['score'])

if __name__ == "__main__":
    assessor = FileQualityAssessor()
    results = assessor.run_comprehensive_assessment()
    
    print(assessor.generate_quality_report())
    
    problematic = assessor.identify_problematic_files()
    if problematic:
        print(f"\n🚨 PROBLEMATIC FILES REQUIRING ATTENTION:")
        print("=" * 50)
        for issue_file in problematic[:10]:  # Show worst 10
            print(f"📁 {issue_file['file']} (Score: {issue_file['score']:.1f})")
            for issue in issue_file['issues']:
                print(f"   ❌ {issue}")
            print()