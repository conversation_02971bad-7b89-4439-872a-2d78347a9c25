#!/usr/bin/env python3
"""
Audit Agent Demonstration - Issue Detection
==========================================

This script demonstrates how the audit agent detects various types of issues:
1. Missing guard decorators  
2. High semantic drift
3. Git status changes

This is for demonstration purposes only.
"""

import tempfile
import shutil
from pathlib import Path
import os
import sys

# Add current directory to path so we can import audit_agent
sys.path.insert(0, str(Path(__file__).parent))

from audit_agent import GitAuditor

def create_test_scenario():
    """Create a test scenario with various issues"""
    
    print("🎬 Creating Test Scenario with Issues...")
    
    # Create a temporary copy of production_oracle.py with missing guard
    original_file = Path("production_oracle.py")
    temp_file = Path("production_oracle_test.py")
    
    if original_file.exists():
        # Read original content
        with open(original_file, 'r') as f:
            content = f.read()
        
        # Remove one guard decorator to simulate missing protection
        modified_content = content.replace(
            '@guard.register(\n    name="predict_next_event",\n    inputs="partial_pattern",\n    outputs="next_event_probabilities",\n    purpose="Pattern completion prediction from CFG rules ONLY"\n)',
            '# @guard.register - REMOVED FOR TESTING'
        )
        
        # Add some extra functions to increase drift score
        modified_content += '''

# SIMULATED DRIFT - Additional functions added
def extra_function_1():
    """This function increases the drift score"""
    pass

def extra_function_2():  
    """Another function that wasn't in original"""
    pass

def extra_function_3():
    """Yet another new function"""  
    pass

def modified_existing_function(new_param, another_param):
    """This function signature was changed"""
    pass
'''
        
        # Write modified content
        with open(temp_file, 'w') as f:
            f.write(modified_content)
        
        print(f"   ✅ Created test file: {temp_file}")
        return temp_file
    
    else:
        print(f"   ❌ Original file {original_file} not found")
        return None

def run_demonstration():
    """Run a demonstration of the audit agent detecting issues"""
    
    print("🔍 AUDIT AGENT ISSUE DETECTION DEMONSTRATION")
    print("=" * 50)
    
    # Create test scenario
    test_file = create_test_scenario()
    
    if test_file:
        try:
            # Create a custom auditor that includes our test file
            class DemoAuditor(GitAuditor):
                def __init__(self):
                    super().__init__()
                    # Add our test file to critical files for demo
                    self.critical_files.append("production_oracle_test.py")
            
            # Run audit with demo file
            auditor = DemoAuditor()
            report = auditor.run_audit()
            
            # Display results
            formatted_report = auditor.format_audit_report(report)
            print(formatted_report)
            
            print("\n" + "=" * 50)
            print("🎯 DEMONSTRATION ANALYSIS:")
            print("=" * 50)
            
            # Analyze the results
            test_result = None
            for result in report.file_results:
                if "production_oracle_test.py" in result.file_path:
                    test_result = result
                    break
            
            if test_result:
                print(f"📊 Test File Analysis:")
                print(f"   File: production_oracle_test.py")
                print(f"   Guard Decorators: {test_result.guards_found}/{test_result.guards_expected}")
                print(f"   Missing Guards: {test_result.missing_guards}")
                print(f"   Drift Score: {test_result.drift_score:.1f}%")
                print(f"   Semantic Changes: {len(test_result.semantic_changes)}")
                
                print(f"\n🔍 Issues Detected:")
                if test_result.missing_guards:
                    print(f"   ❌ Missing guard decorators: {', '.join(test_result.missing_guards)}")
                if test_result.drift_score > 25.0:
                    print(f"   ❌ High semantic drift: {test_result.drift_score:.1f}%") 
                if test_result.semantic_changes:
                    print(f"   ⚠️  Semantic changes detected:")
                    for change in test_result.semantic_changes[:3]:  # Show first 3
                        print(f"      - {change}")
                
                print(f"\n✅ This demonstrates the audit agent successfully detecting:")
                print(f"   1. Missing @guard.register decorators")  
                print(f"   2. High semantic drift from added/modified functions")
                print(f"   3. Git status changes (untracked files)")
            
        finally:
            # Cleanup test file
            if test_file.exists():
                test_file.unlink()
                print(f"\n🧹 Cleaned up test file: {test_file}")

if __name__ == "__main__":
    run_demonstration()