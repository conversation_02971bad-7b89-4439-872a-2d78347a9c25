{"extraction_metadata": {"timestamp": "2025-08-07T11:36:04.600544", "source": "project_oracle_91.1%_baseline", "methodology": "cascade_as_mathematical_primitive", "total_units": 4}, "cascade_units": [{"unit_id": "volume_spike_reversal", "trigger_vector": [1.0, 0.9, 0.8, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "propagation_matrix": [[0.0, 0.3333333333333333, 0.0], [0.0, 0.0, 0.3333333333333333], [0.0, 0.0, 0.0]], "resolution_state": [1.0, 0.0, 0.0], "frequency": 0.32, "signature_pattern": "Volume spike → Price reversal → Momentum shift", "mathematical_form": "C = λ₁(1.57) ⊗ P(0.00) → R(1.00)"}, {"unit_id": "liquidity_vacuum_cascade", "trigger_vector": [0.0, 0.0, 0.0, 1.0, 0.9, 0.8, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "propagation_matrix": [[0.0, 0.3333333333333333, 0.0], [0.0, 0.0, 0.0], [0.3333333333333333, 0.0, 0.0]], "resolution_state": [0.0, 1.0, 0.0], "frequency": 0.28, "signature_pattern": "Liquidity vacuum → Stop run → FPFVG redelivery", "mathematical_form": "C = λ₁(1.57) ⊗ P(0.00) → R(1.00)"}, {"unit_id": "session_boundary_cascade", "trigger_vector": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.9, 0.8, 0.0, 0.0, 0.0], "propagation_matrix": [[0.0, 0.0, 0.0], [0.0, 0.0, 0.0], [0.3333333333333333, 0.0, 0.3333333333333333]], "resolution_state": [0.0, 0.0, 1.0], "frequency": 0.25, "signature_pattern": "Session boundary → HTF activation → Cascade execution", "mathematical_form": "C = λ₁(1.57) ⊗ P(0.33) → R(1.00)"}, {"unit_id": "crystallization_cascade", "trigger_vector": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.9, 0.8], "propagation_matrix": [[0.0, 0.3333333333333333, 0.0], [0.0, 0.0, 0.3333333333333333], [0.0, 0.0, 0.0]], "resolution_state": [0.5, 0.5, 1.0], "frequency": 0.15, "signature_pattern": "Fisher spike → Deterministic regime → Immediate execution", "mathematical_form": "C = λ₁(1.57) ⊗ P(0.00) → R(1.22)"}], "operator_system": {"cascade_operators": {"C_volume_spike_reversal": {"trigger_eigenvalues": [2.4500000000000006, -2.0502239237500207e-16, -3.03378805960308e-17, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "propagation_eigenvalues": [0.0, 0.0, 0.0], "resolution_state": [1.0, 0.0, 0.0], "frequency_weight": 0.32, "mathematical_form": "C = λ₁(1.57) ⊗ P(0.00) → R(1.00)"}, "C_liquidity_vacuum_cascade": {"trigger_eigenvalues": [2.4500000000000006, -2.0502239237500207e-16, -3.03378805960308e-17, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "propagation_eigenvalues": [0.0, 0.0, 0.0], "resolution_state": [0.0, 1.0, 0.0], "frequency_weight": 0.28, "mathematical_form": "C = λ₁(1.57) ⊗ P(0.00) → R(1.00)"}, "C_session_boundary_cascade": {"trigger_eigenvalues": [2.4500000000000006, -2.0502239237500207e-16, -3.03378805960308e-17, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "propagation_eigenvalues": [0.3333333333333333, 0.0, 0.0], "resolution_state": [0.0, 0.0, 1.0], "frequency_weight": 0.25, "mathematical_form": "C = λ₁(1.57) ⊗ P(0.33) → R(1.00)"}, "C_crystallization_cascade": {"trigger_eigenvalues": [2.4500000000000006, -2.0502239237500207e-16, -3.03378805960308e-17, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "propagation_eigenvalues": [0.0, 0.0, 0.0], "resolution_state": [0.5, 0.5, 1.0], "frequency_weight": 0.15, "mathematical_form": "C = λ₁(1.57) ⊗ P(0.00) → R(1.22)"}}, "composition_rules": {"sequential_composition": "Ĉ₂ ∘ Ĉ₁ (cascade chains)", "parallel_composition": "Ĉ₁ ⊗ Ĉ₂ (simultaneous cascades)", "interference_pattern": "α·Ĉ₁ + β·Ĉ₂ (cascade superposition)"}, "operator_count": 4, "total_coverage": 1.0, "mathematical_foundation": "Cascade quantum operator algebra"}, "mathematical_foundation": {"principle": "Cascades as quantum-like operators", "representation": "C = [trigger_vector, propagation_matrix, resolution_state]", "algebra": "Operator composition and superposition"}}