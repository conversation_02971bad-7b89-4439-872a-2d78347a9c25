# Project Oracle Processors

## Level-1 to Dual-Layer Architecture Processor

Converts existing Level-1 JSON files to the enhanced dual-layer format with grammatical intelligence while preserving ALL existing data.

### Key Features

#### Zero Information Loss
- **Preserves ALL Layer 1 data**: FPFVG analysis, contamination metrics, liquidity intelligence
- **Backward Compatible**: Existing systems continue to work unchanged
- **Full Data Integrity**: Every field from original Level-1 files is maintained

#### Grammatical Enhancement
- **16-Symbol Classification**: Maps market events to grammatical alphabet
- **Pattern Recognition**: Extracts event sequences and cascade predictions
- **Context Enhancement**: Cross-session influence analysis and probability scaling

#### Intelligent Event Classification
- **FPFVG Lifecycle**: Formation → Rebalance → Redelivery sequences
- **Price Movement Analysis**: Consolidation → Expansion → Retracement patterns
- **Liquidity Event Mapping**: Takeouts, sweeps, and volume spikes
- **Cross-Session References**: Historical and previous-day influence detection

### Usage

#### Single File Processing
```bash
python processors/lvl1_to_dual_layer_processor.py input_file.json output_file.json
```

#### Batch Processing
```bash
python processors/lvl1_to_dual_layer_processor.py /path/to/level1/files/ /path/to/enhanced/output/
```

#### Python API
```python
from processors.lvl1_to_dual_layer_processor import LevelOneToGrammaticalProcessor

processor = LevelOneToGrammaticalProcessor()
enhanced_data = processor.process_file("input.json", "output.json")
```

### Processing Pipeline

#### 1. Data Preservation (Layer 1)
```python
# All existing data preserved exactly
dual_layer_data = {
    "level1_json": original_data,  # ZERO changes
    "grammatical_intelligence": {},  # NEW enhancement layer
    "processing_metadata": {}
}
```

#### 2. Event Classification (Layer 2)
- **Session Opening**: Mapped to `OPEN` event
- **FPFVG Formation**: Classified as `FPFVG_FORMATION`
- **FPFVG Interactions**: `REDELIVERY`, `REBALANCE`, `TAKEOUT`
- **Price Movements**: `EXPANSION`, `CONSOLIDATION`, `RETRACEMENT`, `REVERSAL`
- **Liquidity Events**: `LIQUIDITY_SWEEP`, `VOLUME_SPIKE`

#### 3. Sequence Extraction
- **Chronological Sequences**: Full session event timeline
- **FPFVG Lifecycles**: Formation through completion patterns
- **Pattern Matching**: Known high-probability sequences

#### 4. Pattern Analysis
- **Active Patterns**: Incomplete sequences with completion predictions
- **Cascade Analysis**: Probability and timing estimates
- **Context Enhancement**: Cross-session influence scaling

### Output Structure

#### Enhanced Dual-Layer Format
```json
{
  "level1_json": {
    // ALL EXISTING DATA PRESERVED EXACTLY
    "session_metadata": {...},
    "session_fpfvg": {...},
    "session_liquidity_events": [...],
    "contamination_analysis": {...}
  },
  "grammatical_intelligence": {
    "event_classification": [
      {
        "event_type": "REDELIVERY",
        "timestamp": "12:09:00", 
        "price": 23173.00,
        "session_time_minutes": 9.0,
        "liquidity_context": {
          "fpfvg_interaction": "lunch_fpfvg_redelivery",
          "cross_session_influence": "none",
          "magnitude": "medium"
        },
        "pattern_context": {
          "completion_probability": 0.87,
          "cascade_trigger_potential": 0.92
        }
      }
    ],
    "event_sequences": [...],
    "pattern_analysis": {...}
  },
  "processing_metadata": {
    "schema_version": "dual_layer_v1.0",
    "layer1_preserved": true,
    "grammatical_enhancement": true
  }
}
```

### Pattern Recognition Examples

#### FPFVG Lifecycle Pattern
```
FPFVG_FORMATION → REBALANCE → REDELIVERY
Completion Probability: 87%
Time Window: 5-45 minutes
```

#### Consolidation Breakout Pattern
```
CONSOLIDATION → EXPANSION → RETRACEMENT
Completion Probability: 82%
Time Window: 3-20 minutes
```

#### Cascade Trigger Pattern
```
REDELIVERY → TAKEOUT
Completion Probability: 94%
Time Window: 1-5 minutes
```

### Cross-Session Enhancement

#### Influence Multipliers
- **Native Session**: 1.0x baseline probability
- **Immediate Cross-Session**: 1.2x probability boost
- **Previous Day Reference**: 1.4x probability boost  
- **Historical Reference**: 1.6x probability boost

#### Context Detection
- Automatically detects cross-session references in event contexts
- Identifies historical depth (same-day, previous-day, multi-day)
- Applies appropriate probability scaling based on temporal distance

### Validation Features

#### Input Validation
- Verifies Level-1 JSON structure integrity
- Checks for required fields and data completeness
- Handles both wrapped (`level1_json`) and direct data formats

#### Output Validation
- Ensures dual-layer schema compliance
- Validates grammatical event classification
- Confirms pattern sequence logical consistency

#### Processing Statistics
- Tracks files processed, events classified, patterns detected
- Reports validation errors and processing failures
- Provides detailed batch processing summaries

### Error Handling

#### Graceful Degradation
- Continues processing when individual events fail classification
- Maintains partial results for incomplete pattern detection
- Provides detailed error reporting for debugging

#### Data Recovery
- Preserves all original data even if enhancement fails
- Maintains file structure integrity under error conditions
- Allows reprocessing with enhanced logic

### Integration

#### Legacy System Compatibility
```python
# Existing systems can continue unchanged
enhanced_data = load_dual_layer_file()
original_data = enhanced_data["level1_json"]
# All existing code works exactly as before
```

#### Enhanced System Access
```python
# New systems get both layers
enhanced_data = load_dual_layer_file()
events = enhanced_data["grammatical_intelligence"]["event_classification"]
patterns = enhanced_data["grammatical_intelligence"]["pattern_analysis"]
# Access to grammatical intelligence for enhanced predictions
```

This processor enables the revolutionary shift from statistical to grammatical market analysis while maintaining complete backward compatibility and zero information loss.