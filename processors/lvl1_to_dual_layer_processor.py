#!/usr/bin/env python3
"""
Level-1 to Dual-Layer Architecture Processor
Converts existing Lvl-1 JSON files to enhanced dual-layer format with grammatical intelligence.

Key Features:
- ZERO INFORMATION LOSS: Preserves all existing data in Layer 1
- GRAMMATICAL ENHANCEMENT: Adds Layer 2 event classification
- PATTERN RECOGNITION: Extracts event sequences and cascade predictions
- VALIDATION: Ensures schema compliance and data integrity
"""

import json
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple, Optional
import re
from pathlib import Path

class LevelOneToGrammaticalProcessor:
    """Convert Level-1 JSON files to dual-layer architecture format."""
    
    def __init__(self, schema_path: str = None):
        """Initialize processor with schema validation capability."""
        self.schema_path = schema_path
        self.processing_stats = {
            "files_processed": 0,
            "events_classified": 0,
            "patterns_detected": 0,
            "validation_errors": 0
        }
        
        # Load grammatical event definitions
        self.event_definitions = self._load_event_definitions()
        
        # Pattern recognition templates
        self.pattern_templates = self._initialize_pattern_templates()
        
    def _load_event_definitions(self) -> Dict[str, Any]:
        """Load grammatical event definitions from schema."""
        try:
            schema_dir = Path(__file__).parent.parent / "schema"
            events_file = schema_dir / "grammatical_events.json"
            
            if events_file.exists():
                with open(events_file, 'r') as f:
                    return json.load(f)
            else:
                print(f"Warning: Event definitions file not found at {events_file}")
                return self._get_default_event_definitions()
        except Exception as e:
            print(f"Error loading event definitions: {e}")
            return self._get_default_event_definitions()
    
    def _get_default_event_definitions(self) -> Dict[str, Any]:
        """Default event definitions if schema file not available."""
        return {
            "definitions": {
                "core_grammar_symbols": [
                    "OPEN", "CONSOLIDATION", "EXPANSION", "EXPANSION_HIGH", 
                    "EXPANSION_LOW", "RETRACEMENT", "REDELIVERY", "FPFVG_FORMATION", "REVERSAL"
                ],
                "liquidity_enhanced_symbols": [
                    "REBALANCE", "INTERACTION", "TAKEOUT", "LIQUIDITY_GRAB", 
                    "LIQUIDITY_SWEEP", "VOLUME_SPIKE", "CLOSE"
                ]
            }
        }
    
    def _initialize_pattern_templates(self) -> Dict[str, Dict]:
        """Initialize pattern recognition templates."""
        return {
            "fpfvg_lifecycle": {
                "sequence": ["FPFVG_FORMATION", "REBALANCE", "REDELIVERY"],
                "completion_probability": 0.87,
                "time_window_minutes": 45
            },
            "consolidation_breakout": {
                "sequence": ["CONSOLIDATION", "EXPANSION", "REDELIVERY"],
                "completion_probability": 0.82,
                "time_window_minutes": 20
            },
            "liquidity_grab_reversal": {
                "sequence": ["LIQUIDITY_GRAB", "REVERSAL", "EXPANSION"],
                "completion_probability": 0.79,
                "time_window_minutes": 15
            },
            "cascade_trigger": {
                "sequence": ["REDELIVERY", "TAKEOUT"],
                "completion_probability": 0.94,
                "time_window_minutes": 5
            }
        }
    
    def process_file(self, input_path: str, output_path: str = None) -> Dict[str, Any]:
        """Process a single Level-1 JSON file to dual-layer format."""
        try:
            # Load existing Level-1 data
            with open(input_path, 'r') as f:
                level1_data = json.load(f)
            
            # Validate input structure
            if not self._validate_level1_structure(level1_data):
                raise ValueError(f"Invalid Level-1 structure in {input_path}")
            
            # Create dual-layer structure
            dual_layer_data = self._create_dual_layer_structure(level1_data)
            
            # Add grammatical intelligence
            grammatical_intelligence = self._extract_grammatical_intelligence(level1_data)
            dual_layer_data["grammatical_intelligence"] = grammatical_intelligence
            
            # Add processing metadata
            dual_layer_data["processing_metadata"] = self._create_processing_metadata()
            
            # Validate output structure
            if not self._validate_dual_layer_structure(dual_layer_data):
                raise ValueError("Generated dual-layer structure failed validation")
            
            # Save output file
            if output_path:
                with open(output_path, 'w') as f:
                    json.dump(dual_layer_data, f, indent=2)
                print(f"✅ Processed: {input_path} → {output_path}")
            
            # Update statistics
            self.processing_stats["files_processed"] += 1
            
            return dual_layer_data
            
        except Exception as e:
            self.processing_stats["validation_errors"] += 1
            print(f"❌ Error processing {input_path}: {e}")
            raise
    
    def _validate_level1_structure(self, data: Dict[str, Any]) -> bool:
        """Validate Level-1 JSON structure."""
        try:
            # Check for level1_json wrapper or direct structure
            if "level1_json" in data:
                level1 = data["level1_json"]
            else:
                level1 = data
            
            required_fields = ["session_metadata", "session_fpfvg", "session_liquidity_events"]
            return all(field in level1 for field in required_fields)
        except:
            return False
    
    def _create_dual_layer_structure(self, level1_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create dual-layer structure preserving all Level-1 data."""
        # LAYER 1: Preserve ALL existing data (zero information loss)
        if "level1_json" in level1_data:
            # Data already wrapped in level1_json
            layer1 = level1_data["level1_json"]
        else:
            # Wrap direct data in level1_json structure
            layer1 = level1_data
        
        return {
            "level1_json": layer1,
            "grammatical_intelligence": {},  # Will be populated separately
            "processing_metadata": {}  # Will be populated separately
        }
    
    def _extract_grammatical_intelligence(self, level1_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and classify grammatical events from Level-1 data."""
        
        # Get the actual Level-1 data structure
        if "level1_json" in level1_data:
            l1_data = level1_data["level1_json"]
        else:
            l1_data = level1_data
        
        # Extract event classification
        event_classification = self._classify_events(l1_data)
        
        # Extract event sequences
        event_sequences = self._extract_event_sequences(event_classification)
        
        # Perform pattern analysis
        pattern_analysis = self._analyze_patterns(event_classification, event_sequences)
        
        self.processing_stats["events_classified"] += len(event_classification)
        self.processing_stats["patterns_detected"] += len(pattern_analysis.get("active_patterns", []))
        
        return {
            "event_classification": event_classification,
            "event_sequences": event_sequences,
            "pattern_analysis": pattern_analysis
        }
    
    def _classify_events(self, level1_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Classify Level-1 data into grammatical events."""
        events = []
        
        # Session metadata for time calculations
        session_start = level1_data.get("session_metadata", {}).get("session_start", "00:00:00")
        
        # 1. Process session opening
        events.append(self._create_event(
            "OPEN",
            session_start,
            level1_data.get("price_movements", [{}])[0].get("price_level", 0.0),
            0.0,
            {"source": "session_open", "context": "session_initiation"}
        ))
        
        # 2. Process FPFVG formations
        fpfvg_data = level1_data.get("session_fpfvg", {})
        if fpfvg_data.get("fpfvg_present", False):
            fpfvg_formation = fpfvg_data.get("fpfvg_formation", {})
            
            # FPFVG formation event
            formation_time = fpfvg_formation.get("formation_time", session_start)
            premium_high = fpfvg_formation.get("premium_high", 0.0)
            
            events.append(self._create_event(
                "FPFVG_FORMATION",
                formation_time,
                premium_high,
                self._time_to_minutes(formation_time, session_start),
                {
                    "gap_size": fpfvg_formation.get("gap_size", 0.0),
                    "premium_high": premium_high,
                    "discount_low": fpfvg_formation.get("discount_low", 0.0)
                }
            ))
            
            # Process FPFVG interactions
            for interaction in fpfvg_formation.get("interactions", []):
                event_type = self._map_interaction_to_event_type(interaction.get("interaction_type"))
                
                events.append(self._create_event(
                    event_type,
                    interaction.get("interaction_time", formation_time),
                    interaction.get("price_level", 0.0),
                    self._time_to_minutes(interaction.get("interaction_time", formation_time), session_start),
                    {
                        "fpfvg_interaction": interaction.get("interaction_type"),
                        "context": interaction.get("interaction_context", ""),
                        "source_reference": "session_fpfvg"
                    }
                ))
        
        # 3. Process price movements for expansions, consolidations, reversals
        price_movements = level1_data.get("price_movements", [])
        events.extend(self._classify_price_movements(price_movements, session_start))
        
        # 4. Process liquidity events
        liquidity_events = level1_data.get("session_liquidity_events", [])
        events.extend(self._classify_liquidity_events(liquidity_events, session_start))
        
        # Sort events by time
        events.sort(key=lambda x: x.get("session_time_minutes", 0))
        
        return events
    
    def _create_event(self, event_type: str, timestamp: str, price: float, 
                     session_minutes: float, context: Dict[str, Any]) -> Dict[str, Any]:
        """Create a standardized grammatical event."""
        return {
            "event_type": event_type,
            "timestamp": timestamp,
            "price": price,
            "session_time_minutes": session_minutes,
            "magnitude": context.get("magnitude", 1.0),
            "liquidity_context": {
                "source_reference": context.get("source_reference", ""),
                "interaction_type": context.get("fpfvg_interaction", ""),
                "cross_session_influence": self._determine_cross_session_influence(context),
                "magnitude": context.get("magnitude_level", "medium"),
                "fpfvg_interaction": context.get("fpfvg_interaction", ""),
                "context": context.get("context", "")
            },
            "pattern_context": {
                "completion_probability": self._estimate_completion_probability(event_type, context),
                "pattern_significance": self._calculate_pattern_significance(event_type, context),
                "cascade_trigger_potential": self._estimate_cascade_potential(event_type, context)
            }
        }
    
    def _map_interaction_to_event_type(self, interaction_type: str) -> str:
        """Map FPFVG interaction types to grammatical event types."""
        mapping = {
            "redelivery": "REDELIVERY",
            "rebalance": "REBALANCE", 
            "takeout": "TAKEOUT",
            "formation": "FPFVG_FORMATION",
            "interaction": "INTERACTION"
        }
        return mapping.get(interaction_type, "INTERACTION")
    
    def _classify_price_movements(self, movements: List[Dict], session_start: str) -> List[Dict[str, Any]]:
        """Classify price movements into grammatical events."""
        events = []
        
        for movement in movements:
            movement_type = movement.get("movement_type", "")
            
            # Map movement types to grammatical events
            if "expansion" in movement_type.lower():
                if "high" in movement_type.lower():
                    event_type = "EXPANSION_HIGH"
                elif "low" in movement_type.lower():
                    event_type = "EXPANSION_LOW"
                else:
                    event_type = "EXPANSION"
            elif "consolidation" in movement_type.lower():
                event_type = "CONSOLIDATION"
            elif "retracement" in movement_type.lower():
                event_type = "RETRACEMENT"
            elif "reversal" in movement_type.lower():
                event_type = "REVERSAL"
            elif "close" in movement_type.lower():
                event_type = "CLOSE"
            else:
                continue  # Skip unmapped movements
            
            events.append(self._create_event(
                event_type,
                movement.get("timestamp", session_start),
                movement.get("price_level", 0.0),
                self._time_to_minutes(movement.get("timestamp", session_start), session_start),
                {
                    "source_reference": "price_movements",
                    "movement_type": movement_type,
                    "contamination_risk": movement.get("contamination_risk", False)
                }
            ))
        
        return events
    
    def _classify_liquidity_events(self, events: List[Dict], session_start: str) -> List[Dict[str, Any]]:
        """Classify liquidity events into grammatical events."""
        classified_events = []
        
        for event in events:
            event_type = event.get("event_type", "")
            
            # Map liquidity event types to grammatical events
            if event_type == "takeout":
                gram_type = "TAKEOUT"
            elif event_type == "sweep":
                gram_type = "LIQUIDITY_SWEEP"
            elif event_type == "redelivery":
                gram_type = "REDELIVERY"
            elif event_type == "rebalance":
                gram_type = "REBALANCE"
            else:
                gram_type = "INTERACTION"
            
            classified_events.append(self._create_event(
                gram_type,
                event.get("timestamp", session_start),
                0.0,  # Liquidity events may not have explicit price
                self._time_to_minutes(event.get("timestamp", session_start), session_start),
                {
                    "source_reference": "session_liquidity_events",
                    "liquidity_type": event.get("liquidity_type", ""),
                    "target_level": event.get("target_level", ""),
                    "magnitude_level": event.get("magnitude", "medium"),
                    "context": event.get("context", "")
                }
            ))
        
        return classified_events
    
    def _extract_event_sequences(self, events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract event sequences for pattern recognition."""
        sequences = []
        
        # Create basic chronological sequence
        event_types = [event["event_type"] for event in events]
        
        if event_types:
            sequences.append({
                "sequence_id": "chronological_full",
                "events": event_types,
                "pattern_type": "Type-2",
                "completion_status": "complete",
                "completion_probability": 1.0,
                "time_window": {
                    "start_time": events[0]["timestamp"],
                    "end_time": events[-1]["timestamp"],
                    "duration_minutes": events[-1]["session_time_minutes"] - events[0]["session_time_minutes"]
                }
            })
        
        # Extract FPFVG lifecycle sequences
        fpfvg_sequences = self._extract_fpfvg_sequences(events)
        sequences.extend(fpfvg_sequences)
        
        # Extract pattern-based sequences
        pattern_sequences = self._extract_pattern_sequences(events)
        sequences.extend(pattern_sequences)
        
        return sequences
    
    def _extract_fpfvg_sequences(self, events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract FPFVG lifecycle sequences."""
        sequences = []
        
        # Find FPFVG formation events
        fpfvg_formations = [e for e in events if e["event_type"] == "FPFVG_FORMATION"]
        
        for formation in fpfvg_formations:
            # Find related interactions within time window
            formation_time = formation["session_time_minutes"]
            related_events = []
            
            for event in events:
                if (event["session_time_minutes"] >= formation_time and
                    event["session_time_minutes"] <= formation_time + 45 and  # 45-minute window
                    event["event_type"] in ["FPFVG_FORMATION", "REBALANCE", "REDELIVERY", "TAKEOUT"]):
                    related_events.append(event)
            
            if len(related_events) > 1:
                sequences.append({
                    "sequence_id": f"fpfvg_lifecycle_{formation_time}",
                    "events": [e["event_type"] for e in related_events],
                    "pattern_type": "Type-2",
                    "completion_status": self._determine_completion_status(related_events),
                    "completion_probability": self._calculate_sequence_probability(related_events),
                    "time_window": {
                        "start_time": related_events[0]["timestamp"],
                        "end_time": related_events[-1]["timestamp"],
                        "duration_minutes": related_events[-1]["session_time_minutes"] - related_events[0]["session_time_minutes"]
                    }
                })
        
        return sequences
    
    def _extract_pattern_sequences(self, events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract sequences matching known patterns."""
        sequences = []
        
        for pattern_name, pattern_info in self.pattern_templates.items():
            pattern_events = pattern_info["sequence"]
            matches = self._find_pattern_matches(events, pattern_events, pattern_info["time_window_minutes"])
            
            for match in matches:
                sequences.append({
                    "sequence_id": f"{pattern_name}_{match['start_time']}",
                    "events": pattern_events,
                    "pattern_type": "Type-2",
                    "completion_status": "complete" if len(match["events"]) == len(pattern_events) else "partial",
                    "completion_probability": pattern_info["completion_probability"],
                    "time_window": {
                        "start_time": match["events"][0]["timestamp"],
                        "end_time": match["events"][-1]["timestamp"],
                        "duration_minutes": match["events"][-1]["session_time_minutes"] - match["events"][0]["session_time_minutes"]
                    }
                })
        
        return sequences
    
    def _analyze_patterns(self, events: List[Dict[str, Any]], sequences: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze patterns and generate cascade predictions."""
        
        # Find active (incomplete) patterns
        active_patterns = []
        for sequence in sequences:
            if sequence["completion_status"] in ["incomplete", "partial"]:
                active_patterns.append({
                    "pattern_name": sequence["sequence_id"],
                    "pattern_type": sequence["pattern_type"],
                    "completion_percentage": self._calculate_completion_percentage(sequence),
                    "expected_completion": self._predict_next_event(sequence),
                    "cascade_probability": sequence["completion_probability"]
                })
        
        # Generate completion expectations
        completion_expectations = []
        for pattern in active_patterns:
            completion_expectations.append({
                "expected_event": pattern["expected_completion"],
                "probability": pattern["cascade_probability"],
                "time_window": "5-15 minutes",
                "conditions": ["pattern_completion", "liquidity_availability"]
            })
        
        # Analyze cascade potential
        cascade_analysis = self._analyze_cascade_potential(events, sequences)
        
        return {
            "active_patterns": active_patterns,
            "completion_expectations": completion_expectations,
            "cascade_analysis": cascade_analysis
        }
    
    def _analyze_cascade_potential(self, events: List[Dict[str, Any]], sequences: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze overall cascade potential."""
        
        # Count high-potential events
        high_cascade_events = [e for e in events if e["pattern_context"]["cascade_trigger_potential"] > 0.8]
        
        # Calculate overall cascade probability
        cascade_probability = 0.0
        if events:
            avg_cascade_potential = sum(e["pattern_context"]["cascade_trigger_potential"] for e in events) / len(events)
            cascade_probability = min(0.95, avg_cascade_potential * 1.2)  # Cap at 95%
        
        # Determine cascade type based on event types and magnitudes
        cascade_type = "micro"
        if any(e["event_type"] in ["TAKEOUT", "LIQUIDITY_SWEEP"] for e in high_cascade_events):
            cascade_type = "major"
        elif any(e["event_type"] == "REDELIVERY" for e in events):
            cascade_type = "standard"
        
        return {
            "cascade_probability": cascade_probability,
            "cascade_type": cascade_type,
            "trigger_events": [e["event_type"] for e in high_cascade_events],
            "timing_prediction": "3-15 minutes",
            "magnitude_estimate": len(high_cascade_events) * 15.0  # Rough point estimate
        }
    
    # Utility methods
    
    def _time_to_minutes(self, time_str: str, session_start: str) -> float:
        """Convert time string to minutes from session start."""
        try:
            def time_to_seconds(t_str):
                parts = t_str.split(":")
                return int(parts[0]) * 3600 + int(parts[1]) * 60 + (int(parts[2]) if len(parts) > 2 else 0)
            
            start_seconds = time_to_seconds(session_start)
            event_seconds = time_to_seconds(time_str)
            
            # Handle day rollover for overnight sessions
            if event_seconds < start_seconds:
                event_seconds += 24 * 3600
            
            return (event_seconds - start_seconds) / 60.0
        except:
            return 0.0
    
    def _determine_cross_session_influence(self, context: Dict[str, Any]) -> str:
        """Determine cross-session influence level."""
        context_str = str(context).lower()
        
        if "historical" in context_str or "three_day" in context_str:
            return "historical"
        elif "previous_day" in context_str:
            return "previous_day"
        elif "cross_session" in context_str or any(session in context_str for session in ["asia", "london", "midnight"]):
            return "immediate"
        else:
            return "none"
    
    def _estimate_completion_probability(self, event_type: str, context: Dict[str, Any]) -> float:
        """Estimate pattern completion probability."""
        base_probabilities = {
            "FPFVG_FORMATION": 0.87,
            "REDELIVERY": 0.92,
            "TAKEOUT": 0.94,
            "EXPANSION": 0.75,
            "CONSOLIDATION": 0.70
        }
        
        base_prob = base_probabilities.get(event_type, 0.50)
        
        # Adjust for cross-session influence
        influence = self._determine_cross_session_influence(context)
        multipliers = {"historical": 1.6, "previous_day": 1.4, "immediate": 1.2, "none": 1.0}
        
        return min(0.95, base_prob * multipliers.get(influence, 1.0))
    
    def _calculate_pattern_significance(self, event_type: str, context: Dict[str, Any]) -> float:
        """Calculate pattern significance score."""
        significance_scores = {
            "REDELIVERY": 0.9,
            "TAKEOUT": 0.95,
            "FPFVG_FORMATION": 0.8,
            "LIQUIDITY_SWEEP": 0.85,
            "REVERSAL": 0.9
        }
        
        return significance_scores.get(event_type, 0.5)
    
    def _estimate_cascade_potential(self, event_type: str, context: Dict[str, Any]) -> float:
        """Estimate cascade trigger potential."""
        cascade_potentials = {
            "TAKEOUT": 0.94,
            "REDELIVERY": 0.87,
            "LIQUIDITY_SWEEP": 0.89,
            "REVERSAL": 0.85,
            "EXPANSION_HIGH": 0.70,
            "EXPANSION_LOW": 0.70
        }
        
        return cascade_potentials.get(event_type, 0.30)
    
    def _determine_completion_status(self, events: List[Dict[str, Any]]) -> str:
        """Determine if event sequence is complete."""
        event_types = [e["event_type"] for e in events]
        
        # FPFVG lifecycle completion check
        if "FPFVG_FORMATION" in event_types:
            if "REDELIVERY" in event_types or "TAKEOUT" in event_types:
                return "complete"
            elif "REBALANCE" in event_types:
                return "partial"
            else:
                return "incomplete"
        
        return "complete"
    
    def _calculate_sequence_probability(self, events: List[Dict[str, Any]]) -> float:
        """Calculate probability for event sequence."""
        if not events:
            return 0.0
        
        # Average the completion probabilities of constituent events
        probabilities = [e["pattern_context"]["completion_probability"] for e in events]
        return sum(probabilities) / len(probabilities)
    
    def _find_pattern_matches(self, events: List[Dict[str, Any]], pattern: List[str], time_window: int) -> List[Dict]:
        """Find matches for specific pattern within time window."""
        matches = []
        
        for i, event in enumerate(events):
            if event["event_type"] == pattern[0]:
                # Found potential start of pattern
                match_events = [event]
                
                # Look for remaining pattern events within time window
                for j in range(i + 1, len(events)):
                    next_event = events[j]
                    
                    # Check if within time window
                    if next_event["session_time_minutes"] - event["session_time_minutes"] > time_window:
                        break
                    
                    # Check if matches next expected event in pattern
                    if (len(match_events) < len(pattern) and 
                        next_event["event_type"] == pattern[len(match_events)]):
                        match_events.append(next_event)
                
                # If we found a significant portion of the pattern
                if len(match_events) >= 2:
                    matches.append({
                        "start_time": event["timestamp"],
                        "events": match_events
                    })
        
        return matches
    
    def _calculate_completion_percentage(self, sequence: Dict[str, Any]) -> float:
        """Calculate pattern completion percentage."""
        # Simple implementation - can be enhanced
        if sequence["completion_status"] == "complete":
            return 100.0
        elif sequence["completion_status"] == "partial":
            return 60.0
        else:
            return 30.0
    
    def _predict_next_event(self, sequence: Dict[str, Any]) -> str:
        """Predict next event in sequence."""
        # Simple implementation - can be enhanced with pattern templates
        current_events = sequence["events"]
        
        if "FPFVG_FORMATION" in current_events and "REDELIVERY" not in current_events:
            return "REDELIVERY"
        elif "CONSOLIDATION" in current_events and "EXPANSION" not in current_events:
            return "EXPANSION"
        elif "EXPANSION" in current_events and "RETRACEMENT" not in current_events:
            return "RETRACEMENT"
        else:
            return "CONTINUATION"
    
    def _create_processing_metadata(self) -> Dict[str, Any]:
        """Create processing metadata."""
        return {
            "schema_version": "dual_layer_v1.0",
            "processing_timestamp": datetime.now().isoformat(),
            "layer1_preserved": True,
            "grammatical_enhancement": True,
            "validation_status": "validated",
            "compatibility": {
                "legacy_systems": True,
                "grammatical_parser": True,
                "prediction_engine": True
            }
        }
    
    def _validate_dual_layer_structure(self, data: Dict[str, Any]) -> bool:
        """Validate dual-layer structure."""
        try:
            required_keys = ["level1_json", "grammatical_intelligence", "processing_metadata"]
            if not all(key in data for key in required_keys):
                return False
            
            # Validate grammatical intelligence structure
            gi = data["grammatical_intelligence"]
            gi_required = ["event_classification", "event_sequences", "pattern_analysis"]
            if not all(key in gi for key in gi_required):
                return False
            
            return True
        except:
            return False
    
    def process_batch(self, input_directory: str, output_directory: str, 
                     file_pattern: str = "*_Lvl-1_*.json") -> Dict[str, Any]:
        """Process batch of Level-1 files."""
        input_path = Path(input_directory)
        output_path = Path(output_directory)
        
        # Create output directory if it doesn't exist
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Find matching files
        input_files = list(input_path.glob(file_pattern))
        
        results = {
            "processed_files": [],
            "failed_files": [],
            "statistics": {}
        }
        
        for input_file in input_files:
            try:
                # Generate output filename
                output_file = output_path / f"enhanced_{input_file.name}"
                
                # Process file
                self.process_file(str(input_file), str(output_file))
                results["processed_files"].append(str(input_file))
                
            except Exception as e:
                results["failed_files"].append({"file": str(input_file), "error": str(e)})
                print(f"❌ Failed to process {input_file}: {e}")
        
        # Add final statistics
        results["statistics"] = self.processing_stats.copy()
        
        return results

def main():
    """Main function for command-line usage."""
    if len(sys.argv) < 2:
        print("Usage: python lvl1_to_dual_layer_processor.py <input_file_or_directory> [output_file_or_directory]")
        sys.exit(1)
    
    input_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    processor = LevelOneToGrammaticalProcessor()
    
    if os.path.isdir(input_path):
        # Batch processing
        if not output_path:
            output_path = os.path.join(input_path, "enhanced")
        
        results = processor.process_batch(input_path, output_path)
        
        print(f"\n📊 Batch Processing Results:")
        print(f"✅ Processed: {len(results['processed_files'])} files")
        print(f"❌ Failed: {len(results['failed_files'])} files")
        print(f"📈 Events classified: {results['statistics']['events_classified']}")
        print(f"🔍 Patterns detected: {results['statistics']['patterns_detected']}")
        
    else:
        # Single file processing
        if not output_path:
            output_path = input_path.replace(".json", "_enhanced.json")
        
        try:
            result = processor.process_file(input_path, output_path)
            print(f"✅ Successfully processed {input_path}")
        except Exception as e:
            print(f"❌ Processing failed: {e}")
            sys.exit(1)

if __name__ == "__main__":
    main()