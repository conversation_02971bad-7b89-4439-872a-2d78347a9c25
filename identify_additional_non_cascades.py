#!/usr/bin/env python3
"""
Identify Additional Non-Cascades

Analyzes unknown sessions to find additional non-cascade candidates
based on enhanced heuristics and activity patterns.
"""

import json
from pathlib import Path
from typing import Dict, List, <PERSON><PERSON>

def analyze_session_for_non_cascade(file_path: Path) -> Tuple[str, Dict]:
    """Analyze a session file for non-cascade indicators."""
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        # Extract key metrics
        cascade_events = data.get('micro_timing_analysis', {}).get('cascade_events', [])
        price_movements = data.get('price_movements', [])
        session_metadata = data.get('session_metadata', {})
        session_analysis = data.get('session_analysis', {})
        
        session_type = session_metadata.get('session_type', '').lower()
        institutional_activity = session_analysis.get('institutional_activity', '').lower()
        
        # Calculate metrics
        num_cascade_events = len(cascade_events)
        num_price_movements = len(price_movements)
        
        price_range = 0
        if price_movements:
            prices = [pm.get('price_level', 0) for pm in price_movements if pm.get('price_level')]
            if prices:
                price_range = max(prices) - min(prices)
        
        # Non-cascade scoring
        non_cascade_score = 0
        reasons = []
        
        # Strong indicators (high weight)
        if num_cascade_events == 0:
            non_cascade_score += 3
            reasons.append("no_cascade_events")
        
        # Session type indicators
        if session_type in ['lunch', 'midnight']:
            non_cascade_score += 2
            reasons.append(f"low_activity_session_type_{session_type}")
        elif session_type == 'premarket' and num_price_movements <= 12:
            non_cascade_score += 2
            reasons.append("low_activity_premarket")
        
        # Activity level indicators
        if 'minimal_activity' in institutional_activity or 'low_activity' in institutional_activity:
            non_cascade_score += 2
            reasons.append("minimal_institutional_activity")
        
        if 'tight_range' in institutional_activity or 'consolidation' in institutional_activity:
            non_cascade_score += 1
            reasons.append("tight_range_consolidation")
        
        # Price movement indicators
        if num_price_movements <= 6:
            non_cascade_score += 2
            reasons.append("very_few_price_movements")
        elif num_price_movements <= 10:
            non_cascade_score += 1
            reasons.append("few_price_movements")
        
        # Price range indicators
        if price_range < 15:
            non_cascade_score += 2
            reasons.append("very_tight_price_range")
        elif price_range < 30:
            non_cascade_score += 1
            reasons.append("tight_price_range")
        
        # Determine classification
        if non_cascade_score >= 4:
            classification = "non_cascade"
        elif non_cascade_score >= 2:
            classification = "likely_non_cascade"
        else:
            classification = "unknown"
        
        return classification, {
            'score': non_cascade_score,
            'reasons': reasons,
            'metrics': {
                'cascade_events': num_cascade_events,
                'price_movements': num_price_movements,
                'price_range': price_range,
                'session_type': session_type,
                'institutional_activity': institutional_activity
            }
        }
        
    except Exception as e:
        return "error", {"error": str(e)}

def main():
    # Load current manifest
    with open('data_manifest_enhanced.json', 'r') as f:
        manifest = json.load(f)
    
    # Find unknown sessions
    unknown_sessions = [item for item in manifest['items'] 
                       if item['label'] == 'unknown' and item['valid']]
    
    print(f"Analyzing {len(unknown_sessions)} unknown sessions...")
    
    candidates = []
    likely_candidates = []
    
    for item in unknown_sessions:
        file_path = Path(item['path'])
        if file_path.exists():
            classification, analysis = analyze_session_for_non_cascade(file_path)
            
            if classification == "non_cascade":
                candidates.append({
                    'path': str(file_path),
                    'session_type': item.get('session_type'),
                    'session_date': item.get('session_date'),
                    'analysis': analysis
                })
            elif classification == "likely_non_cascade":
                likely_candidates.append({
                    'path': str(file_path),
                    'session_type': item.get('session_type'),
                    'session_date': item.get('session_date'),
                    'analysis': analysis
                })
    
    print(f"\nFound {len(candidates)} strong non-cascade candidates:")
    for candidate in candidates:
        print(f"  {candidate['path']}")
        print(f"    Type: {candidate['session_type']}, Date: {candidate['session_date']}")
        print(f"    Score: {candidate['analysis']['score']}, Reasons: {candidate['analysis']['reasons']}")
        print()
    
    print(f"\nFound {len(likely_candidates)} likely non-cascade candidates:")
    for candidate in likely_candidates:
        print(f"  {candidate['path']}")
        print(f"    Type: {candidate['session_type']}, Date: {candidate['session_date']}")
        print(f"    Score: {candidate['analysis']['score']}, Reasons: {candidate['analysis']['reasons']}")
        print()
    
    # Generate label overrides
    overrides = {}
    for candidate in candidates:
        # Use file stem as key for override
        file_path = Path(candidate['path'])
        overrides[file_path.stem.lower()] = "non_cascade"
    
    # Add some high-confidence likely candidates
    high_confidence_likely = [c for c in likely_candidates if c['analysis']['score'] >= 3]
    for candidate in high_confidence_likely:
        file_path = Path(candidate['path'])
        overrides[file_path.stem.lower()] = "non_cascade"
    
    # Save overrides
    with open('additional_non_cascade_overrides.json', 'w') as f:
        json.dump(overrides, f, indent=2)
    
    total_additional = len(candidates) + len(high_confidence_likely)
    current_non_cascades = 23
    projected_total = current_non_cascades + total_additional
    
    print(f"\nSummary:")
    print(f"Current non-cascades: {current_non_cascades}")
    print(f"Additional candidates: {total_additional}")
    print(f"Projected total: {projected_total}")
    print(f"Target (≥30): {'✅ ACHIEVED' if projected_total >= 30 else '❌ NEED MORE'}")
    print(f"\nOverrides saved to: additional_non_cascade_overrides.json")

if __name__ == "__main__":
    main()
