#!/usr/bin/env python3
"""
Oracle Prediction API - FastAPI Integration
===========================================

Phase 1 FastAPI wrapper around the existing Oracle prediction system.
Exposes REST API endpoints while preserving the compartmentalized architecture.

Usage:
    uvicorn oracle_api:app --host 0.0.0.0 --port 8000 --reload
"""

import sys
import time
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uvicorn

# Add current directory to path for imports
sys.path.append('.')

# Import Oracle components
from compartments.predict import PredictCompartment
from compartments.calibration import CalibrationCompartment

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app instance
app = FastAPI(
    title="Oracle Prediction API",
    description="Three-Oracle cascade prediction system with Grammar Bridge intelligence",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Pydantic models for API requests/responses
class SessionData(BaseModel):
    """Input session data for predictions"""
    session_id: str = Field(..., description="Unique session identifier")
    session_type: str = Field(default="NYAM", description="Session type (NYAM, NYPM, LONDON, etc.)")
    session_date: str = Field(default="2025-08-09", description="Session date in YYYY-MM-DD format")
    price_movements: List[Dict[str, Any]] = Field(default=[], description="Price movement events")
    liquidity_events: List[Dict[str, Any]] = Field(default=[], description="Liquidity events")
    use_grammar_bridge: bool = Field(default=True, description="Use Grammar Bridge cascade events")

class PredictionResponse(BaseModel):
    """Oracle prediction response"""
    prediction_id: str
    session_id: str
    prediction_time: str
    oracle_choice: str
    final_prediction: float
    prediction_confidence: float
    timing_window: str
    pattern_type: str
    cascade_probability: float
    echo_strength: float
    virgin_prediction: float
    contaminated_prediction: float
    arbiter_reasoning: str
    metacognition_detected: bool
    latency_ms: float
    system_health: Dict[str, Any]

class HealthStatus(BaseModel):
    """System health status"""
    status: str
    timestamp: str
    components: Dict[str, str]
    version: str
    uptime_seconds: float

class SystemStats(BaseModel):
    """System statistics"""
    total_predictions: int
    avg_latency_ms: float
    success_rate: float
    grammar_bridge_events: int
    active_sessions: int

# Global state
app_start_time = time.time()
prediction_count = 0
total_latency = 0.0
error_count = 0

# Initialize Oracle components
predict_compartment = None
calibration_compartment = None

@app.on_event("startup")
async def startup_event():
    """Initialize Oracle components on startup"""
    global predict_compartment, calibration_compartment
    
    logger.info("🚀 Starting Oracle Prediction API")
    logger.info("=" * 50)
    
    try:
        # Initialize compartments
        predict_compartment = PredictCompartment()
        calibration_compartment = CalibrationCompartment()
        
        logger.info("✅ Oracle components initialized successfully")
        
        # Validate Grammar Bridge data
        grammar_bridge_file = Path("grammar_bridge/cascade_events.json")
        if grammar_bridge_file.exists():
            with open(grammar_bridge_file, 'r') as f:
                data = json.load(f)
            events_count = len(data.get('unified_cascade_events', []))
            logger.info(f"✅ Grammar Bridge: {events_count} cascade events available")
        else:
            logger.warning("⚠️ Grammar Bridge data not found")
            
    except Exception as e:
        logger.error(f"❌ Failed to initialize Oracle components: {e}")
        raise

@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint with API information"""
    return {
        "service": "Oracle Prediction API",
        "version": "1.0.0",
        "status": "operational",
        "docs": "/docs",
        "health": "/health"
    }

@app.get("/health", response_model=HealthStatus)
async def health_check():
    """System health check endpoint"""
    global app_start_time, predict_compartment, calibration_compartment
    
    uptime = time.time() - app_start_time
    
    # Check component health
    components = {}
    
    try:
        if predict_compartment:
            components["predict_compartment"] = "healthy"
        else:
            components["predict_compartment"] = "not_initialized"
            
        if calibration_compartment:
            components["calibration_compartment"] = "healthy"
        else:
            components["calibration_compartment"] = "not_initialized"
            
        # Check Grammar Bridge
        grammar_bridge_file = Path("grammar_bridge/cascade_events.json")
        if grammar_bridge_file.exists():
            components["grammar_bridge"] = "healthy"
        else:
            components["grammar_bridge"] = "missing_data"
            
        # Check enhanced sessions
        enhanced_dir = Path("enhanced_sessions")
        if enhanced_dir.exists() and list(enhanced_dir.glob("*.json")):
            components["enhanced_sessions"] = "healthy"
        else:
            components["enhanced_sessions"] = "missing_data"
            
    except Exception as e:
        logger.error(f"Health check error: {e}")
        components["error"] = str(e)
    
    # Determine overall status
    unhealthy_components = [k for k, v in components.items() if v not in ["healthy"]]
    overall_status = "healthy" if not unhealthy_components else "degraded"
    
    return HealthStatus(
        status=overall_status,
        timestamp=datetime.now().isoformat(),
        components=components,
        version="1.0.0",
        uptime_seconds=uptime
    )

@app.get("/stats", response_model=SystemStats)
async def get_stats():
    """Get system statistics"""
    global prediction_count, total_latency, error_count
    
    # Count Grammar Bridge events
    grammar_events = 0
    try:
        grammar_bridge_file = Path("grammar_bridge/cascade_events.json")
        if grammar_bridge_file.exists():
            with open(grammar_bridge_file, 'r') as f:
                data = json.load(f)
            grammar_events = len(data.get('unified_cascade_events', []))
    except:
        pass
    
    # Count active sessions
    active_sessions = 0
    try:
        enhanced_dir = Path("enhanced_sessions")
        if enhanced_dir.exists():
            active_sessions = len(list(enhanced_dir.glob("*.json")))
    except:
        pass
    
    avg_latency = (total_latency / prediction_count) if prediction_count > 0 else 0.0
    success_rate = ((prediction_count - error_count) / prediction_count) if prediction_count > 0 else 1.0
    
    return SystemStats(
        total_predictions=prediction_count,
        avg_latency_ms=avg_latency,
        success_rate=success_rate,
        grammar_bridge_events=grammar_events,
        active_sessions=active_sessions
    )

@app.post("/predict", response_model=PredictionResponse)
async def predict_cascade(session_data: SessionData, background_tasks: BackgroundTasks):
    """
    Main prediction endpoint using Three-Oracle system

    Accepts session data and returns cascade timing predictions using the
    complete Oracle architecture with Grammar Bridge intelligence.
    """
    global prediction_count, total_latency, error_count, predict_compartment

    start_time = time.time()
    prediction_count += 1

    try:
        logger.info(f"🔮 Processing prediction request for session {session_data.session_id}")

        if not predict_compartment:
            raise HTTPException(status_code=503, detail="Predict compartment not initialized")

        # Run calibration first (if needed)
        calibration_params = {}
        if calibration_compartment:
            try:
                calibration_result = calibration_compartment.run()
                if calibration_result and calibration_result.get('output'):
                    calibration_params = calibration_result['output'].get('calibration_params', {})
                    logger.info("✅ Calibration completed successfully")
            except Exception as e:
                logger.warning(f"⚠️ Calibration failed, using defaults: {e}")

        # Prepare session data for predict compartment
        # The predict compartment expects to load from enhanced_sessions directory
        # For API mode, we'll create a temporary session file or use direct data

        if session_data.use_grammar_bridge:
            # Use existing Grammar Bridge integration
            logger.info("🌉 Using Grammar Bridge cascade events")

            # Run predict compartment with calibration params
            prediction_result = predict_compartment._perform_real_predictions(calibration_params)

        else:
            # Use provided session data directly
            logger.info("📊 Using provided session data")

            # Convert API session data to internal format
            internal_session_data = {
                "session_id": session_data.session_id,
                "session_type": session_data.session_type,
                "session_date": session_data.session_date,
                "price_movements": session_data.price_movements,
                "micro_timing_analysis": {
                    "cascade_events": session_data.liquidity_events
                }
            }

            # Use fallback predictions for now
            prediction_result = predict_compartment._fallback_predictions(calibration_params)

        # Extract first prediction from result
        predictions = prediction_result.get('predictions', [])
        if not predictions:
            raise HTTPException(status_code=500, detail="No predictions generated")

        first_prediction = predictions[0]

        # Calculate latency
        latency_ms = (time.time() - start_time) * 1000
        total_latency += latency_ms

        # Create response
        response = PredictionResponse(
            prediction_id=first_prediction.get('prediction_id', f'pred_{prediction_count}'),
            session_id=session_data.session_id,
            prediction_time=datetime.now().isoformat(),
            oracle_choice=first_prediction.get('oracle_choice', 'unknown'),
            final_prediction=first_prediction.get('timing_window', '0.5min').replace('min', ''),
            prediction_confidence=first_prediction.get('confidence', 0.0),
            timing_window=first_prediction.get('timing_window', '0.5min'),
            pattern_type=first_prediction.get('pattern_type', 'unknown'),
            cascade_probability=first_prediction.get('cascade_probability', 0.0),
            echo_strength=first_prediction.get('echo_strength', 0.0),
            virgin_prediction=first_prediction.get('virgin_prediction', 0.0),
            contaminated_prediction=first_prediction.get('contaminated_prediction', 0.0),
            arbiter_reasoning=first_prediction.get('arbiter_reasoning', 'API prediction'),
            metacognition_detected=first_prediction.get('metacognition_detected', False),
            latency_ms=latency_ms,
            system_health=prediction_result.get('stats', {})
        )

        logger.info(f"✅ Prediction completed in {latency_ms:.2f}ms")
        return response

    except HTTPException:
        error_count += 1
        raise
    except Exception as e:
        error_count += 1
        logger.error(f"❌ Prediction failed: {e}")
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

@app.post("/predict/session/{session_id}")
async def predict_existing_session(session_id: str):
    """
    Predict using an existing enhanced session file

    Looks for enhanced session file and runs prediction using Grammar Bridge events.
    """
    global predict_compartment

    try:
        if not predict_compartment:
            raise HTTPException(status_code=503, detail="Predict compartment not initialized")

        # Check if enhanced session exists
        enhanced_file = Path(f"enhanced_sessions/enhanced_{session_id}.json")
        if not enhanced_file.exists():
            raise HTTPException(status_code=404, detail=f"Enhanced session {session_id} not found")

        logger.info(f"🔮 Predicting existing session: {session_id}")

        # Run calibration
        calibration_params = {}
        if calibration_compartment:
            try:
                calibration_result = calibration_compartment.run()
                if calibration_result and calibration_result.get('output'):
                    calibration_params = calibration_result['output'].get('calibration_params', {})
            except Exception as e:
                logger.warning(f"⚠️ Calibration failed: {e}")

        # Run prediction
        prediction_result = predict_compartment._perform_real_predictions(calibration_params)

        return JSONResponse(content={
            "session_id": session_id,
            "prediction_result": prediction_result,
            "timestamp": datetime.now().isoformat()
        })

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Session prediction failed: {e}")
        raise HTTPException(status_code=500, detail=f"Session prediction failed: {str(e)}")

if __name__ == "__main__":
    # Run the API server
    uvicorn.run(
        "oracle_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
