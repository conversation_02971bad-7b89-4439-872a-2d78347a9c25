#!/usr/bin/env python3
"""
Event Grammar Analyzer - Market Linguistics Discovery
====================================================

Phase 3 FIRST: Analyze existing session data to discover whether recorded 
events form grammatical cascade patterns that can predict market behavior.

This validates the fundamental assumption that manual recording captures
market "phonemes" - minimal meaningful units that form cascade "sentences".

Key Question: Do FPFVG, liquidity grabs, and redeliveries form predictive
event sequences that correlate with actual cascade occurrences?
"""

import json
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from collections import defaultdict, Counter
import itertools

@dataclass
class MarketEvent:
    """Individual market event extracted from session data"""
    timestamp: str
    event_type: str
    price_level: float
    magnitude: float
    context: str
    session_time_minutes: float

@dataclass
class EventSequence:
    """Sequence of events that may form cascade pattern"""
    events: List[MarketEvent]
    sequence_type: str
    duration_minutes: float
    total_magnitude: float
    cascade_occurred: bool
    confidence: float

@dataclass
class GrammarPattern:
    """Discovered grammatical pattern in event sequences"""
    pattern_signature: str
    event_types: List[str]
    typical_duration_minutes: float
    cascade_probability: float
    frequency_count: int
    example_sequences: List[EventSequence]

class EventGrammarAnalyzer:
    """
    Analyzes session data to discover event grammar patterns that predict cascades.
    
    This is the crucial first test: Do your recorded events contain sufficient
    linguistic structure to build a cascade prediction system?
    """
    
    def __init__(self):
        self.discovered_patterns: List[GrammarPattern] = []
        self.all_events: List[MarketEvent] = []
        self.event_sequences: List[EventSequence] = []
        
        # Event type mappings from your data
        self.event_type_mapping = {
            # FPFVG patterns
            'fpfvg_formation': 'FPFVG',
            'fpfvg_redelivery': 'REDELIVERY', 
            'fpfvg_interaction': 'INTERACTION',
            'multiple_fpfvg_redelivery': 'MULTI_REDELIVERY',
            
            # Liquidity patterns  
            'liquidity_grab': 'LIQUIDITY_GRAB',
            'liquidity_sweep': 'LIQUIDITY_SWEEP',
            'liquidity_raid': 'LIQUIDITY_RAID',
            
            # Movement patterns
            'expansion_start': 'EXPANSION',
            'expansion_low': 'EXPANSION_LOW',
            'expansion_high': 'EXPANSION_HIGH', 
            'reversal_point': 'REVERSAL',
            'consolidation': 'CONSOLIDATION',
            
            # Session events
            'open': 'OPEN',
            'session_boundary': 'SESSION_BOUNDARY',
            'cascade_execution': 'CASCADE'
        }
    
    def analyze_sessions(self, session_files: List[str]) -> Dict[str, Any]:
        """
        Analyze multiple session files to discover event grammar patterns
        
        Args:
            session_files: List of paths to Level-1 session JSON files
            
        Returns:
            Analysis results with discovered patterns and validation
        """
        
        print("🔍 EVENT GRAMMAR ANALYSIS - Market Linguistics Discovery")
        print("=" * 65)
        print(f"Analyzing {len(session_files)} sessions for event patterns...")
        print()
        
        # Step 1: Extract all events from sessions
        print("📂 Step 1: Extracting Events from Sessions")
        print("-" * 45)
        
        for session_file in session_files:
            events = self._extract_events_from_session(session_file)
            self.all_events.extend(events)
            print(f"   {Path(session_file).name}: {len(events)} events")
        
        print(f"\n📊 Total Events Extracted: {len(self.all_events)}")
        
        # Step 2: Build event sequences
        print(f"\n🔗 Step 2: Building Event Sequences")
        print("-" * 35)
        
        sequences = self._build_event_sequences()
        self.event_sequences = sequences
        
        print(f"   Event Sequences Generated: {len(sequences)}")
        print(f"   Average Sequence Length: {np.mean([len(s.events) for s in sequences]):.1f}")
        
        # Step 3: Discover grammatical patterns
        print(f"\n🎪 Step 3: Discovering Cascade Grammar Patterns")
        print("-" * 50)
        
        patterns = self._discover_grammar_patterns()
        self.discovered_patterns = patterns
        
        print(f"   Patterns Discovered: {len(patterns)}")
        
        # Step 4: Validate cascade prediction capability  
        print(f"\n🎯 Step 4: Validating Cascade Prediction Capability")
        print("-" * 55)
        
        validation_results = self._validate_cascade_prediction()
        
        # Step 5: Generate comprehensive analysis
        print(f"\n📈 Step 5: Comprehensive Grammar Analysis")
        print("-" * 45)
        
        analysis_results = {
            'total_events': len(self.all_events),
            'total_sequences': len(self.event_sequences),
            'patterns_discovered': len(patterns),
            'validation_results': validation_results,
            'grammar_patterns': [self._pattern_to_dict(p) for p in patterns],
            'event_type_distribution': self._analyze_event_distribution(),
            'sequence_statistics': self._analyze_sequence_statistics(),
            'linguistic_insights': self._generate_linguistic_insights()
        }
        
        # Display results
        self._display_analysis_results(analysis_results)
        
        # Save results
        self._save_analysis_results(analysis_results)
        
        return analysis_results
    
    def _extract_events_from_session(self, session_file: str) -> List[MarketEvent]:
        """Extract market events from a single session file"""
        
        events = []
        
        try:
            with open(session_file, 'r') as f:
                session_data = json.load(f)
            
            level1_data = session_data['level1_json']
            session_start = level1_data['session_metadata']['session_start']
            
            # Extract events from price movements
            price_movements = level1_data.get('price_movements', [])
            
            for movement in price_movements:
                # Parse timestamp and calculate session time
                timestamp = movement['timestamp']
                session_minutes = self._calculate_session_minutes(timestamp, session_start)
                
                # Determine event type and magnitude
                movement_type = movement.get('movement_type', 'unknown')
                event_type = self._classify_event_type(movement_type)
                
                if event_type != 'UNKNOWN':  # Only include classified events
                    # Calculate magnitude (price change from previous movements)
                    magnitude = self._calculate_event_magnitude(movement, price_movements)
                    
                    event = MarketEvent(
                        timestamp=timestamp,
                        event_type=event_type,
                        price_level=movement['price_level'],
                        magnitude=magnitude,
                        context=movement_type,
                        session_time_minutes=session_minutes
                    )
                    events.append(event)
            
            # Extract FPFVG events if present
            fpfvg_data = level1_data.get('session_fpfvg', {})
            if fpfvg_data.get('fpfvg_present'):
                fpfvg_events = self._extract_fpfvg_events(fpfvg_data, session_start)
                events.extend(fpfvg_events)
            
        except Exception as e:
            print(f"   ❌ Error extracting from {Path(session_file).name}: {e}")
        
        # Sort events by timestamp
        events.sort(key=lambda x: x.session_time_minutes)
        return events
    
    def _classify_event_type(self, movement_type: str) -> str:
        """Classify movement type into event grammar categories"""
        
        movement_lower = movement_type.lower()
        
        # Check for each pattern type
        for pattern, event_type in self.event_type_mapping.items():
            if pattern in movement_lower:
                return event_type
        
        # Special classifications
        if 'fpfvg' in movement_lower:
            return 'FPFVG'
        elif 'liquidity' in movement_lower:
            return 'LIQUIDITY_GRAB'
        elif 'expansion' in movement_lower:
            return 'EXPANSION'
        elif 'reversal' in movement_lower:
            return 'REVERSAL'
        elif 'redelivery' in movement_lower:
            return 'REDELIVERY'
        
        return 'UNKNOWN'
    
    def _calculate_session_minutes(self, timestamp: str, session_start: str) -> float:
        """Calculate minutes from session start"""
        
        try:
            # Parse time strings (assuming format HH:MM:SS)
            from datetime import datetime
            
            time_format = '%H:%M:%S'
            event_time = datetime.strptime(timestamp, time_format)
            start_time = datetime.strptime(session_start, time_format)
            
            # Handle day boundary crossing
            if event_time < start_time:
                event_time = event_time.replace(day=event_time.day + 1)
            
            delta = event_time - start_time
            return delta.total_seconds() / 60.0
            
        except:
            return 0.0
    
    def _calculate_event_magnitude(self, current_movement: Dict, all_movements: List[Dict]) -> float:
        """Calculate event magnitude as price change percentage"""
        
        try:
            current_price = current_movement['price_level']
            
            # Find previous movement for comparison
            current_time = current_movement['timestamp']
            prev_price = current_price
            
            for i, movement in enumerate(all_movements):
                if movement['timestamp'] == current_time and i > 0:
                    prev_price = all_movements[i-1]['price_level']
                    break
            
            # Calculate percentage change
            if prev_price > 0:
                magnitude = abs((current_price - prev_price) / prev_price) * 100
                return magnitude
            
        except:
            pass
        
        return 0.0
    
    def _extract_fpfvg_events(self, fpfvg_data: Dict, session_start: str) -> List[MarketEvent]:
        """Extract FPFVG-specific events"""
        
        events = []
        
        try:
            formation_data = fpfvg_data.get('fpfvg_formation', {})
            
            # FPFVG formation event
            if 'formation_time' in formation_data:
                formation_time = formation_data['formation_time']
                session_minutes = self._calculate_session_minutes(formation_time, session_start)
                
                event = MarketEvent(
                    timestamp=formation_time,
                    event_type='FPFVG',
                    price_level=formation_data.get('premium_high', 0),
                    magnitude=formation_data.get('gap_size', 0) / 100,  # Convert to percentage
                    context='fpfvg_formation',
                    session_time_minutes=session_minutes
                )
                events.append(event)
            
            # FPFVG interaction events
            interactions = formation_data.get('interactions', [])
            for interaction in interactions:
                interaction_time = interaction['interaction_time']
                session_minutes = self._calculate_session_minutes(interaction_time, session_start)
                
                interaction_type = interaction.get('interaction_type', 'interaction')
                event_type = 'REDELIVERY' if 'redelivery' in interaction_type else 'INTERACTION'
                
                event = MarketEvent(
                    timestamp=interaction_time,
                    event_type=event_type,
                    price_level=interaction.get('price_level', 0),
                    magnitude=1.0,  # Standard interaction magnitude
                    context=interaction_type,
                    session_time_minutes=session_minutes
                )
                events.append(event)
            
        except Exception as e:
            print(f"   Warning: FPFVG extraction error: {e}")
        
        return events
    
    def _build_event_sequences(self) -> List[EventSequence]:
        """Build event sequences that may form cascade patterns"""
        
        sequences = []
        
        # Define sequence window (look for patterns within X minutes)
        sequence_window_minutes = 30.0
        min_sequence_length = 2
        max_sequence_length = 6
        
        # Group events by session (assuming similar session_time_minutes = same session)
        session_groups = defaultdict(list)
        for event in self.all_events:
            # Simple session grouping by rounding to nearest 150 minutes (session length)
            session_id = int(event.session_time_minutes // 150)
            session_groups[session_id].append(event)
        
        # Build sequences within each session
        for session_events in session_groups.values():
            session_events.sort(key=lambda x: x.session_time_minutes)
            
            for i in range(len(session_events)):
                for j in range(i + min_sequence_length, min(i + max_sequence_length + 1, len(session_events) + 1)):
                    # Check if sequence fits within time window
                    if session_events[j-1].session_time_minutes - session_events[i].session_time_minutes <= sequence_window_minutes:
                        
                        sequence_events = session_events[i:j]
                        sequence_types = [e.event_type for e in sequence_events]
                        duration = sequence_events[-1].session_time_minutes - sequence_events[0].session_time_minutes
                        total_magnitude = sum(e.magnitude for e in sequence_events)
                        
                        # Determine if this sequence led to a cascade (heuristic)
                        cascade_occurred = self._detect_cascade_in_sequence(sequence_events)
                        confidence = self._calculate_sequence_confidence(sequence_events)
                        
                        sequence = EventSequence(
                            events=sequence_events,
                            sequence_type=' → '.join(sequence_types),
                            duration_minutes=duration,
                            total_magnitude=total_magnitude,
                            cascade_occurred=cascade_occurred,
                            confidence=confidence
                        )
                        sequences.append(sequence)
        
        return sequences
    
    def _detect_cascade_in_sequence(self, events: List[MarketEvent]) -> bool:
        """Detect if sequence contains/leads to cascade pattern"""
        
        # Heuristic cascade detection criteria
        cascade_indicators = 0
        
        # 1. High magnitude events
        if any(e.magnitude > 5.0 for e in events):  # >5% price move
            cascade_indicators += 1
        
        # 2. Multiple liquidity events
        liquidity_events = [e for e in events if 'LIQUIDITY' in e.event_type]
        if len(liquidity_events) >= 2:
            cascade_indicators += 1
        
        # 3. FPFVG followed by redelivery
        event_types = [e.event_type for e in events]
        if 'FPFVG' in event_types and 'REDELIVERY' in event_types:
            cascade_indicators += 1
        
        # 4. Expansion followed by reversal
        if 'EXPANSION' in event_types and 'REVERSAL' in event_types:
            cascade_indicators += 1
        
        # 5. High event density (many events in short time)
        if len(events) >= 4 and events[-1].session_time_minutes - events[0].session_time_minutes <= 10:
            cascade_indicators += 1
        
        # Cascade if multiple indicators present
        return cascade_indicators >= 2
    
    def _calculate_sequence_confidence(self, events: List[MarketEvent]) -> float:
        """Calculate confidence score for sequence"""
        
        confidence = 0.5  # Base confidence
        
        # Boost for high magnitude
        avg_magnitude = np.mean([e.magnitude for e in events])
        confidence += min(0.3, avg_magnitude / 10.0)
        
        # Boost for known good patterns
        event_types = [e.event_type for e in events]
        if 'FPFVG' in event_types:
            confidence += 0.1
        if 'LIQUIDITY_GRAB' in event_types:
            confidence += 0.1
        
        return min(1.0, confidence)
    
    def _discover_grammar_patterns(self) -> List[GrammarPattern]:
        """Discover recurring grammatical patterns in event sequences"""
        
        patterns = []
        
        # Group sequences by type and analyze frequency
        sequence_type_groups = defaultdict(list)
        for sequence in self.event_sequences:
            sequence_type_groups[sequence.sequence_type].append(sequence)
        
        # Find patterns that occur frequently and predict cascades
        for sequence_type, sequences in sequence_type_groups.items():
            if len(sequences) >= 3:  # Must occur at least 3 times
                
                # Calculate cascade prediction statistics
                cascade_count = sum(1 for s in sequences if s.cascade_occurred)
                cascade_probability = cascade_count / len(sequences)
                
                if cascade_probability > 0.5:  # Must predict cascades better than chance
                    
                    # Calculate typical characteristics
                    durations = [s.duration_minutes for s in sequences]
                    magnitudes = [s.total_magnitude for s in sequences]
                    
                    pattern = GrammarPattern(
                        pattern_signature=sequence_type,
                        event_types=sequence_type.split(' → '),
                        typical_duration_minutes=np.mean(durations),
                        cascade_probability=cascade_probability,
                        frequency_count=len(sequences),
                        example_sequences=sequences[:3]  # Store examples
                    )
                    patterns.append(pattern)
        
        # Sort by cascade probability
        patterns.sort(key=lambda p: p.cascade_probability, reverse=True)
        
        return patterns
    
    def _validate_cascade_prediction(self) -> Dict[str, Any]:
        """Validate cascade prediction capability of discovered patterns"""
        
        validation = {
            'total_patterns': len(self.discovered_patterns),
            'high_probability_patterns': 0,
            'average_cascade_probability': 0.0,
            'pattern_coverage': 0.0,
            'linguistic_validation': False
        }
        
        if self.discovered_patterns:
            probabilities = [p.cascade_probability for p in self.discovered_patterns]
            validation['average_cascade_probability'] = np.mean(probabilities)
            validation['high_probability_patterns'] = sum(1 for p in probabilities if p > 0.7)
            
            # Calculate how many sequences are covered by patterns
            pattern_sequences = set()
            for pattern in self.discovered_patterns:
                for seq in pattern.example_sequences:
                    pattern_sequences.add(seq.sequence_type)
            
            total_unique_sequences = len(set(s.sequence_type for s in self.event_sequences))
            if total_unique_sequences > 0:
                validation['pattern_coverage'] = len(pattern_sequences) / total_unique_sequences
            
            # Linguistic validation: Do we have ≥3 distinct patterns with >60% accuracy?
            validation['linguistic_validation'] = (
                len(self.discovered_patterns) >= 3 and
                validation['high_probability_patterns'] >= 3
            )
        
        return validation
    
    def _analyze_event_distribution(self) -> Dict[str, int]:
        """Analyze distribution of event types"""
        
        event_types = [e.event_type for e in self.all_events]
        return dict(Counter(event_types))
    
    def _analyze_sequence_statistics(self) -> Dict[str, float]:
        """Analyze sequence statistics"""
        
        if not self.event_sequences:
            return {}
        
        durations = [s.duration_minutes for s in self.event_sequences]
        magnitudes = [s.total_magnitude for s in self.event_sequences]
        cascade_rate = sum(1 for s in self.event_sequences if s.cascade_occurred) / len(self.event_sequences)
        
        return {
            'average_sequence_duration_minutes': np.mean(durations),
            'average_total_magnitude': np.mean(magnitudes),
            'cascade_occurrence_rate': cascade_rate,
            'sequences_with_cascades': sum(1 for s in self.event_sequences if s.cascade_occurred)
        }
    
    def _generate_linguistic_insights(self) -> List[str]:
        """Generate linguistic insights about discovered patterns"""
        
        insights = []
        
        if self.discovered_patterns:
            # Most predictive pattern
            best_pattern = max(self.discovered_patterns, key=lambda p: p.cascade_probability)
            insights.append(f"Most predictive pattern: '{best_pattern.pattern_signature}' "
                          f"({best_pattern.cascade_probability:.1%} cascade probability)")
            
            # Most frequent pattern
            most_frequent = max(self.discovered_patterns, key=lambda p: p.frequency_count)
            insights.append(f"Most frequent pattern: '{most_frequent.pattern_signature}' "
                          f"(occurs {most_frequent.frequency_count} times)")
            
            # Pattern complexity analysis
            pattern_lengths = [len(p.event_types) for p in self.discovered_patterns]
            avg_length = np.mean(pattern_lengths)
            insights.append(f"Average cascade pattern length: {avg_length:.1f} events")
            
            # Time characteristics
            durations = [p.typical_duration_minutes for p in self.discovered_patterns]
            avg_duration = np.mean(durations)
            insights.append(f"Typical cascade pattern duration: {avg_duration:.1f} minutes")
        
        else:
            insights.append("No significant grammatical patterns discovered")
            insights.append("Manual recording may not contain sufficient linguistic structure")
        
        return insights
    
    def _pattern_to_dict(self, pattern: GrammarPattern) -> Dict:
        """Convert pattern to dictionary for serialization"""
        
        return {
            'pattern_signature': pattern.pattern_signature,
            'event_types': pattern.event_types,
            'typical_duration_minutes': pattern.typical_duration_minutes,
            'cascade_probability': pattern.cascade_probability,
            'frequency_count': pattern.frequency_count,
            'example_count': len(pattern.example_sequences)
        }
    
    def _display_analysis_results(self, results: Dict[str, Any]):
        """Display comprehensive analysis results"""
        
        print(f"\n🏆 EVENT GRAMMAR ANALYSIS RESULTS")
        print("=" * 50)
        
        print(f"📊 Data Statistics:")
        print(f"   Total Events: {results['total_events']}")
        print(f"   Event Sequences: {results['total_sequences']}")
        print(f"   Patterns Discovered: {results['patterns_discovered']}")
        
        validation = results['validation_results']
        print(f"\n🎯 Cascade Prediction Validation:")
        print(f"   High-Probability Patterns (>70%): {validation['high_probability_patterns']}")
        print(f"   Average Cascade Probability: {validation['average_cascade_probability']:.1%}")
        print(f"   Pattern Coverage: {validation['pattern_coverage']:.1%}")
        print(f"   Linguistic Validation: {'✅ PASSED' if validation['linguistic_validation'] else '❌ FAILED'}")
        
        print(f"\n🎪 Discovered Grammar Patterns:")
        if results['grammar_patterns']:
            for i, pattern in enumerate(results['grammar_patterns'], 1):
                print(f"   {i}. {pattern['pattern_signature']}")
                print(f"      Cascade Probability: {pattern['cascade_probability']:.1%}")
                print(f"      Frequency: {pattern['frequency_count']} occurrences")
                print(f"      Duration: {pattern['typical_duration_minutes']:.1f} min")
        else:
            print("   No significant patterns discovered")
        
        print(f"\n📈 Event Distribution:")
        distribution = results['event_type_distribution']
        for event_type, count in sorted(distribution.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"   {event_type}: {count}")
        
        print(f"\n🧠 Linguistic Insights:")
        for insight in results['linguistic_insights']:
            print(f"   • {insight}")
    
    def _save_analysis_results(self, results: Dict[str, Any]):
        """Save analysis results to file"""
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_path = f"event_grammar_analysis_{timestamp}.json"
        
        # Make results JSON serializable
        serializable_results = results.copy()
        
        with open(output_path, 'w') as f:
            json.dump(serializable_results, f, indent=2, default=str)
        
        print(f"\n💾 Analysis results saved: {output_path}")

def discover_event_grammar(data_directory: str = "../data/sessions/level_1/") -> Dict[str, Any]:
    """
    Main function to discover event grammar from session data
    
    Args:
        data_directory: Directory containing Level-1 session files
        
    Returns:
        Grammar analysis results
    """
    
    # Find all session files
    data_path = Path(data_directory)
    session_files = list(data_path.glob("*.json"))
    
    if not session_files:
        print(f"❌ No session files found in {data_directory}")
        return {}
    
    print(f"🔍 Found {len(session_files)} session files")
    
    # Limit to reasonable number for analysis
    if len(session_files) > 10:
        session_files = session_files[:10]
        print(f"   Analyzing first 10 files for performance")
    
    # Run analysis
    analyzer = EventGrammarAnalyzer()
    results = analyzer.analyze_sessions([str(f) for f in session_files])
    
    # Final validation message
    validation = results['validation_results']
    if validation['linguistic_validation']:
        print(f"\n🎉 LINGUISTIC VALIDATION PASSED!")
        print(f"   Your manual recording contains sufficient grammatical structure")
        print(f"   Found {validation['high_probability_patterns']} high-probability cascade patterns")
        print(f"   Ready to proceed with Event-Grammar Architecture Migration")
    else:
        print(f"\n⚠️ LINGUISTIC VALIDATION NEEDS IMPROVEMENT")
        print(f"   Current patterns: {len(results['grammar_patterns'])}")
        print(f"   High-probability patterns: {validation['high_probability_patterns']}")
        print(f"   May need to refine event extraction or pattern detection")
    
    return results

if __name__ == "__main__":
    results = discover_event_grammar()