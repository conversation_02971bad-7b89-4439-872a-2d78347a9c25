{"validation_id": "prod_val_1754676017", "timestamp": "2025-08-08 19:00:30.242935", "pipeline_version": "1.0", "stages_results": [{"stage_name": "data_integrity", "success": true, "duration_seconds": 2.380249261856079, "metrics": {}, "artifacts": {}, "error_message": null}, {"stage_name": "ml_training", "success": true, "duration_seconds": 2.2303740978240967, "metrics": {}, "artifacts": {}, "error_message": null}, {"stage_name": "accuracy_validation", "success": true, "duration_seconds": 2.487100839614868, "metrics": {}, "artifacts": {}, "error_message": null}, {"stage_name": "ab_testing", "success": true, "duration_seconds": 1.844007968902588, "metrics": {}, "artifacts": {}, "error_message": null}, {"stage_name": "calibration", "success": true, "duration_seconds": 1.9732961654663086, "metrics": {}, "artifacts": {}, "error_message": null}, {"stage_name": "prediction_service", "success": true, "duration_seconds": 1.9713680744171143, "metrics": {}, "artifacts": {}, "error_message": null}], "gates_status": {"accuracy_baseline": false, "statistical_significance": false, "temporal_stability": false, "latency_sla": false, "success_rate": false, "sample_size": false}, "overall_status": "FAIL", "deployment_recommendation": "DO_NOT_DEPLOY", "performance_metrics": {"total_pipeline_duration_seconds": 12.886396408081055, "successful_stages_ratio": 1.0, "average_stage_duration_seconds": 2.147732734680176}, "regression_analysis": {"baseline_comparison": {"current_accuracy": 0, "baseline_accuracy": 0.911, "regression_detected": true, "improvement_detected": false}, "trend_analysis": {"accuracy_trend": "stable", "latency_trend": "stable", "success_rate_trend": "stable"}, "anomaly_detection": {"latency_anomaly": false, "accuracy_anomaly": true, "pipeline_duration_anomaly": false}}, "recommendations": ["CRITICAL GATE FAILURE: Model accuracy meets Oracle baseline", "CRITICAL GATE FAILURE: Results are statistically significant", "CRITICAL GATE FAILURE: Model stable over time", "CRITICAL GATE FAILURE: Prediction latency within SLA", "CRITICAL GATE FAILURE: Prediction success rate adequate", "WARNING: Adequate sample size for validation"]}