{"validation_id": "prod_val_1754676139", "timestamp": "2025-08-08 19:02:32.040973", "pipeline_version": "1.0", "stages_results": [{"stage_name": "data_integrity", "success": true, "duration_seconds": 2.104739189147949, "metrics": {}, "artifacts": {}, "error_message": null}, {"stage_name": "ml_training", "success": true, "duration_seconds": 2.5349981784820557, "metrics": {}, "artifacts": {}, "error_message": null}, {"stage_name": "accuracy_validation", "success": true, "duration_seconds": 2.155921220779419, "metrics": {}, "artifacts": {}, "error_message": null}, {"stage_name": "ab_testing", "success": true, "duration_seconds": 1.9176828861236572, "metrics": {}, "artifacts": {}, "error_message": null}, {"stage_name": "calibration", "success": true, "duration_seconds": 1.95188570022583, "metrics": {}, "artifacts": {}, "error_message": null}, {"stage_name": "prediction_service", "success": true, "duration_seconds": 2.193779230117798, "metrics": {}, "artifacts": {}, "error_message": null}], "gates_status": {"accuracy_baseline": false, "statistical_significance": false, "temporal_stability": false, "latency_sla": false, "success_rate": false, "sample_size": false}, "overall_status": "FAIL", "deployment_recommendation": "DO_NOT_DEPLOY", "performance_metrics": {"total_pipeline_duration_seconds": 12.859006404876709, "successful_stages_ratio": 1.0, "average_stage_duration_seconds": 2.143167734146118}, "regression_analysis": {"baseline_comparison": {"current_accuracy": 0, "baseline_accuracy": 0.911, "regression_detected": true, "improvement_detected": false}, "trend_analysis": {"accuracy_trend": "stable", "latency_trend": "stable", "success_rate_trend": "stable"}, "anomaly_detection": {"latency_anomaly": false, "accuracy_anomaly": true, "pipeline_duration_anomaly": false}}, "recommendations": ["CRITICAL GATE FAILURE: Model accuracy meets Oracle baseline", "CRITICAL GATE FAILURE: Results are statistically significant", "CRITICAL GATE FAILURE: Model stable over time", "CRITICAL GATE FAILURE: Prediction latency within SLA", "CRITICAL GATE FAILURE: Prediction success rate adequate", "WARNING: Adequate sample size for validation"]}