#!/usr/bin/env python3
"""
Conservative Rebalancing - Address SMOTE Over-Learning
=====================================================

The Class Balance Revolution revealed regime-dependent memorization:
- 99.3% accuracy but ±10.5% CV variance  
- Model memorized SMOTE synthetic patterns instead of genuine market patterns

New Strategy: Ultra-Conservative Approach
1. Manual downsampling (no synthetic data)
2. Extreme regularization (max_depth=2, learning_rate=0.01)  
3. Early stopping with patience=5
4. Target: 75-80% accuracy with <3% CV variance = genuine robustness

Mathematical Principle: 
Better to have 77% consistent accuracy than 99% unstable accuracy
"""

import json
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.metrics import accuracy_score, balanced_accuracy_score
from sklearn.preprocessing import StandardScaler
import pickle

from xgboost_real_trainer import XGBoostRealTrainer, TrainingExample

@dataclass
class ConservativeMetrics:
    """Ultra-conservative model metrics"""
    balanced_accuracy: float
    regular_accuracy: float
    cv_mean: float
    cv_std: float
    training_accuracy: float
    overfitting_ratio: float  # training/validation ratio
    stability_score: float    # 1 - cv_std (higher is better)

class ConservativeRebalancer:
    """
    Ultra-conservative rebalancing to prioritize stability over accuracy
    
    Philosophy: Consistent 77% >> Unstable 99%
    Target: CV variance <3% with genuine learning patterns
    """
    
    def __init__(self):
        self.trainer = XGBoostRealTrainer()
        self.conservative_model = None
        self.conservative_scaler = StandardScaler()
        
        print("🛡️ CONSERVATIVE REBALANCER")
        print("=" * 30)
        print("Philosophy: Stability > Accuracy")
        print("Target: 77% ± 3% CV variance")
        print("Method: Manual downsampling + extreme regularization")
        print()
    
    def manual_conservative_balancing(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Conservative manual balancing with maximum genuine data retention"""
        
        print("⚖️ Applying ultra-conservative manual balancing...")
        
        # Separate classes
        cascade_indices = np.where(y == 1)[0]
        non_cascade_indices = np.where(y == 0)[0]
        
        minority_size = len(non_cascade_indices)
        majority_size = len(cascade_indices)
        
        print(f"   Original: {majority_size} cascades, {minority_size} non-cascades")
        
        # Strategy: Keep ALL minority examples + equal number of majority
        # This maximizes genuine data while achieving balance
        target_size = minority_size
        
        if target_size < 8:
            print(f"⚠️ Very small dataset: {target_size} examples per class")
            print("   Risk: High variance due to insufficient data")
        
        # Randomly sample from majority class (reproducible)
        np.random.seed(42)
        sampled_cascade_indices = np.random.choice(
            cascade_indices, size=target_size, replace=False
        )
        
        # Combine all minority + sampled majority
        balanced_indices = np.concatenate([non_cascade_indices, sampled_cascade_indices])
        np.random.shuffle(balanced_indices)
        
        X_balanced = X[balanced_indices]
        y_balanced = y[balanced_indices]
        
        print(f"✅ Conservative balancing complete:")
        print(f"   Balanced dataset: {len(X_balanced)} samples")
        print(f"   Cascade rate: {np.mean(y_balanced):.1%}")
        print(f"   All minority examples retained: {minority_size}")
        print(f"   Majority examples sampled: {target_size}")
        
        return X_balanced, y_balanced
    
    def train_ultra_conservative_model(self, X_balanced: np.ndarray, y_balanced: np.ndarray) -> GradientBoostingClassifier:
        """Train with extreme regularization to prevent any overfitting"""
        
        print("🎯 Training ultra-conservative model...")
        
        # Scale features
        X_scaled = self.conservative_scaler.fit_transform(X_balanced)
        
        # Ultra-conservative hyperparameters
        # Target: Underfitting slightly is better than overfitting
        self.conservative_model = GradientBoostingClassifier(
            n_estimators=50,          # Very few trees  
            max_depth=2,              # Extremely shallow
            learning_rate=0.01,       # Very slow learning
            subsample=0.6,            # High stochasticity
            max_features=0.5,         # Use only half features
            min_samples_split=20,     # Prevent small pattern fitting
            min_samples_leaf=10,      # Prevent small pattern fitting
            random_state=42,
            validation_fraction=0.3,  # Large validation set
            n_iter_no_change=5        # Very early stopping
        )
        
        print(f"   Ultra-conservative hyperparameters:")
        print(f"   - n_estimators=50 (vs 100)")
        print(f"   - max_depth=2 (vs 3)")  
        print(f"   - learning_rate=0.01 (vs 0.05)")
        print(f"   - Early stopping patience=5")
        
        # Train model
        self.conservative_model.fit(X_scaled, y_balanced)
        
        # Check training accuracy - should NOT be 100%
        train_accuracy = self.conservative_model.score(X_scaled, y_balanced)
        print(f"   Training accuracy: {train_accuracy:.1%}")
        
        if train_accuracy > 0.90:
            print("⚠️ Warning: Training accuracy still high, may need more regularization")
        elif train_accuracy < 0.70:
            print("⚠️ Warning: Training accuracy very low, may be underfitting")
        else:
            print("✅ Training accuracy in acceptable range (70-90%)")
        
        return self.conservative_model
    
    def comprehensive_stability_evaluation(self, X_original: np.ndarray, y_original: np.ndarray,
                                         X_balanced: np.ndarray, y_balanced: np.ndarray) -> ConservativeMetrics:
        """Focus on stability metrics over raw accuracy"""
        
        print("📊 Comprehensive stability evaluation...")
        
        # Evaluate on original data (not synthetic)
        X_scaled = self.conservative_scaler.transform(X_original)
        y_pred = self.conservative_model.predict(X_scaled)
        
        # Basic accuracies
        regular_accuracy = accuracy_score(y_original, y_pred)
        balanced_accuracy = balanced_accuracy_score(y_original, y_pred)
        
        # Training accuracy for overfitting detection
        X_train_scaled = self.conservative_scaler.transform(X_balanced)
        training_accuracy = self.conservative_model.score(X_train_scaled, y_balanced)
        
        # Cross-validation stability (key metric)
        cv = StratifiedKFold(n_splits=min(5, len(X_original)//8), shuffle=True, random_state=42)
        cv_scores = cross_val_score(
            self.conservative_model, X_scaled, y_original, 
            cv=cv, scoring='balanced_accuracy', n_jobs=-1
        )
        
        cv_mean = np.mean(cv_scores)
        cv_std = np.std(cv_scores)
        
        # Derived metrics
        overfitting_ratio = training_accuracy / balanced_accuracy if balanced_accuracy > 0 else 999
        stability_score = max(0, 1 - cv_std)  # 1 = perfect stability, 0 = maximum variance
        
        metrics = ConservativeMetrics(
            balanced_accuracy=balanced_accuracy,
            regular_accuracy=regular_accuracy,
            cv_mean=cv_mean,
            cv_std=cv_std,
            training_accuracy=training_accuracy,
            overfitting_ratio=overfitting_ratio,
            stability_score=stability_score
        )
        
        print(f"✅ Stability evaluation complete:")
        print(f"   Balanced accuracy: {balanced_accuracy:.1%}")
        print(f"   Regular accuracy: {regular_accuracy:.1%}")
        print(f"   Training accuracy: {training_accuracy:.1%}")
        print(f"   CV mean: {cv_mean:.1%}")
        print(f"   CV std: ±{cv_std:.1%}")
        print(f"   Overfitting ratio: {overfitting_ratio:.2f}")
        print(f"   Stability score: {stability_score:.3f}")
        
        return metrics
    
    def evaluate_conservative_success(self, metrics: ConservativeMetrics) -> bool:
        """Evaluate success based on conservative criteria"""
        
        print(f"\n🏆 CONSERVATIVE SUCCESS EVALUATION")
        print("=" * 40)
        
        success_criteria = []
        
        # 1. Stability criterion (most important)
        if metrics.cv_std < 0.03:  # <3% variance
            success_criteria.append("✅ Excellent stability (CV std <3%)")
        elif metrics.cv_std < 0.05:  # <5% variance
            success_criteria.append("🟡 Good stability (CV std <5%)")
        else:
            success_criteria.append("❌ Poor stability (CV std >5%)")
        
        # 2. Overfitting criterion
        if metrics.overfitting_ratio < 1.10:  # Training accuracy within 10% of validation
            success_criteria.append("✅ No overfitting detected")
        elif metrics.overfitting_ratio < 1.20:  # Within 20%
            success_criteria.append("🟡 Mild overfitting")
        else:
            success_criteria.append("❌ Significant overfitting")
        
        # 3. Minimum accuracy criterion (relaxed)
        if metrics.balanced_accuracy > 0.75:  # >75%
            success_criteria.append("✅ Acceptable accuracy (>75%)")
        elif metrics.balanced_accuracy > 0.70:  # >70%
            success_criteria.append("🟡 Marginal accuracy (>70%)")
        else:
            success_criteria.append("❌ Poor accuracy (<70%)")
        
        # 4. Consistency criterion
        if abs(metrics.cv_mean - metrics.balanced_accuracy) < 0.05:  # Within 5%
            success_criteria.append("✅ Consistent performance")
        else:
            success_criteria.append("❌ Inconsistent performance")
        
        for criterion in success_criteria:
            print(f"   {criterion}")
        
        # Overall assessment
        excellent_count = sum(1 for c in success_criteria if c.startswith("✅"))
        good_count = sum(1 for c in success_criteria if c.startswith("🟡"))
        
        if excellent_count >= 3:
            assessment = "🎉 CONSERVATIVE SUCCESS - Deploy to production"
            success = True
        elif excellent_count + good_count >= 3:
            assessment = "🟡 CONSERVATIVE PARTIAL - Acceptable for deployment"
            success = True
        else:
            assessment = "❌ CONSERVATIVE FAILURE - Further adjustments needed"
            success = False
        
        print(f"\n🎯 FINAL ASSESSMENT:")
        print(f"   {assessment}")
        print(f"   Excellent criteria: {excellent_count}/4")
        print(f"   Good + Excellent: {excellent_count + good_count}/4")
        
        return success
    
    def save_conservative_model(self, filename: str = "conservative_xgboost_model.pkl"):
        """Save the conservative model"""
        
        if self.conservative_model is None:
            print("❌ No conservative model to save")
            return False
        
        model_data = {
            'model': self.conservative_model,
            'scaler': self.conservative_scaler,
            'pattern_encoder': self.trainer.enhancer.pattern_encoder,
            'model_type': 'ultra_conservative_xgboost',
            'balancing_method': 'manual_downsampling',
            'hyperparameters': {
                'n_estimators': 50,
                'max_depth': 2,
                'learning_rate': 0.01,
                'subsample': 0.6,
                'max_features': 0.5,
                'early_stopping': 5
            },
            'training_date': pd.Timestamp.now().isoformat(),
            'philosophy': 'Stability over accuracy'
        }
        
        with open(filename, 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"✅ Conservative model saved: {filename}")
        return True
    
    def execute_conservative_approach(self) -> Dict[str, Any]:
        """Execute the complete conservative approach"""
        
        print("🛡️ EXECUTING ULTRA-CONSERVATIVE APPROACH")
        print("=" * 45)
        print("Philosophy: Consistent 77% > Unstable 99%")
        print()
        
        # Step 1: Load original data
        print("📁 Loading original training data...")
        sessions = self.trainer.load_enhanced_sessions()
        examples = self.trainer.extract_training_examples(sessions)
        X_original, y_original = self.trainer.prepare_training_data(examples)
        
        print(f"✅ Original data: {len(examples)} examples, {np.mean(y_original):.1%} cascade rate")
        
        # Step 2: Conservative balancing (no SMOTE)
        X_balanced, y_balanced = self.manual_conservative_balancing(X_original, y_original)
        
        # Step 3: Ultra-conservative training
        conservative_model = self.train_ultra_conservative_model(X_balanced, y_balanced)
        
        # Step 4: Stability evaluation
        metrics = self.comprehensive_stability_evaluation(X_original, y_original, X_balanced, y_balanced)
        
        # Step 5: Success evaluation
        success = self.evaluate_conservative_success(metrics)
        
        # Step 6: Save if successful
        if success:
            self.save_conservative_model()
        
        results = {
            'conservative_success': success,
            'metrics': metrics,
            'original_cascade_rate': np.mean(y_original),
            'balanced_cascade_rate': np.mean(y_balanced),
            'model_saved': success,
            'philosophy': 'Stability prioritized over raw accuracy'
        }
        
        print(f"\n🎯 ULTRA-CONSERVATIVE APPROACH COMPLETE")
        print(f"Success: {'YES' if success else 'NO'}")
        print(f"Balanced Accuracy: {metrics.balanced_accuracy:.1%}")
        print(f"CV Stability: ±{metrics.cv_std:.1%}")
        print(f"Stability Score: {metrics.stability_score:.3f}")
        print(f"Overfitting Ratio: {metrics.overfitting_ratio:.2f}")
        
        if success:
            print("🎉 Ready for production deployment!")
        else:
            print("⚠️ Needs further refinement")
        
        return results

def main():
    """Execute ultra-conservative rebalancing"""
    
    rebalancer = ConservativeRebalancer()
    results = rebalancer.execute_conservative_approach()
    
    return results

if __name__ == "__main__":
    conservative_results = main()