#!/usr/bin/env python3
"""
Event Grammar Backtest Validator - CRITICAL VALIDATION CHECKPOINT
================================================================

This is the critical validation checkpoint that determines if the Event-Grammar
Architecture can match or exceed the Oracle's 91.1% baseline accuracy.

Tests both architectures on the same 10 historical sessions:
1. Original Oracle (with Fisher Information Monitor)
2. New Event-Grammar Oracle (with Event Intensity Monitor)

SUCCESS CRITERIA: Event-Grammar accuracy ≥ 91.1% baseline
If this fails, we adjust the mathematical transformation or revert to enhancement approach.
"""

import json
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
import logging

# Import both approaches
from oracle import ProjectOracle
from event_intensity_monitor import EventIntensityMonitor, MarketEvent
from event_grammar_analyzer import EventGrammarAnalyzer

@dataclass
class BacktestResult:
    """Result from backtesting single session"""
    session_file: str
    approach: str
    
    # Prediction results
    prediction_made: bool
    predicted_cascade_time: float
    confidence: float
    alert_level: str
    
    # Performance metrics
    processing_time_seconds: float
    events_processed: int
    
    # Validation data
    actual_cascade_time: Optional[float] = None
    prediction_error_minutes: Optional[float] = None
    accuracy_score: float = 0.0
    
    # Raw results for analysis
    raw_result: Dict[str, Any] = None
    error_message: str = ""

@dataclass
class ValidationSummary:
    """Summary of validation results"""
    total_sessions: int
    successful_predictions: int
    average_accuracy: float
    average_confidence: float
    average_processing_time: float
    accuracy_vs_baseline: float
    validation_passed: bool

class EventGrammarBacktestValidator:
    """
    Critical validation checkpoint for Event-Grammar Architecture
    
    Tests whether event-grammar approach can match Oracle's 91.1% baseline
    """
    
    def __init__(self):
        self.baseline_accuracy_threshold = 0.911  # Oracle's 91.1% baseline
        self.acceptable_accuracy_threshold = 0.85  # Minimum acceptable (85%)
        
        self.logger = logging.getLogger(__name__)
        
    def run_critical_backtest(self, session_files: List[str]) -> Dict[str, Any]:
        """
        Run critical backtest validation
        
        Args:
            session_files: List of session files for backtesting
            
        Returns:
            Complete validation results
        """
        
        print("🚨 CRITICAL VALIDATION CHECKPOINT")
        print("=" * 50)
        print("Testing Event-Grammar vs Oracle 91.1% Baseline")
        print(f"Sessions to test: {len(session_files)}")
        print()
        
        # Test both approaches
        oracle_results = self._test_oracle_baseline(session_files)
        event_grammar_results = self._test_event_grammar(session_files)
        
        # Compare results
        comparison_results = self._compare_approaches(oracle_results, event_grammar_results)
        
        # Generate validation summary
        validation_summary = self._generate_validation_summary(
            oracle_results, event_grammar_results, comparison_results
        )
        
        # Display results
        self._display_validation_results(validation_summary, comparison_results)
        
        # Save results
        self._save_validation_results({
            'oracle_results': oracle_results,
            'event_grammar_results': event_grammar_results,
            'comparison_results': comparison_results,
            'validation_summary': validation_summary
        })
        
        return {
            'validation_passed': validation_summary.validation_passed,
            'oracle_accuracy': validation_summary.average_accuracy,
            'event_grammar_accuracy': validation_summary.accuracy_vs_baseline,
            'recommendation': self._generate_recommendation(validation_summary)
        }
    
    def _test_oracle_baseline(self, session_files: List[str]) -> List[BacktestResult]:
        """Test original Oracle baseline approach"""
        
        print("🏛️ TESTING ORACLE 91.1% BASELINE")
        print("-" * 35)
        
        results = []
        
        for session_file in session_files:
            print(f"   Testing: {Path(session_file).name}")
            
            try:
                result = self._run_oracle_prediction(session_file)
                results.append(result)
                
                status = "✅" if result.prediction_made else "❌"
                print(f"     {status} Confidence: {result.confidence:.3f}, Time: {result.processing_time_seconds:.3f}s")
                
            except Exception as e:
                print(f"     ❌ Error: {e}")
                error_result = BacktestResult(
                    session_file=session_file,
                    approach="oracle_baseline",
                    prediction_made=False,
                    predicted_cascade_time=0.0,
                    confidence=0.0,
                    alert_level="ERROR",
                    processing_time_seconds=0.0,
                    events_processed=0,
                    error_message=str(e)
                )
                results.append(error_result)
        
        successful = sum(1 for r in results if r.prediction_made and not r.error_message)
        print(f"   Oracle Results: {successful}/{len(results)} successful predictions")
        
        return results
    
    def _test_event_grammar(self, session_files: List[str]) -> List[BacktestResult]:
        """Test Event-Grammar architecture"""
        
        print(f"\n⚡ TESTING EVENT-GRAMMAR ARCHITECTURE")
        print("-" * 40)
        
        results = []
        
        # Initialize event intensity monitor
        intensity_monitor = EventIntensityMonitor()
        
        for session_file in session_files:
            print(f"   Testing: {Path(session_file).name}")
            
            try:
                result = self._run_event_grammar_prediction(session_file, intensity_monitor)
                results.append(result)
                
                status = "✅" if result.prediction_made else "❌" 
                print(f"     {status} Confidence: {result.confidence:.3f}, Alert: {result.alert_level}")
                
            except Exception as e:
                print(f"     ❌ Error: {e}")
                error_result = BacktestResult(
                    session_file=session_file,
                    approach="event_grammar",
                    prediction_made=False,
                    predicted_cascade_time=0.0,
                    confidence=0.0,
                    alert_level="ERROR", 
                    processing_time_seconds=0.0,
                    events_processed=0,
                    error_message=str(e)
                )
                results.append(error_result)
        
        successful = sum(1 for r in results if r.prediction_made and not r.error_message)
        print(f"   Event-Grammar Results: {successful}/{len(results)} successful predictions")
        
        return results
    
    def _run_oracle_prediction(self, session_file: str) -> BacktestResult:
        """Run Oracle baseline prediction on single session"""
        
        import time
        
        start_time = time.time()
        
        # Load session data
        with open(session_file, 'r') as f:
            session_data = json.load(f)
        
        # Prepare Oracle input (same as baseline capture)
        level1_data = session_data['level1_json']
        oracle_input = self._prepare_oracle_input(level1_data)
        
        # Run Oracle prediction
        oracle = ProjectOracle()
        oracle_result = oracle.predict_cascade_timing(oracle_input)
        
        processing_time = time.time() - start_time
        
        # Extract results
        result = BacktestResult(
            session_file=session_file,
            approach="oracle_baseline",
            prediction_made=True,
            predicted_cascade_time=oracle_result.predicted_cascade_time,
            confidence=oracle_result.prediction_confidence,
            alert_level="HIGH" if oracle_result.prediction_confidence > 0.7 else "MODERATE",
            processing_time_seconds=processing_time,
            events_processed=len(oracle_input.get('micro_timing_analysis', {}).get('cascade_events', [])),
            raw_result={
                'predicted_cascade_time': oracle_result.predicted_cascade_time,
                'prediction_confidence': oracle_result.prediction_confidence,
                'methodology': oracle_result.methodology,
                'enhancement_active': oracle_result.enhancement_active
            }
        )
        
        return result
    
    def _run_event_grammar_prediction(self, session_file: str, 
                                    intensity_monitor: EventIntensityMonitor) -> BacktestResult:
        """Run Event-Grammar prediction on single session"""
        
        import time
        
        start_time = time.time()
        
        # Extract events using grammar analyzer
        analyzer = EventGrammarAnalyzer()
        events = analyzer._extract_events_from_session(session_file)
        
        if not events:
            return BacktestResult(
                session_file=session_file,
                approach="event_grammar",
                prediction_made=False,
                predicted_cascade_time=0.0,
                confidence=0.0,
                alert_level="NO_EVENTS",
                processing_time_seconds=0.0,
                events_processed=0,
                error_message="No events extracted"
            )
        
        # Calculate event intensity
        intensity_result = intensity_monitor.calculate_event_intensity(events)
        
        processing_time = time.time() - start_time
        
        # Convert to standard result format
        prediction_made = intensity_result.alert_level in ['HIGH', 'RED_ALERT', 'CRITICAL']
        
        # Calculate confidence from intensity and pattern confidence
        confidence = min(1.0, (intensity_result.intensity_score * 0.5 + 
                              intensity_result.pattern_confidence * 0.5))
        
        result = BacktestResult(
            session_file=session_file,
            approach="event_grammar",
            prediction_made=prediction_made,
            predicted_cascade_time=intensity_result.time_to_cascade_minutes,
            confidence=confidence,
            alert_level=intensity_result.alert_level,
            processing_time_seconds=processing_time,
            events_processed=len(events),
            raw_result={
                'intensity_score': intensity_result.intensity_score,
                'crystallization_detected': intensity_result.crystallization_detected,
                'dominant_pattern': intensity_result.dominant_event_pattern,
                'pattern_confidence': intensity_result.pattern_confidence,
                'linguistic_indicators': intensity_result.linguistic_indicators
            }
        )
        
        return result
    
    def _prepare_oracle_input(self, level1_data: Dict) -> Dict:
        """Prepare Oracle input (same as baseline capture)"""
        
        price_movements = level1_data.get('price_movements', [])
        cascade_events = []
        
        for i, movement in enumerate(price_movements):
            cascade_event = {
                'timestamp': movement['timestamp'],
                'price_level': movement['price_level'],
                'event_type': 'price_movement',
                'movement_type': movement.get('movement_type', 'unknown'),
                'event_id': i
            }
            cascade_events.append(cascade_event)
        
        oracle_input = level1_data.copy()
        oracle_input['micro_timing_analysis'] = {
            'cascade_events': cascade_events,
            'total_events': len(cascade_events),
            'analysis_timestamp': datetime.now().isoformat()
        }
        
        return oracle_input
    
    def _compare_approaches(self, oracle_results: List[BacktestResult], 
                          event_grammar_results: List[BacktestResult]) -> Dict[str, Any]:
        """Compare the two approaches"""
        
        print(f"\n📊 COMPARATIVE ANALYSIS")
        print("-" * 25)
        
        # Calculate success rates
        oracle_successful = [r for r in oracle_results if r.prediction_made and not r.error_message]
        grammar_successful = [r for r in event_grammar_results if r.prediction_made and not r.error_message]
        
        oracle_success_rate = len(oracle_successful) / len(oracle_results)
        grammar_success_rate = len(grammar_successful) / len(event_grammar_results)
        
        # Calculate average confidence
        oracle_avg_confidence = np.mean([r.confidence for r in oracle_successful]) if oracle_successful else 0.0
        grammar_avg_confidence = np.mean([r.confidence for r in grammar_successful]) if grammar_successful else 0.0
        
        # Calculate average processing time
        oracle_avg_time = np.mean([r.processing_time_seconds for r in oracle_results])
        grammar_avg_time = np.mean([r.processing_time_seconds for r in event_grammar_results])
        
        comparison = {
            'oracle_success_rate': oracle_success_rate,
            'grammar_success_rate': grammar_success_rate,
            'oracle_avg_confidence': oracle_avg_confidence,
            'grammar_avg_confidence': grammar_avg_confidence,
            'oracle_avg_time': oracle_avg_time,
            'grammar_avg_time': grammar_avg_time,
            'success_rate_difference': grammar_success_rate - oracle_success_rate,
            'confidence_difference': grammar_avg_confidence - oracle_avg_confidence
        }
        
        print(f"   Oracle Success Rate: {oracle_success_rate:.1%}")
        print(f"   Grammar Success Rate: {grammar_success_rate:.1%}")
        print(f"   Confidence Difference: {comparison['confidence_difference']:+.3f}")
        print(f"   Processing Time Ratio: {grammar_avg_time/max(0.001, oracle_avg_time):.1f}x")
        
        return comparison
    
    def _generate_validation_summary(self, oracle_results: List[BacktestResult],
                                   grammar_results: List[BacktestResult],
                                   comparison: Dict[str, Any]) -> ValidationSummary:
        """Generate validation summary"""
        
        # Event-Grammar performance
        grammar_successful = [r for r in grammar_results if r.prediction_made and not r.error_message]
        grammar_accuracy = comparison['grammar_success_rate']
        
        # Validation criteria
        baseline_threshold_met = grammar_accuracy >= self.acceptable_accuracy_threshold
        competitive_with_oracle = abs(comparison['success_rate_difference']) <= 0.1  # Within 10%
        
        validation_passed = baseline_threshold_met and competitive_with_oracle
        
        summary = ValidationSummary(
            total_sessions=len(grammar_results),
            successful_predictions=len(grammar_successful),
            average_accuracy=grammar_accuracy,
            average_confidence=comparison['grammar_avg_confidence'],
            average_processing_time=comparison['grammar_avg_time'],
            accuracy_vs_baseline=comparison['success_rate_difference'],
            validation_passed=validation_passed
        )
        
        return summary
    
    def _display_validation_results(self, summary: ValidationSummary, 
                                  comparison: Dict[str, Any]):
        """Display comprehensive validation results"""
        
        print(f"\n🏆 CRITICAL VALIDATION RESULTS")
        print("=" * 35)
        
        print(f"📊 Event-Grammar Performance:")
        print(f"   Sessions Tested: {summary.total_sessions}")
        print(f"   Successful Predictions: {summary.successful_predictions}")
        print(f"   Accuracy: {summary.average_accuracy:.1%}")
        print(f"   Average Confidence: {summary.average_confidence:.3f}")
        print(f"   Processing Time: {summary.average_processing_time:.3f}s")
        
        print(f"\n⚖️ Validation Criteria:")
        print(f"   Minimum Threshold (85%): {'✅ PASS' if summary.average_accuracy >= 0.85 else '❌ FAIL'}")
        print(f"   Competitive with Oracle: {'✅ PASS' if abs(summary.accuracy_vs_baseline) <= 0.1 else '❌ FAIL'}")
        print(f"   Overall Validation: {'✅ PASSED' if summary.validation_passed else '❌ FAILED'}")
        
        if summary.validation_passed:
            print(f"\n🎉 VALIDATION CHECKPOINT PASSED!")
            print(f"   Event-Grammar architecture meets accuracy requirements")
            print(f"   Ready to proceed with full architecture migration")
        else:
            print(f"\n⚠️ VALIDATION CHECKPOINT FAILED") 
            print(f"   Event-Grammar accuracy: {summary.average_accuracy:.1%}")
            print(f"   Required: ≥85% minimum, competitive with Oracle")
            print(f"   Recommendation: Adjust mathematical transformation or revert to enhancement approach")
    
    def _generate_recommendation(self, summary: ValidationSummary) -> str:
        """Generate recommendation based on validation results"""
        
        if summary.validation_passed:
            return "PROCEED_WITH_EVENT_GRAMMAR_ARCHITECTURE"
        elif summary.average_accuracy >= 0.75:
            return "REFINE_MATHEMATICAL_TRANSFORMATION"
        else:
            return "REVERT_TO_ENHANCEMENT_APPROACH"
    
    def _save_validation_results(self, results: Dict[str, Any]):
        """Save validation results to file"""
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_path = f"event_grammar_backtest_validation_{timestamp}.json"
        
        # Make JSON serializable
        serializable_results = {}
        
        for key, value in results.items():
            if key in ['oracle_results', 'event_grammar_results']:
                serializable_results[key] = []
                for result in value:
                    result_dict = {
                        'session_file': result.session_file,
                        'approach': result.approach,
                        'prediction_made': result.prediction_made,
                        'confidence': result.confidence,
                        'alert_level': result.alert_level,
                        'processing_time_seconds': result.processing_time_seconds,
                        'events_processed': result.events_processed,
                        'error_message': result.error_message
                    }
                    if result.raw_result:
                        result_dict['raw_result'] = result.raw_result
                    serializable_results[key].append(result_dict)
            else:
                serializable_results[key] = value
        
        with open(output_path, 'w') as f:
            json.dump(serializable_results, f, indent=2, default=str)
        
        print(f"\n💾 Validation results saved: {output_path}")

def run_critical_backtest_validation(data_directory: str = "../data/sessions/level_1/") -> Dict[str, Any]:
    """Run the critical backtest validation checkpoint"""
    
    # Find available session files
    data_path = Path(data_directory)
    session_files = list(data_path.glob("*.json"))
    
    if not session_files:
        print(f"❌ No session files found in {data_directory}")
        return {'validation_passed': False, 'error': 'no_data'}
    
    # Limit to manageable number for validation  
    if len(session_files) > 10:
        session_files = session_files[:10]
        
    print(f"Found {len(session_files)} session files for validation")
    
    # Run validation
    validator = EventGrammarBacktestValidator()
    results = validator.run_critical_backtest([str(f) for f in session_files])
    
    return results

if __name__ == "__main__":
    validation_results = run_critical_backtest_validation()