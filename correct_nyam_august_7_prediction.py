#!/usr/bin/env python3
"""
Correct NYAM August 7th Prediction
==================================
Using actual August 7th overnight session data (MIDNIGHT, ASIA, LONDON, PREMARKET)
to predict what will happen when NYAM opens.
"""

import json
from datetime import datetime
from cascade_predictor import CascadePredictor

def load_overnight_sessions():
    """Load all overnight sessions from August 7th."""
    
    sessions = {}
    session_files = {
        'midnight': 'MIDNIGHT_Lvl-1_2025_08_07.json',
        'asia': 'ASIA_Lvl-1_2025_08_07.json', 
        'london': 'LONDON_Lvl-1_2025_08_07.json',
        'premarket': 'PREMARKET_Lvl-1_2025_08_07.json'
    }
    
    print("📁 Loading August 7th overnight sessions...")
    
    for session_name, filename in session_files.items():
        try:
            with open(filename, 'r') as f:
                sessions[session_name] = json.load(f)
            print(f"✅ {session_name.upper()}: {filename}")
        except Exception as e:
            print(f"❌ {session_name.upper()}: {e}")
    
    return sessions

def analyze_overnight_setup(sessions):
    """Analyze overnight sessions to determine NYAM opening conditions."""
    
    predictor = CascadePredictor()
    overnight_analysis = {}
    
    print(f"\n🌙 OVERNIGHT SESSION ANALYSIS:")
    print("=" * 35)
    
    for session_name, session_data in sessions.items():
        print(f"\n📊 {session_name.upper()} SESSION:")
        
        result = predictor.predict_cascade(session_data)
        overnight_analysis[session_name] = result
        
        if result['current_events']:
            print(f"   Events: {' → '.join(result['current_events'][-3:])}")
        
        if result['highest_probability']:
            top = result['highest_probability']
            print(f"   Pattern: {top['cascade_type']} ({top['probability']:.1%})")
            print(f"   Status: {top['expecting_next']}")
        else:
            print("   No significant patterns detected")
    
    return overnight_analysis

def predict_nyam_opening(overnight_analysis, sessions):
    """Generate NYAM opening prediction based on overnight setup."""
    
    print(f"\n🎯 NYAM AUGUST 7TH OPENING PREDICTION")
    print("=" * 40)
    
    # Analyze key factors
    factors = {}
    
    # 1. Asian session momentum
    if 'asia' in overnight_analysis:
        asia_result = overnight_analysis['asia']
        if asia_result['highest_probability']:
            asia_prob = asia_result['highest_probability']['probability']
            factors['asia_momentum'] = 'bullish' if asia_prob > 0.7 else 'neutral'
        else:
            factors['asia_momentum'] = 'neutral'
    
    # 2. London session direction
    if 'london' in overnight_analysis:
        london_result = overnight_analysis['london']
        london_events = london_result.get('current_events', [])
        if 'EXPANSION_HIGH' in london_events:
            factors['london_bias'] = 'bullish'
        elif 'EXPANSION_LOW' in london_events:
            factors['london_bias'] = 'bearish'
        else:
            factors['london_bias'] = 'neutral'
    
    # 3. Premarket setup
    if 'premarket' in overnight_analysis:
        premarket_result = overnight_analysis['premarket']
        premarket_events = premarket_result.get('current_events', [])
        if premarket_events:
            factors['premarket_setup'] = premarket_events[-1]  # Last event
        else:
            factors['premarket_setup'] = 'NEUTRAL'
    
    print(f"🔍 OVERNIGHT FACTOR ANALYSIS:")
    for factor, value in factors.items():
        print(f"   {factor.replace('_', ' ').title()}: {value.upper()}")
    
    # Generate predictions based on overnight setup
    predictions = []
    
    # Scenario 1: Bullish continuation
    if (factors.get('asia_momentum') == 'bullish' and 
        factors.get('london_bias') == 'bullish'):
        predictions.append({
            'scenario': 'Bullish Continuation',
            'pattern': ['OPEN', 'EXPANSION_HIGH', 'TAKEOUT'],
            'probability': 0.85,
            'description': 'Strong overnight momentum continues into NYAM',
            'time_frame': '09:30-09:50',
            'trigger': 'Gap up opening with immediate expansion'
        })
    
    # Scenario 2: FPFVG interaction setup
    if any('FPFVG' in str(session.get('current_events', [])) 
           for session in overnight_analysis.values()):
        predictions.append({
            'scenario': 'FPFVG Interaction Setup',
            'pattern': ['FPFVG_FORMATION', 'INTERACTION', 'REDELIVERY'],
            'probability': 0.78,
            'description': 'Overnight FPFVG gets retested at NYAM open',
            'time_frame': '09:31-10:00',
            'trigger': 'Price interaction with overnight FPFVG levels'
        })
    
    # Scenario 3: Reversal setup
    if (factors.get('premarket_setup') in ['EXPANSION_HIGH', 'TAKEOUT']):
        predictions.append({
            'scenario': 'Momentum Exhaustion Reversal',
            'pattern': ['EXPANSION_HIGH', 'REVERSAL'],
            'probability': 0.72,
            'description': 'Overnight momentum meets resistance and reverses',
            'time_frame': '09:30-09:45',
            'trigger': 'Failed breakout or key level rejection'
        })
    
    # Scenario 4: Consolidation start
    predictions.append({
        'scenario': 'Opening Consolidation',
        'pattern': ['OPEN', 'CONSOLIDATION'],
        'probability': 0.65,
        'description': 'Market consolidates after overnight moves',
        'time_frame': '09:30-10:00',
        'trigger': 'Narrow opening range with low volatility'
    })
    
    # Sort by probability
    predictions.sort(key=lambda x: x['probability'], reverse=True)
    
    # Display predictions
    print(f"\n📈 NYAM OPENING SCENARIOS:")
    print("=" * 25)
    
    for i, pred in enumerate(predictions, 1):
        print(f"\n{i}. {pred['scenario'].upper()}:")
        print(f"   Pattern: {' → '.join(pred['pattern'])}")
        print(f"   Probability: {pred['probability']:.1%}")
        print(f"   Description: {pred['description']}")
        print(f"   Time Frame: {pred['time_frame']}")
        print(f"   Trigger: {pred['trigger']}")
    
    # Primary recommendation
    if predictions:
        top_scenario = predictions[0]
        
        print(f"\n🎯 PRIMARY NYAM AUGUST 7TH PREDICTION:")
        print("=" * 42)
        print(f"🚀 HIGHEST PROBABILITY: {top_scenario['scenario']}")
        print(f"🎲 PROBABILITY: {top_scenario['probability']:.1%}")
        print(f"⏰ TIMING: {top_scenario['time_frame']}")
        print(f"📊 PATTERN: {' → '.join(top_scenario['pattern'])}")
        print(f"🔔 TRIGGER: {top_scenario['trigger']}")
        
        # Trading recommendation
        if top_scenario['probability'] >= 0.8:
            confidence = "HIGH CONFIDENCE"
            action = "Position for primary scenario"
        elif top_scenario['probability'] >= 0.7:
            confidence = "MODERATE CONFIDENCE"
            action = "Monitor opening for confirmation"
        else:
            confidence = "LOW CONFIDENCE"
            action = "Wait for clearer signals"
        
        print(f"\n💡 TRADING RECOMMENDATION:")
        print(f"   {confidence} - {action}")
    
    return predictions

def main():
    """Main execution for correct NYAM August 7th prediction."""
    
    print("🎯 CORRECT NYAM AUGUST 7TH PREDICTION")
    print("=" * 42)
    print("Using actual overnight session data to predict NYAM opening...")
    print()
    
    # Load overnight sessions
    sessions = load_overnight_sessions()
    
    if not sessions:
        print("❌ No overnight session data available")
        return None
    
    # Analyze overnight setup  
    overnight_analysis = analyze_overnight_setup(sessions)
    
    # Generate NYAM predictions
    predictions = predict_nyam_opening(overnight_analysis, sessions)
    
    print(f"\n✅ NYAM AUGUST 7TH PREDICTION COMPLETE")
    print(f"Based on {len(sessions)} overnight sessions")
    print(f"Generated {len(predictions)} scenario predictions")
    
    return predictions

if __name__ == "__main__":
    nyam_predictions = main()