#!/usr/bin/env python3
"""
Compartment Orchestrator

Runs selected processing compartments in sequence with dependency checks
and idempotent planning. Minimal scaffold that reads data_manifest.json and
invokes Lvl1EnhanceCompartment as a first example.

Usage:
  python run_compartments.py --sequence lvl1_enhance \
    --manifest data_manifest.json
"""
from __future__ import annotations
import argparse
import json
from pathlib import Path
from typing import List, Dict, Any, Set

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False

from compartments.lvl1_enhance import Lvl1EnhanceCompartment
from compartments.htf_context import HtfContextCompartment
from compartments.ml_update import MlUpdateCompartment
from compartments.calibration import CalibrationCompartment
from compartments.predict import PredictCompartment
from compartments.accuracy_validation import AccuracyValidationCompartment
from compartments.grammar_bridge import GrammarBridge
from compartments.ab_testing import ABTestingCompartment
from compartments.production_validation import ProductionValidationCompartment
from compartments.base import _hash_json

COMPARTMENT_MAP = {
    "lvl1_enhance": Lvl1EnhanceCompartment,
    "htf_context": HtfContextCompartment,
    "ml_update": MlUpdateCompartment,
    "calibration": CalibrationCompartment,
    "predict": PredictCompartment,
    "accuracy_validation": AccuracyValidationCompartment,
    "ab_testing": ABTestingCompartment,
    "production_validation": ProductionValidationCompartment,
    "grammar_bridge": GrammarBridge,
}


def load_manifest(path: str) -> Dict[str, Any]:
    with open(path, "r") as f:
        return json.load(f)


def load_dag_config(path: str = "compartments.yml") -> Dict[str, Any]:
    """Load compartments DAG configuration"""
    dag_path = Path(path)
    if not dag_path.exists() or not YAML_AVAILABLE:
        # Fallback minimal config
        return {
            "compartments": {
                "lvl1_enhance": {"dependencies": []},
                "htf_context": {"dependencies": ["lvl1_enhance"]},
                "ml_update": {"dependencies": ["lvl1_enhance"]},
                "calibration": {"dependencies": ["ml_update"]},
                "predict": {"dependencies": ["calibration"]}
            },
            "sequences": {
                "data_only": ["lvl1_enhance", "htf_context"],
                "ml_pipeline": ["lvl1_enhance", "ml_update", "calibration"],
                "full_pipeline": ["lvl1_enhance", "htf_context", "ml_update", "calibration", "predict"]
            },
            "config": {"artifacts_manifest": "artifacts_manifest.json"}
        }

    with dag_path.open("r") as f:
        return yaml.safe_load(f)


def topological_sort(compartments: Dict[str, Any]) -> List[str]:
    """Simple topological sort for compartment dependencies"""
    # Build dependency graph
    deps = {name: set(comp.get("dependencies", [])) for name, comp in compartments.items()}

    # Kahn's algorithm
    in_degree = {name: 0 for name in deps}
    for name, dep_set in deps.items():
        for dep in dep_set:
            if dep in in_degree:
                in_degree[name] += 1

    queue = [name for name, degree in in_degree.items() if degree == 0]
    result = []

    while queue:
        current = queue.pop(0)
        result.append(current)

        for name, dep_set in deps.items():
            if current in dep_set:
                in_degree[name] -= 1
                if in_degree[name] == 0:
                    queue.append(name)

    if len(result) != len(compartments):
        raise ValueError("Circular dependency detected in compartments")

    return result


def write_artifacts_manifest(artifacts: List[Dict[str, Any]], path: str = "artifacts_manifest.json"):
    """Write artifacts manifest for tracking"""
    manifest = {
        "generated_by": "run_compartments.py",
        "timestamp": json.dumps({"timestamp": "now"}, default=str),
        "artifacts": artifacts
    }

    with open(path, "w") as f:
        json.dump(manifest, f, indent=2, default=str)


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--sequence", nargs="*", help="Compartment names in order (overrides DAG)")
    ap.add_argument("--full-sequence", action="store_true", help="Run full pipeline from DAG")
    ap.add_argument("--predefined", help="Use predefined sequence from DAG (e.g., data_only, ml_pipeline)")
    ap.add_argument("--manifest", default="data_manifest.json", help="Path to input manifest JSON")
    ap.add_argument("--dag-config", default="compartments.yml", help="Path to DAG configuration")
    ap.add_argument("--dry-run", action="store_true", help="Plan only, do not execute")
    args = ap.parse_args()

    manifest = load_manifest(args.manifest)
    dag_config = load_dag_config(args.dag_config)

    # Determine sequence
    compartments_config = dag_config.get("compartments", {})
    sequences_config = dag_config.get("sequences", {})

    if args.sequence:
        # Manual sequence override
        planned = args.sequence
    elif args.full_sequence:
        # Full pipeline from DAG
        planned = sequences_config.get("full_pipeline", list(compartments_config.keys()))
    elif args.predefined:
        # Predefined sequence
        if args.predefined not in sequences_config:
            raise SystemExit(f"Unknown predefined sequence: {args.predefined}")
        planned = sequences_config[args.predefined]
    else:
        # Default: topological sort of all compartments
        if compartments_config:
            planned = topological_sort(compartments_config)
        else:
            raise SystemExit("No sequence specified and no DAG config found")

    # Validate compartments exist
    for name in planned:
        if name not in COMPARTMENT_MAP:
            raise SystemExit(f"Unknown compartment: {name}")

    print(f"Planned sequence: {planned}")

    if args.dry_run:
        print("Dry-run: no execution.")
        return

    # Execute compartments
    artifacts = []
    for name in planned:
        cls = COMPARTMENT_MAP[name]
        comp = cls()
        ok, reasons = comp.check_dependencies(manifest)
        if not ok:
            raise SystemExit(f"Dependency check failed for {name}: {reasons}")
        key = comp.idempotent_key(manifest)
        print(f"→ Running {name} (key={key[:8]}…)")
        out = comp.run(manifest)
        artifacts.append({"compartment": name, "output": out})
        print(json.dumps({"name": name, "output": out}, indent=2))

    # Write artifacts manifest
    artifacts_path = dag_config.get("config", {}).get("artifacts_manifest", "artifacts_manifest.json")
    write_artifacts_manifest(artifacts, artifacts_path)
    print(f"\n📋 Artifacts manifest written to {artifacts_path}")


if __name__ == "__main__":
    main()

