## Project Oracle — Layered Architecture and Change Guide (for Grok 4)

Last updated: 2025-08-08
Owner repo: project_oracle
Purpose: Give Grok 4 the minimum, sufficient context to reason across modules, avoid breaking changes, and make safe improvements.

---

## 0) Executive Summary

- What it does: Predicts market “cascade” events using a layered architecture: RG Scaler → Fisher Information → Hawkes Engine → ML model (XGBoost) → VQE optimization → Three-Oracle decisioning, orchestrated via modular Processing Compartments.
- Where to start: Use `run_compartments.py` with `--sequence production_validation` for end-to-end validation, or `production_simple.py` for a working API.
- Biggest pitfalls addressed:
  - Script sprawl replaced with compartments and DAG (`compartments.yml`).
  - Timeouts avoided via subprocess isolation and compartment orchestration.
  - Persistence standardized via `storage/adapter.py` and artifacts manifest.
  - Schema and data variance across pipelines (Level-1 vs enhanced sessions) leads to integration mismatches.

---

## 1) High-Level System Map (Layers)

- L1 Public Interfaces
  - REST API: production_simple.py (FastAPI endpoints /predict, /health, /patterns, /stats, /).
  - Batch/CLIs: many scripts for validation, calibration, and case studies (see Section 6).
- L2 Orchestrators
  - run_compartments.py (primary orchestrator with DAG and gates); compartments/production_validation.py (end-to-end validation); three_oracle_architecture.py (decisioning); production_oracle.py (core functions for API).
- L3 Core Predictors (canonical modules)
  - core_predictor/rg_scaler_production.py (RGScaler)
  - core_predictor/fisher_information_monitor.py
  - core_predictor/hawkes_engine.py (EnhancedHawkesEngine)
  - three_oracle_architecture.py (composition/decision)
  - optimization_shell/optimization_shell.py (VQEOptimizationShell)
- L4 Data Pipelines & Schemas
  - data_pipeline/enhanced_data_pipeline.py (Schema v2, synthetic volume integration)
  - data_pipeline/data_factory.py (ingestion skeleton)
  - schema/*.json (dual_layer_schema.json, grammatical_events.json)
- L5 Persistence & Artifacts
  - Unified storage adapter: SqliteDict or JSON fallback via storage/adapter.py
  - Artifacts manifest: artifacts_manifest.json; validation reports under validation/ and production_validation/
  - enhanced_sessions/, enhanced_sessions_batch/ (preprocessed session inputs)
  - Hardcoded state file: processed_files.json path in create_auto_trigger_system.py
- L6 Validation & Tests
  - test_* scripts focusing on imports, invariants, component health, E2E integration stability.
- L7 Tooling & Deployment
  - requirements.txt, deploy.sh, setup_* scripts, deployment/README.md.

---

## 2) Public Entry Points and How to Run

- REST API (recommended for smoke):
  - File: production_simple.py
  - Start: uvicorn production_simple:app --reload
  - Endpoints: /predict (POST), /predict/{id}, /health, /patterns, /stats, /
  - Persistence: Uses SqliteDict if installed, else JSON file oracle_predictions.json.

- Enhanced Oracle loop (data resolution improvements):
  - File: enhanced_oracle_integration.py
  - Factory: create_enhanced_htf_parser(), create_enhanced_rg_scaler(), create_high_density_fisher_monitor() → integrated predictor.

- Isolation runner (avoid import hangs/timeouts):
  - File: oracle_subprocess_runner.py
  - Pattern: Parent process coordinates; child subprocess performs prediction with timeout + JSON IPC + retries.

- Heavy orchestrator (use with care):
  - File: oracle.py (and three_oracle_architecture.py)
  - Known issue: Can hang/timeout due to resource conflicts; prefer isolation.

---

## 3) Core Components and Contracts

- RG Scaler (core_predictor/rg_scaler_production.py)
  - Role: Density-adaptive scaling (s(d) = 15 - 5*log10(d)), canonical scaler for cascades.
  - Contract: Deterministic, pure function shape; use via factory create_production_rg_scaler.

- Fisher Information Monitor (core_predictor/fisher_information_monitor.py)
  - Role: Detects “crystallization” via Fisher information estimates; high-density variant available.
  - Contract: Requires sufficient micro-event rate; enhanced data pipeline improves signal.

- Enhanced Hawkes Engine (core_predictor/hawkes_engine.py)
  - Role: Multi-dimensional Hawkes process for cascade intensity; exposes EnhancedHawkesEngine.
  - Contract: Input must respect schema invariants; calibrated via validation scripts.

- Three-Oracle System (three_oracle_architecture.py)
  - Role: Composition/decision making across sub-oracles; encapsulates domain rules.
  - Contract: Treat as the canonical “decision surface” for production predictions.

- VQE Optimization Shell (optimization_shell/optimization_shell.py)
  - Role: Optional optimizer (e.g., COBYLA) to refine parameters; runs on schedule/frequency.
  - Contract: Side-effectful (writes results JSONs); guard with frequency controls.

---

## 4) Data Flow and Schema

- Inputs
  - Level-1 raw sessions (data/sessions/level_1 or external paths) → see create_auto_trigger_system.py watcher.
  - Enhanced sessions (enhanced_sessions/, enhanced_sessions_batch/) generated by pipeline.

- Processing
  - enhanced_data_pipeline.py: validation (Schema v2), transformation, synthetic volume, caching, performance metrics.
  - Output artifacts: metrics reports (pipeline_metrics_report_*.json), enhanced session files.

- Contracts
  - schema/dual_layer_schema.json: structural constraints for dual-layer data; keep consistent across predictors.
  - schema/grammatical_events.json: event taxonomy used by classifiers/grammars.

- Known fragilities
  - Mixing raw Level-1 and enhanced sessions in the same run path can break assumptions (micro-event density, Fisher thresholds).
  - Hardcoded absolute paths in watchers can drift from the current workspace.

---

## 5) Persistence and State Map

- Predictions store
  - Primary: oracle_predictions.db (SqliteDict, autocommit=True) when sqlitedict is installed.
  - Fallback: oracle_predictions.json (root) with simple load/save helpers.

- Auto-trigger processed state
  - create_auto_trigger_system.py stores processed_files.json at a hardcoded absolute path.
  - Recommendation: make this configurable via env or CLI; default to repo-relative path.

- Artifacts and reports (non-exhaustive)
  - *_validation_*.json in root and validation/
  - *_prediction_*.json in root
  - optimization_shell/vqe_optimization_results_*.json
  - enhanced_* session JSONs in enhanced_sessions*/

- Risks
  - Running API and batch scripts concurrently can write different stores (SQLite vs JSON), causing divergence.
  - File-based state lacks locking; prefer SqliteDict when concurrency is expected.

---

## 6) Script Landscape (curated)

- API/Serving
  - production_simple.py — canonical FastAPI service
  - cascade_prediction_api.py — alternative or legacy API variation

- Orchestrators/Core
  - oracle.py, oracle_core.py, three_oracle_architecture.py, oracle_subprocess_runner.py
  - production_oracle.py, production_cascade_predictor.py, ensemble_production_predictor.py

- Data Pipeline & Prep
  - data_pipeline/enhanced_data_pipeline.py, data_pipeline/data_factory.py
  - processors/lvl1_to_dual_layer_processor.py

- Validation/Calibration (examples)
  - validation/*.py (energy_validator.py, migration_validator.py, test_mathematical_invariants.py)
  - hawkes_mae_validation.py, advanced_mae_validation.py, precision_hawkes_calibrator.py

- Case Studies / Analyses
  - nasdaq_case_study_*.py, event_grammar_*.py, statistical_significance_*.py

- XGBoost-related (optional)
  - xgboost_real_trainer.py, xgboost_context_enhancer.py, demonstrate_automatic_xgboost.py
  - Models: enhanced_xgboost_model.pkl, ensemble_xgboost_model.pkl, etc.

- Tests & Smoke
  - test_without_xgboost.py — exercise core without xgboost
  - test_oracle_native_core.py, test_openmp.py, test_complete_system_integration.py

Note: There are many date-stamped scripts; treat them as experiments or snapshots. Prefer the canonical modules listed in Sections 2–3.

---

## 7) Known Issues and How to Avoid Breakage

- Import hangs/timeouts
  - Cause: heavy, global imports and resource conflicts in oracle.py.
  - Mitigation: Use oracle_subprocess_runner.py for prediction calls; set timeouts and retries.

- Context fragmentation (script proliferation)
  - Cause: changes are introduced as new scripts with divergent wiring.
  - Mitigation: Centralize integration in a small set of canonical factories and entry points; update them instead of cloning.

- Persistence divergence
  - Cause: SqliteDict vs JSON fallback; batch runs vs API runs.
  - Mitigation: Standardize on SqliteDict in production; configure storage adapter explicitly; avoid fallback in multi-process runs.

- Hardcoded paths
  - Cause: create_auto_trigger_system.py uses absolute paths for watch/state files.
  - Mitigation: Parameterize via env or CLI; default to repo-relative paths.

- Schema drift
  - Cause: mixed raw/enhanced paths; changing event grammars without updating consumers.
  - Mitigation: Treat schema/*.json as source of truth; bump schema version and run validation suite before integration.

---

## 8) Canonical Factories and How to Compose

- Use factories to construct predictors consistently:
  - RGScaler: core_predictor/rg_scaler_production.py → create_production_rg_scaler
  - Fisher Monitor: core_predictor/fisher_information_monitor.py → create_fisher_monitor or create_high_density_fisher_monitor
  - Hawkes: core_predictor/hawkes_engine.py → EnhancedHawkesEngine constructor/factory
  - VQE: optimization_shell/optimization_shell.py → create_vqe_optimization_shell
  - Three-Oracle: three_oracle_architecture.py → create system wrapper (if provided)

- Enhanced path (recommended when data supports it):
  - enhanced_oracle_integration.py integrates enhanced HTF parser + RG scaler (high-density) + Fisher monitor.

- Isolation path for production:
  - Wrap calls through oracle_subprocess_runner.py for resilience and clearer resource boundaries.

---

## 9) Change Impact Checklist (use before adding a new script)

1) Does this change belong in an existing canonical module or factory?
2) If new functionality, can it be toggled via configuration/flags instead of a new script?
3) Are data contracts affected (schema/*.json)? If yes, bump version and update validators.
4) Are persistence paths consistent (SqliteDict vs JSON)? Decide and configure explicitly.
5) Add/extend a test in test_without_xgboost.py or test_complete_system_integration.py.
6) Run health checks (/health, /stats) or validation scripts relevant to the change.
7) Document the change in this guide’s “Script Landscape” only if it introduces a new canonical entry point.

---

## 10) Quick Verification Playbook

- Data audit (new)
  - Run: python audit_data_integrity.py --sources data/sessions enhanced_sessions enhanced_sessions_batch --output data_manifest.json --report data_audit_report.md
  - Expect: no schema violations in training candidates; duplicates reported; label counts visible

- API smoke test
  - Start: ORACLE_STORAGE_BACKEND=sqlite uvicorn production_simple:app --reload
  - POST /predict with a minimal valid session (from enhanced_sessions/)
  - GET /health → expect OK with unified storage backend/path reported

- Core imports
  - Run: python test_without_xgboost.py → ensure all core components import and construct

- Enhanced pipeline
  - Run: python data_pipeline/enhanced_data_pipeline.py (module-level factory) or tests to ensure schema v2 flow

- Hawkes calibration
  - Run: python hawkes_mae_validation.py; review *_report_*.json

---

## 11) Environment & Dependencies

- Python packages: see requirements.txt (fastapi, uvicorn, pydantic, sqlitedict, loguru, numpy, pandas, scipy; optional scikit-learn, xgboost, hypothesis).
- OpenMP and xgboost env helpers: setup_openmp_env.sh, setup_xgboost_environment.py
- Deployment helper: deploy.sh (installs minimal deps; generates optional synthetic data; health test stubs)

Recommendation: Pin and install using a virtual environment; prefer enabling sqlitedict in production to avoid JSON race conditions.

---

## 12) Grok 4 Working Rules (Key Constraints)

- Favor these files as sources of truth:
  - production_simple.py (service), enhanced_oracle_integration.py (enhanced path), oracle_subprocess_runner.py (isolation), core_predictor/* (models), three_oracle_architecture.py (decision), optimization_shell/optimization_shell.py (optimizer), data_pipeline/enhanced_data_pipeline.py (data), schema/*.json (contracts), storage/adapter.py (persistence).

- Do NOT:
  - Create new top-level predictors unless a clear interface gap exists; prefer flags/config in existing orchestrators.
  - Introduce new persistence files by default; use storage/adapter.py instead of ad-hoc reads/writes.
  - Hardcode absolute paths; always parameterize and default to repo-relative paths.

- Always:
  - Run audit_data_integrity.py first when touching data or training.
  - Update or add tests when changing predictors or schemas.
  - Run the Quick Verification Playbook.
  - Update this guide if you change canonical entry points.

---

## 13) Glossary

- Cascade: Target market event type; predicted by the system.
- Enhanced sessions: Preprocessed data with higher micro-event density, enabling Fisher crystallization detection.
- Fisher crystallization: Signal that information structure has “solidified,” enabling confident cascade prediction.
- Three-Oracle: Ensemble decision mechanism implementing domain rules.

---

## 14) Pointers to Files (quick jump)

- API: production_simple.py
- Isolation: oracle_subprocess_runner.py
- Orchestrator: oracle.py, three_oracle_architecture.py
- Core predictor modules: core_predictor/*
- Optimization: optimization_shell/optimization_shell.py
- Data pipeline: data_pipeline/enhanced_data_pipeline.py
- Schemas: schema/*.json
- Tests: test_without_xgboost.py, test_oracle_native_core.py, test_complete_system_integration.py
- Validation suite: validation/*.py

---

This guide is designed to minimize context switching and reduce regressions by standardizing on factories, schemas, and a small set of canonical entry points. If a change seems to require “another script,” reconsider and look for a configuration flag or factory parameter first.

