# Predictor Relationships Mapping for Project Oracle

This document maps the relationships between key components in the Project Oracle architecture as specified: density -> scale -> spike -> override.

## Flow of Predictor Relationships

1. **Density (d)**: 
   - Represents the event density or cascade density in the system.
   - Acts as the primary input driving the variance in predictions (80% as per the architecture).
   - Directly influences the scaling factor through the RG Scaler.

2. **Scale (s)**: 
   - Derived from density using the RG Scaler formula `s(d) = 15 - 5*log₁₀(d)`.
   - Adjusts the 'magnification' or resolution at which market data is analyzed.
   - Influences the Hawkes Engine's ability to detect patterns at the correct granularity.

3. **Spike (Fisher Information, F)**: 
   - Monitored by the Fisher Information Monitor.
   - A spike with F > 1000 signals a regime shift from probabilistic to deterministic behavior (15% of regime shifts).
   - Indicates critical crystallization points where cascade events become imminent.
   - Triggers potential overrides or adjustments in prediction methodology.

4. **Override**: 
   - Activated based on Fisher Information spikes (F > 1000).
   - Overrides standard probabilistic predictions with deterministic outputs when a regime shift is detected.
   - Ensures the system adapts to critical market conditions for accurate cascade prediction.

## Summary of Interaction

- **Density** drives the **Scale** through the RG Scaler, setting the analysis resolution.
- **Scale** impacts the Hawkes Engine's pattern detection, potentially leading to the identification of significant **Spikes** via Fisher Information.
- **Spikes** (F > 1000) trigger **Override** mechanisms, shifting the prediction approach to deterministic for high-confidence outputs.

This mapping ensures traceability of the prediction flow and aligns with the crystallized topology of Project Oracle, version 1.0.

## Visual Representation

```
Density (d) → Scale (s(d)) → Spike (F > 1000) → Override (Deterministic Prediction)
```
