## Project Oracle — Risk & Mitigation Addendum (for Grok 4)

Purpose: Turn the identified risks (data, architecture, validation, operations) into concrete, testable procedures and guardrails so Grok 4 can act safely and keep the system coherent.

---

## 1) Data Integrity and Balance Protocol

Objectives
- Single source of truth for sessions and labels
- Schema-conformant datasets (no drift)
- Real-data-first training with transparent synthetic policy

Data Audit (Automatable)
- Enumerate sessions from all sources:
  - Repo: data/sessions/**, enhanced_sessions/**, enhanced_sessions_batch/**
  - External (if used): /Users/<USER>/grok-claude-automation/data/sessions/level_1/**
- Normalize paths and filenames; compute content hash to deduplicate
- Parse metadata (market, date, session type) and label (cascade vs non-cascade)
- Validate each file against schema/dual_layer_schema.json and schema/grammatical_events.json
- Produce report JSON + Markdown:
  - total_count, per-source counts, duplicates_removed
  - class_counts (cascade/non), minority_ratio
  - invalid_files (schema errors), orphaned files, label conflicts

Acceptance Criteria
- 0 duplicates, 0 schema violations in training set
- Minority class ratio between 30–50% (target 30–40%); absolute n_non_cascade_real ≥ 30
- Training/validation splits are file-disjoint by session ID and date

Synthetic Data Policy
- Cap to 20–30 high-fidelity synthetic non-cascades; must validate against schemas
- All synthetic items flagged: provenance: "synthetic" and generator version
- Synthetic excluded from reported accuracy; permitted for training with sample weights ≤ 0.5
- Any metric claim must include: real_only_holdout=TRUE and synthetic_usage=LOW

Deliverables
- data_manifest.json (canonical list of sessions with labels and provenance)
- data_audit_report.json/.md (counts, balance, invalids)

---

## 2) Architectural Governance and Anti-Sprawl

Objectives
- Centralize behavior in canonical factories/entrypoints
- Prevent proliferation of top-level predictors and one-off scripts

Controls
- Script Registry: scripts/registry.yaml enumerates allowed top-level entrypoints with role and owner
- CI Guard: PR fails if new top-level *.py added unless registered or placed under approved module dirs
- Feature Flags, not forks: new behavior added via flags/config to existing factories (enhanced_oracle_integration, core_predictor/*, optimization_shell/*)
- Subprocess Isolation: heavy orchestrations must use oracle_subprocess_runner for production-facing calls

Acceptance Criteria
- No new orchestrator at repo root without registry entry and review
- All predictors construct via factories (e.g., create_production_rg_scaler, create_fisher_monitor)
- production_simple.py uses a storage adapter (see §3) and does not import heavy modules at import-time without guards

---

## 3) Persistence Unification and Safety

Objectives
- Single, explicit storage backend
- No divergence between API and batch jobs

Controls
- Introduce storage/adapter.py (configurable via env/CLI):
  - ORACLE_STORAGE_BACKEND = sqlite | json (default: sqlite if sqlitedict available)
  - ORACLE_STORAGE_PATH (default: ./oracle_predictions.db or ./oracle_predictions.json)
- Adapter provides get(id), put(id, obj), list(), health() with internal locking/atomic writes
- production_simple.py and batch scripts import the adapter instead of ad-hoc reads/writes

Acceptance Criteria
- /health endpoint reports storage backend and path; matches adapter config
- Concurrency test (parallel writes) succeeds without corruption
- No direct file writes to oracle_predictions.json in code outside adapter

---

## 4) Validation: Statistical Rigor and Reporting

Objectives
- Realistic generalization estimates; avoid synthetic-induced Goodhart effects
- Temporal awareness and ablations to detect spurious contributors

Controls
- Temporal CV / Out-of-Time Holdout
  - Split by date/session (past vs future); ensure no leakage across time windows
- Learning Curves
  - Train on increasing n (real-only); report ∂accuracy/∂n; expect < 0.001 near saturation
- Ablation Studies
  - Disable components (RG, VQE, ensemble) to measure Δmetric; require effect sizes and CIs
- Perturbation/Stress Tests
  - Input noise, event jitter, partial-session truncation; measure robustness delta
- Invariants and Schema Pins
  - Run validation/test_mathematical_invariants.py and schema pin checks on each PR

Reporting & Thresholds
- All reported accuracy must be: real_out_of_time=True, report 95% CI via bootstrap or Wilson
- CV std ≤ 3% across folds; if > 3%, gate merge pending investigation
- Ablation effect sizes reported with sign and CI; undocumented gains rejected

Deliverables
- validation/out_of_time_eval.py outputs JSON summaries and plots
- validation/learning_curves.json, ablations.json, perturbation.json

---

## 5) Operational Hardening

Objectives
- Predictable runtime; path/config portability; scalable service posture

Controls
- Config: 12‑factor style via env (ORACLE_*); no hardcoded absolute paths
- Service Health: /health includes storage, component readiness, version hashes
- Timeouts and Circuit Breakers for subprocess runner; bounded retries
- Resource Budgets: max model load time, per-request latency SLIs, concurrency limits
- Deployment Scripts: idempotent, minimal deps; verify with smoke tests

Acceptance Criteria
- create_auto_trigger_system takes watch/state paths from env/CLI; repo-relative defaults
- Uvicorn service stable under N concurrent requests (define N per hardware), P95 latency within target
- All setup_* scripts are optional; service boot succeeds without xgboost/OpenMP

---

## 6) Documentation and Audit Trail

Objectives
- End-to-end traceability from data to model to metrics

Artifacts
- data_manifest.json (authoritative dataset definition)
- model_card.md (training data ranges, features, hyperparams, metrics w/ CI, limitations)
- change_log.md (notable changes, flags, schema bumps)
- Update: documentation/grok4_project_oracle_guide.md to reference this addendum and tooling

---

## 7) Implementation Order of Operations

1) Data Audit Script
- Build audit_data_integrity.py to generate data_manifest.json and data_audit_report.*
- Block training/metrics claims if audit fails

2) Storage Adapter
- Implement storage/adapter.py, refactor production_simple.py and key scripts

3) Script Registry + CI Guard
- Add scripts/registry.yaml and a small checker; integrate GitHub Actions

4) Out-of-Time Validation Harness
- Implement validation/out_of_time_eval.py with temporal splits, learning curves, ablations 

5) Parameterize Hardcoded Paths
- Refactor create_auto_trigger_system.py and similar to use env/CLI

6) Tests and Invariants
- Add tests for dataset counts, label thresholds, schema pins, adapter health

7) Guide Updates
- Link addendum, document workflows and acceptance thresholds

---

## 8) Minimal Commands (once implemented)

- Run data audit: python audit_data_integrity.py --sources data/sessions enhanced_sessions enhanced_sessions_batch
- Start API with unified storage: ORACLE_STORAGE_BACKEND=sqlite uvicorn production_simple:app --reload
- Out-of-time eval: python -m validation.out_of_time_eval --manifest data_manifest.json --oos-window 2025-08-07+
- Ablations: python -m validation.out_of_time_eval --ablate RG,VQE

---

## 9) Guardrails Summary (for Grok 4)

- Do not create new top-level predictors; extend factories via flags
- Do not report metrics that include synthetic holdouts; real_out_of_time only with 95% CI
- Do not write persistence outside storage/adapter.py
- Do not hardcode absolute paths; use ORACLE_* env with repo-relative defaults
- Always run data audit and invariants before any training or major integration

