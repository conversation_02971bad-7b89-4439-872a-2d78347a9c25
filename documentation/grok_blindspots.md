# Grok Blindspots: Critical Domain Knowledge Preservation

**Critical Warning**: This document contains domain-specific knowledge that <PERSON><PERSON> may lack during Project_Oracle development. These patterns and rules are ESSENTIAL for maintaining the 91.1% accuracy of the proven system.

**Preserve these rules exactly during migration - ANY deviation compromises system performance.**

---

## 🚨 FPFVG Formation Rules (87.5% Contamination Filtering)

### **Rule 1: One FPFVG Per Session Maximum**
```python
# IMMUTABLE RULE - Never allow multiple FPFVG formations in single session
MAX_FPFVG_PER_SESSION = 1

def validate_fpfvg_formation(session_events):
    fpfvg_count = count_fpfvg_formations(session_events)
    if fpfvg_count > MAX_FPFVG_PER_SESSION:
        raise FPFVGContaminationError(f"Multiple FPFVG detected: {fpfvg_count}")
```

### **Rule 2: Native vs Inheritance FPFVG Detection**
**Native FPFVG Patterns** (90% cascade probability):
- `.*_FPFVG_formation_premium_high`
- `.*_session_high.*formation`
- `.*_formation_.*_high`

**Inheritance FPFVG Patterns** (0% cascade probability):
- `.*_(Asia|London|PM|Midnight)_FPFVG_redelivered`
- `Previous_Day_.*_FPFVG`
- `Three_Day_.*_FPFVG`

```python
# CRITICAL: This classification determines cascade probability
def classify_fpfvg_type(fpfvg_description):
    if any(pattern in fpfvg_description for pattern in NATIVE_PATTERNS):
        return "native", 0.900  # 90% cascade probability
    elif any(pattern in fpfvg_description for pattern in INHERITANCE_PATTERNS):
        return "inheritance", 0.000  # 0% cascade probability
    else:
        return "unknown", 0.000  # Default to inheritance (safer)
```

### **Rule 3: FPFVG Inheritance Decay**
```python
# Time constant for inheritance decay: τ = 173.1 hours
TAU_INHERITANCE = 173.1
ALPHA_CROSS_SESSION = 0.8

def calculate_inheritance_strength(hours_elapsed):
    return math.exp(-hours_elapsed / TAU_INHERITANCE) * ALPHA_CROSS_SESSION
```

---

## ⚡ Energy Discharge Sequences (70% Carryover Rule)

### **Phase Sequence Rules** (Must occur in exact order):

1. **Consolidation Phase** (d > 60 minutes)
   - Energy accumulation period
   - Distance threshold: `d > 60 minutes`
   - Energy builds below base threshold (0.25)

2. **Primer Phase** (< 30 points movement)
   - Trigger validation phase
   - Movement constraint: `|price_change| < 30 points`
   - Energy approaches base threshold

3. **FPFVG Formation Phase**
   - Structural confirmation
   - Must satisfy FPFVG formation rules above
   - Energy exceeds base threshold (0.25)

4. **Expansion Phase** (> 100 points movement)
   - Energy release phase
   - Movement requirement: `|price_change| > 100 points`
   - Energy discharge toward peak threshold (0.45)

```python
def validate_phase_sequence(session_phases):
    required_sequence = [
        SessionPhases.CONSOLIDATION,
        SessionPhases.PRIMER,
        SessionPhases.FPFVG_FORMATION,
        SessionPhases.EXPANSION
    ]
    
    for i, expected_phase in enumerate(required_sequence):
        if i >= len(session_phases) or session_phases[i] != expected_phase:
            return False
    return True
```

### **Energy Conservation Law**
```python
# 70% energy carries over between sessions - IMMUTABLE
ENERGY_CARRYOVER_RATE = 0.70

def apply_session_transition(previous_session_energy):
    carried_energy = previous_session_energy * ENERGY_CARRYOVER_RATE
    # Remaining 30% is dissipated as "market friction"
    return carried_energy
```

---

## 🔬 Synthetic Volume Detection Algorithms

### **Algorithm 1: Volume Pattern Anomaly Detection**
```python
def detect_synthetic_volume_patterns(volume_data):
    """
    Critical algorithm for identifying artificial volume injection
    87.5% accuracy in contamination detection
    """
    
    # Pattern 1: Unnatural volume spikes (>3σ without news)
    volume_zscore = calculate_zscore(volume_data)
    synthetic_spike_threshold = 3.0
    
    # Pattern 2: Volume clustering at round numbers
    round_number_clustering = detect_round_number_bias(volume_data)
    
    # Pattern 3: Absence of natural volume decay
    decay_pattern_score = analyze_volume_decay(volume_data)
    
    # Combined synthetic score (proprietary weighting)
    synthetic_score = (
        0.4 * (volume_zscore > synthetic_spike_threshold) +
        0.35 * round_number_clustering +
        0.25 * (1 - decay_pattern_score)  # Inverted - natural decay = lower synthetic score
    )
    
    return min(1.0, synthetic_score)
```

### **Algorithm 2: Cross-Session Volume Correlation**
```python
def analyze_cross_session_volume_correlation(sessions):
    """
    Synthetic volume often shows unnatural correlation across sessions
    """
    correlations = []
    for i in range(len(sessions) - 1):
        corr = calculate_correlation(sessions[i].volume, sessions[i + 1].volume)
        correlations.append(corr)
    
    # Natural volume should have low cross-session correlation
    avg_correlation = sum(correlations) / len(correlations)
    synthetic_threshold = 0.7  # Correlation > 0.7 suggests synthetic volume
    
    return avg_correlation > synthetic_threshold
```

---

## 📊 Contamination Inheritance Patterns

### **Pattern 1: Three-Day FPFVG Inheritance**
```python
def track_three_day_inheritance(fpfvg_history):
    """
    FPFVG contamination can persist up to 3 trading days
    Inheritance strength decays exponentially
    """
    inheritance_map = {}
    
    for day_offset in range(1, 4):  # 1, 2, 3 days back
        if day_offset in fpfvg_history:
            decay_factor = math.exp(-day_offset / 1.5)  # 1.5-day half-life
            inheritance_map[f"day_{day_offset}"] = {
                'strength': decay_factor,
                'fpfvg_events': fpfvg_history[day_offset]
            }
    
    return inheritance_map
```

### **Pattern 2: Cross-Session Contamination Transfer**
```python
def calculate_cross_session_contamination(source_session, target_session):
    """
    Contamination transfer between sessions follows specific rules:
    - Asia → London: 85% transfer rate
    - London → NY_AM: 75% transfer rate  
    - NY_AM → NY_PM: 60% transfer rate
    - NY_PM → Next_Asia: 40% transfer rate
    """
    
    transfer_rates = {
        ('asia', 'london'): 0.85,
        ('london', 'ny_am'): 0.75,
        ('ny_am', 'ny_pm'): 0.60,
        ('ny_pm', 'asia'): 0.40,
        # Same-day transfers
        ('london', 'ny_pm'): 0.30,  # Skip NY_AM
        ('asia', 'ny_am'): 0.20     # Cross-continental jump
    }
    
    session_pair = (source_session.session_type, target_session.session_type)
    transfer_rate = transfer_rates.get(session_pair, 0.10)  # Default minimal transfer
    
    contamination_level = source_session.contamination_score * transfer_rate
    return contamination_level
```

---

## 🔄 Intraday Acceleration Factors

### **Acceleration Rule 1: Morning Momentum**
```python
def calculate_morning_acceleration(session_time, base_intensity):
    """
    9:30-10:30 AM ET shows 2.3x acceleration factor
    Critical for NY_AM session predictions
    """
    if is_morning_momentum_window(session_time):  # 9:30-10:30 AM ET
        return base_intensity * 2.3
    elif is_pre_lunch_window(session_time):      # 10:30-12:00 PM ET
        return base_intensity * 1.6
    else:
        return base_intensity
```

### **Acceleration Rule 2: End-of-Session Compression**
```python
def apply_end_of_session_compression(time_remaining, predicted_movement):
    """
    Last 15 minutes of session show movement compression
    Factor ranges from 0.4x to 0.8x depending on session type
    """
    if time_remaining <= 15:  # minutes
        compression_factors = {
            'asia': 0.6,      # Asia session: moderate compression
            'london': 0.4,    # London session: high compression  
            'ny_am': 0.5,     # NY AM: high compression
            'ny_pm': 0.8      # NY PM: minimal compression (rollover effects)
        }
        
        session_type = get_current_session_type()
        compression = compression_factors.get(session_type, 0.6)
        
        return predicted_movement * compression
    
    return predicted_movement
```

### **Acceleration Rule 3: News Impact Multipliers**
```python
def calculate_news_impact_acceleration(news_events, base_prediction):
    """
    News events create acceleration multipliers based on:
    - Event significance (FOMC: 3.5x, NFP: 2.8x, CPI: 2.4x)
    - Time proximity (exponential decay from news release)
    - Market session (London/NY_AM more reactive than Asia/NY_PM)
    """
    
    news_multipliers = {
        'FOMC': 3.5,
        'NFP': 2.8,
        'CPI': 2.4,
        'GDP': 2.1,
        'retail_sales': 1.8,
        'unemployment': 1.6
    }
    
    total_acceleration = 1.0
    
    for event in news_events:
        if event.type in news_multipliers:
            time_decay = math.exp(-event.minutes_since_release / 30)  # 30-min half-life
            session_reactivity = get_session_reactivity_factor()
            
            event_acceleration = (
                news_multipliers[event.type] * 
                time_decay * 
                session_reactivity
            )
            
            total_acceleration *= (1 + event_acceleration * 0.1)  # Additive with 10% weighting
    
    return base_prediction * total_acceleration
```

---

## 🎯 Session-Specific Blind Spots

### **Asia Session Peculiarities**
- **Volume Patterns**: Lower volume can trigger higher sensitivity (inverse relationship)
- **Weekend Carryover**: Friday close events retain 84.7% influence strength
- **Thin Market Effects**: Price movements appear exaggerated due to low liquidity

### **London Session Peculiarities**  
- **Overlap Effects**: London/Asia overlap (2:00-4:00 AM ET) shows unique correlation patterns
- **Brexit Sensitivity**: GBP-related events create 1.8x multiplier even for non-GBP instruments
- **Energy Market Correlation**: Oil price movements show 0.6 correlation with index cascades

### **NY Sessions Peculiarities**
- **AM Session**: Pre-market futures correlation weakens after 10:00 AM ET
- **PM Session**: Extremely low gamma (0.000163) requires fallback to 0.143 for predictions
- **Lunch Hour**: 12:00-1:00 PM ET shows movement suppression (0.3x factor)

---

## ⚠️ Critical Implementation Warnings

### **Warning 1: Floating Point Precision**
```python
# NEVER use standard float for critical calculations
# Always use Decimal with high precision
from decimal import Decimal, getcontext
getcontext().prec = 50

# WRONG:
energy_threshold = 1.5
carryover_rate = 0.70

# CORRECT:
energy_threshold = Decimal('1.5')
carryover_rate = Decimal('0.70')
```

### **Warning 2: Session Boundary Edge Cases**
```python
def handle_session_boundary_edge_case(timestamp, session_data):
    """
    Critical: Sessions can extend 2-3 minutes beyond official close
    Energy calculations must account for this overlap
    """
    official_close = session_data.official_close_time
    extended_close = official_close + timedelta(minutes=3)
    
    if official_close <= timestamp <= extended_close:
        # Apply boundary transition rules
        return apply_boundary_energy_rules(session_data, timestamp)
    
    return standard_energy_calculation(session_data, timestamp)
```

### **Warning 3: Cascade Type Boundary Conditions**
```python
def handle_cascade_boundary_conditions(magnitude):
    """
    Edge case: Magnitude exactly at boundary (e.g., 0.30)
    Must consistently assign to lower bracket
    """
    for cascade_type in CASCADE_TYPES_V1.get_all_types().values():
        # Use < for max threshold (not <=) to avoid double-assignment
        if cascade_type.threshold_min <= magnitude < cascade_type.threshold_max:
            return cascade_type
    
    # Handle edge case of magnitude = 1.0 exactly
    if magnitude == Decimal('1.0'):
        return CASCADE_TYPES_V1.TYPE_5
    
    return None  # Invalid magnitude
```

---

## 🔐 Validation Checkpoints

### **Checkpoint 1: Mathematical Invariant Verification**
Run `test_mathematical_invariants.py` after ANY change to ensure:
- All constants unchanged
- Cascade type coverage complete  
- Theory weights sum to 1.0
- Energy conservation laws preserved

### **Checkpoint 2: FPFVG Classification Test**
```python
def test_fpfvg_classification_accuracy():
    """
    Must maintain 87.5% contamination filtering accuracy
    """
    test_cases = load_fpfvg_test_dataset()
    correct_classifications = 0
    
    for test_case in test_cases:
        predicted_type = classify_fpfvg_type(test_case.description)
        if predicted_type == test_case.actual_type:
            correct_classifications += 1
    
    accuracy = correct_classifications / len(test_cases)
    assert accuracy >= 0.875, f"FPFVG accuracy dropped to {accuracy:.3f}"
```

### **Checkpoint 3: Energy Carryover Validation**
```python
def test_energy_carryover_conservation():
    """
    Verify 70% carryover rule across session transitions
    """
    test_sessions = load_session_transition_test_data()
    
    for transition in test_sessions:
        initial_energy = transition.previous_session.final_energy
        carried_energy = apply_energy_carryover(initial_energy)
        actual_energy = transition.current_session.initial_energy
        
        # Allow 1% tolerance for measurement noise
        assert abs(carried_energy - actual_energy) <= 0.01 * initial_energy
```

---

## 📋 Migration Checklist

**Before migrating ANY component, verify:**

- [ ] Mathematical constants extracted to `constraints.py`
- [ ] FPFVG formation rules preserved exactly
- [ ] Energy discharge sequence validation implemented
- [ ] Synthetic volume detection algorithms transferred
- [ ] Contamination inheritance patterns maintained
- [ ] Intraday acceleration factors included
- [ ] Session-specific peculiarities documented
- [ ] Edge case handling for boundary conditions
- [ ] Validation checkpoints pass at 100%
- [ ] Parallel system testing shows identical outputs

**Post-migration validation:**
- [ ] Run complete test suite (`test_mathematical_invariants.py`)
- [ ] Verify 91.1% accuracy maintained on validation dataset
- [ ] Check 87.5% FPFVG contamination filtering accuracy
- [ ] Validate energy conservation across session transitions
- [ ] Test edge cases and boundary conditions

---

**REMEMBER: Better verbose + correct than clean + wrong**

This document preserves critical domain knowledge that took months to discover and validate. Every rule exists because it improves system accuracy. Do not sacrifice correctness for code elegance during migration.