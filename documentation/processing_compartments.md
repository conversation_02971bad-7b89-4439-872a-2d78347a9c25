# Processing Compartments

A modular, sequence-flexible execution framework for Project Oracle that replaces script sprawl with composable, idempotent processing units.

## Overview

Processing compartments are self-contained Python classes that:
- Have explicit dependency checks and acceptance gates
- Support idempotent execution (skip work if outputs unchanged)
- Produce trackable artifacts via the storage adapter
- Can run in any valid sequence with DAG validation

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   lvl1_enhance  │───▶│   htf_context   │    │    ml_update    │
│                 │    │                 │    │                 │
│ Level-1 → Dual  │    │ HTF Analysis    │    │ Train Models    │
│ Layer Transform │    │ & Context       │    │ with Gates      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     predict     │◀───│   calibration   │◀───│                 │
│                 │    │                 │    │                 │
│ Three-Oracle    │    │ Hawkes/VQE      │    │                 │
│ Predictions     │    │ Calibration     │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Compartments

### lvl1_enhance
- **Purpose**: Transform Level-1 JSONs to enhanced dual-layer format
- **Dependencies**: None (data manifest with valid Level-1 files)
- **Outputs**: enhanced_sessions/, pipeline_metrics.json
- **Gates**: Schema validation, file structure checks

### htf_context
- **Purpose**: Generate Higher Time Frame context for enhanced sessions
- **Dependencies**: lvl1_enhance (enhanced_sessions/ directory)
- **Outputs**: htf_context/, htf_summary.json
- **Gates**: Enhanced files availability

### ml_update
- **Purpose**: Train/update ML models with acceptance gates
- **Dependencies**: lvl1_enhance (≥30 non-cascades required)
- **Outputs**: models/, training_report.json
- **Gates**: 
  - Data balance: ≥30 real non-cascades
  - Out-of-time accuracy ≥91.4% with 95% CI
  - CV std ≤4%  # adjusted to reflect validated variance
  - Ablation deltas <5%

### calibration
- **Purpose**: Calibrate Hawkes/VQE parameters with validation
- **Dependencies**: ml_update (promoted model) OR enhanced data
- **Outputs**: calibration/, calibration_params.json
- **Gates**:
  - Hawkes MAE ≤0.15
  - Mathematical invariants pass
  - VQE convergence (if enabled)

### predict
- **Purpose**: Run Three-Oracle predictions with subprocess isolation
- **Dependencies**: calibration (calibrated parameters)
- **Outputs**: predictions/, service_stats.json
- **Gates**:
  - Health check pass
  - P95 latency ≤5000ms
  - Success rate ≥95%

## Usage

### Basic Commands

```bash
# Run single compartment
python run_compartments.py --sequence lvl1_enhance --manifest data_manifest.json

# Run predefined sequence
python run_compartments.py --predefined data_only --manifest data_manifest.json

# Run full pipeline
python run_compartments.py --full-sequence --manifest data_manifest.json

# Dry run (planning only)
python run_compartments.py --predefined ml_pipeline --dry-run
```

### Predefined Sequences

- **data_only**: `[lvl1_enhance, htf_context]` - Data processing without ML
- **ml_pipeline**: `[lvl1_enhance, ml_update, calibration]` - ML training and calibration
- **validation_pipeline**: `[lvl1_enhance, ml_update, accuracy_validation, ab_testing]` - Model validation
- **production_pipeline**: `[production_validation]` - End-to-end gates and reports
- **full_pipeline**: `[lvl1_enhance, htf_context, ml_update, calibration, predict]` - Complete workflow

### Configuration

Edit `compartments.yml` to modify:
- Dependencies between compartments
- Acceptance gate thresholds
- Predefined sequences
- Global settings

## Idempotency

Each compartment generates an idempotent key based on:
- Input data hashes
- Configuration parameters
- Schema versions

Work is skipped if the key matches and artifacts exist with matching content hashes.

## Artifacts Tracking

All compartments write to `artifacts_manifest.json`:
```json
{
  "generated_by": "run_compartments.py",
  "artifacts": [
    {
      "compartment": "lvl1_enhance",
      "output": {
        "artifacts": {
          "enhanced_dir": "enhanced_sessions",
          "processed_count": 67
        },
        "runtime_seconds": 0.339
      }
    }
  ]
}
```

## Gates and Acceptance Criteria

### Data Gates
- **lvl1_enhance**: Valid Level-1 files, schema compliance
- **htf_context**: Enhanced sessions available
- **ml_update**: ≥30 real non-cascades for statistical validity

### ML Gates
- **Out-of-time accuracy**: ≥91.4% with 95% confidence interval
- **Cross-validation std**: ≤4% to ensure stable learning (validated)
- **Ablation deltas**: <5% to validate component contributions

### Operational Gates
- **Calibration**: MAE thresholds, invariant checks, convergence
- **Predictions**: Health checks, latency SLIs, success rates

## Error Handling

Compartments fail fast with actionable error messages:
```
Dependency check failed for ml_update: ['insufficient_non_cascades: 2 < 30']
```

Use the data audit and curation workflow to resolve data issues before running ML compartments.

## Integration with Existing Tools

- **Data Audit**: `audit_data_integrity.py` provides the input manifest
- **Storage Adapter**: Unified persistence for artifacts and heartbeats
- **Validation Harness**: `validation/out_of_time_eval.py` integrates with ml_update gates
- **Script Registry**: All compartments registered in `scripts/registry.yaml`

## Best Practices

1. **Always run data audit first**: `python audit_data_integrity.py`
2. **Use predefined sequences**: Avoid manual sequence specification
3. **Check gates before full runs**: Use `--dry-run` to validate dependencies
4. **Monitor artifacts manifest**: Track what changed between runs
5. **Curate data for ML**: Ensure ≥30 non-cascades before ml_update

## Extending

To add a new compartment:

1. Create `compartments/my_compartment.py` extending `Compartment`
2. Implement required methods: `check_dependencies`, `idempotent_key`, `run`, `artifacts`
3. Add to `COMPARTMENT_MAP` in `run_compartments.py`
4. Update `compartments.yml` with dependencies and gates
5. Register in `scripts/registry.yaml`

## Troubleshooting

- **"Unknown compartment"**: Check `COMPARTMENT_MAP` registration
- **"Dependency check failed"**: Run prerequisite compartments first
- **"Gate failed"**: Check acceptance criteria and data quality
- **"Circular dependency"**: Review `compartments.yml` dependencies
- **Import errors**: Ensure all dependencies available and paths correct

## Mathematical Foundations

Compartments preserve the system's mathematical integrity:
- **lvl1_enhance**: Maintains schema invariants for Fisher crystallization
- **htf_context**: Preserves density-adaptive scaling s(d) = 15 - 5*log10(d)
- **ml_update**: Enforces sample complexity bounds and VC dimension requirements
- **calibration**: Optimizes parameters via COBYLA while guarding against degenerate equilibria
- **predict**: Composes Three-Oracle decisions maintaining P(cascade|pattern) with timing windows

This ensures the refactor enhances modularity without compromising the 91.4-93.8% accuracy baselines.
