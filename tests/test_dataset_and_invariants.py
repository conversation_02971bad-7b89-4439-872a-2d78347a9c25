#!/usr/bin/env python3
"""
Lightweight checks for dataset and invariants.

Run:
  python tests/test_dataset_and_invariants.py
"""
from __future__ import annotations
import json
from pathlib import Path


def load_json(path: Path):
    with path.open("r") as f:
        return json.load(f)


def main():
    root = Path(__file__).resolve().parent.parent
    # Ensure repo root on sys.path for package imports
    import sys
    if str(root) not in sys.path:
        sys.path.insert(0, str(root))

    manifest_path = root / "data_manifest.json"
    assert manifest_path.exists(), "data_manifest.json not found. Run audit_data_integrity.py first."

    manifest = load_json(manifest_path)
    totals = manifest.get("totals", {})
    assert totals.get("duplicate_groups", -1) == 0, "Duplicate groups present. Run audit and dedupe."

    labels = totals.get("labels", {})
    cascade = labels.get("cascade", 0)
    non_cascade = labels.get("non_cascade", 0)
    print(f"Label counts: cascade={cascade}, non_cascade={non_cascade}")

    # Warn (do not fail) if minority below target; gates will catch in validation
    if non_cascade < 30:
        print("WARNING: non_cascade < 30; out_of_time_eval will skip. Curate overrides.")

    # Storage adapter health
    from storage.adapter import create_storage_adapter
    storage = create_storage_adapter()
    health = storage.health()
    storage.close()
    assert "backend" in health and "path" in health, "Storage adapter health missing backend/path."

    # Schema files exist
    assert (root / "schema/dual_layer_schema.json").exists(), "Missing dual_layer_schema.json"
    assert (root / "schema/grammatical_events.json").exists(), "Missing grammatical_events.json"

    print("All checks passed (with warnings possible).")


if __name__ == "__main__":
    main()

