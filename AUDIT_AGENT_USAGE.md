# Code Audit Guardian - Usage Guide
=====================================

## Overview

The Code Audit Guardian (`audit_agent.py`) is a specialized security-focused agent that protects critical Python codebases from accidental modifications and ensures code integrity. It provides comprehensive analysis through git-based change detection, AST analysis, and guard decorator verification.

## Features

### 🔍 Git Status Analysis
- Detects uncommitted changes in critical files
- Identifies staged vs unstaged modifications
- Counts number of changes per file
- Handles both tracked and untracked files

### 🛡️ Guard Decorator Verification  
- Scans for `@guard.register` decorators on protected functions
- Detects missing guard decorators that should be present
- Validates decorator integrity and completeness
- Identifies functions that lost their protection

### 📈 Semantic Drift Detection
- Compares current working directory against last commit
- Uses AST parsing to detect structural changes
- Calculates drift percentage based on function changes
- Identifies added, removed, or modified functions

### 📊 Concise Reporting
- Generates clean, scannable reports with emoji indicators
- Uses standardized table format for easy reading
- Provides specific issue details and recommendations
- Saves detailed reports to markdown files

## Usage

### Basic Usage
```bash
# Run audit from project directory
python3 audit_agent.py

# Run with custom repo path
python3 audit_agent.py --repo-path /path/to/repo
```

### Expected Output Format
```
🔍 CODE INTEGRITY AUDIT REPORT
=====================================

📊 GIT STATUS CHECK
File                 | Status        | Changes
---------------------|---------------|--------
oracle_core.py      | ✅ Clean     | 0
invariants.py       | ⚠️ Modified  | 3
production_simple.py| ❌ Unstaged  | 7

🛡️ GUARD DECORATOR CHECK  
File                 | Guards Found  | Status
---------------------|---------------|--------
oracle_core.py      | 5/5          | ✅ Pass
invariants.py       | 2/2          | ✅ Pass  
production_simple.py| 3/4          | ❌ Missing @guard on validate_oracle()

📈 SEMANTIC DRIFT CHECK
File                 | Drift Score   | Status
---------------------|---------------|--------
oracle_core.py      | 2.1%         | ✅ Acceptable
invariants.py       | 15.7%        | ⚠️ Moderate
production_simple.py| 28.3%        | ❌ High Drift

🎯 SUMMARY: 2 issues detected - Review production_simple.py
```

## Target Files

The audit agent monitors these critical files:

1. **oracle_core.py** - Core grammatical parsing functions
2. **invariants.py** - Guard decorator definitions and control system  
3. **production_oracle.py** - Production-ready CFG implementation
4. **production_simple.py** - Simple deployment stack (FastAPI)

## Status Indicators

### Git Status
- ✅ **Clean**: No uncommitted changes
- ⚠️ **Modified/Staged**: Changes detected but manageable  
- ❌ **Unstaged/Many Changes**: Significant uncommitted changes

### Guard Decorators
- ✅ **Pass**: All expected guards present
- ❌ **Missing**: Guard decorators removed or missing

### Semantic Drift
- ✅ **Acceptable**: <10% drift (minor changes)
- ⚠️ **Moderate**: 10-25% drift (review recommended)
- ❌ **High Drift**: >25% drift (significant structural changes)

## Integration

### As Pre-Commit Hook
```bash
# Add to .git/hooks/pre-commit
#!/bin/bash
python3 project_oracle/audit_agent.py
if [ $? -eq 1 ]; then
    echo "❌ Audit failed - commit blocked"
    exit 1
fi
```

### As CI/CD Check
```yaml
# GitHub Actions example
- name: Code Integrity Audit
  run: |
    cd project_oracle
    python3 audit_agent.py
    if [ $? -eq 1 ]; then
      echo "::error::Critical audit failures detected"
      exit 1
    fi
```

### Programmatic Usage
```python
from audit_agent import GitAuditor

# Initialize auditor
auditor = GitAuditor("/path/to/repo")

# Run comprehensive audit
report = auditor.run_audit()

# Check for critical issues
if report.system_health == "CRITICAL":
    print("❌ Critical issues detected!")
    for issue in report.critical_issues:
        print(f"   - {issue}")

# Generate formatted report
formatted_report = auditor.format_audit_report(report)
print(formatted_report)
```

## Key Classes and Methods

### GitAuditor
- `__init__(repo_path)` - Initialize with repository path
- `run_audit()` - Execute complete audit and return AuditReport
- `audit_file(file_path)` - Audit a single file
- `format_audit_report(report)` - Format report in table format

### AuditReport
- `timestamp` - When audit was performed
- `total_files` - Number of files audited
- `files_with_issues` - Count of files with problems
- `critical_issues` - List of critical problems found
- `system_health` - Overall system status (EXCELLENT/GOOD/CRITICAL)

### FileAuditResult
- `file_path` - Path to audited file
- `git_status` - Git status (Clean/Modified/Staged/Untracked)
- `guards_found/expected` - Guard decorator counts
- `drift_score` - Semantic drift percentage
- `missing_guards` - List of missing guard decorators

## Error Handling

The audit agent gracefully handles:
- Missing git repositories (falls back to file-only analysis)
- Unparseable Python files (reports parsing errors)
- Missing target files (reports as not found)
- Git operation failures (continues with available data)

## Dependencies

- **GitPython** - Git repository operations
- **ast** - Python AST parsing (built-in)
- **pathlib** - File system operations (built-in)

Install dependencies:
```bash
pip3 install GitPython
```

## Security Considerations

- The agent performs **read-only** operations only
- No modifications are made to source files
- Git operations are limited to status and diff queries
- AST parsing is safe and sandboxed
- All file access uses absolute paths to prevent directory traversal

## Performance

- **Git Operations**: O(1) per file for status checks
- **AST Parsing**: O(n) where n = lines of code per file
- **Overall Runtime**: <1 second for typical project sizes
- **Memory Usage**: Minimal, processes files individually

The Code Audit Guardian provides essential protection for critical codebases while maintaining excellent performance and usability characteristics.