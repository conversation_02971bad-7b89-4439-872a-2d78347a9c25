"""
HTF Context Compartment

Generates Higher Time Frame (HTF) context artifacts for enhanced sessions.
Produces sidecar context files with synthetic volume integration and 
temporal analysis for improved cascade prediction accuracy.
"""
from __future__ import annotations
from typing import Dict, Any, Tuple, List
from pathlib import Path
import json
import time
import logging

from compartments.base import Compartment, _hash_json
from storage.adapter import create_storage_adapter


class HtfContextCompartment(Compartment):
    name = "htf_context"

    def __init__(self, context_dir: str = "htf_context", config: Dict[str, Any] = None):
        self.context_dir = Path(context_dir)
        self.config = config or {"volume_integration": True, "temporal_windows": [5, 15, 30]}
        self._artifacts: Dict[str, str] = {}
        self.logger = logging.getLogger(__name__)

    def check_dependencies(self, input_manifest: Dict[str, Any]) -> Tuple[bool, list]:
        reasons = []
        
        # Check for enhanced sessions directory
        enhanced_dir = Path("enhanced_sessions")
        if not enhanced_dir.exists():
            reasons.append("enhanced_sessions directory not found - run lvl1_enhance first")
        
        # Check for at least some enhanced files
        enhanced_files = list(enhanced_dir.glob("enhanced_*.json")) if enhanced_dir.exists() else []
        if len(enhanced_files) == 0:
            reasons.append("no enhanced session files found - run lvl1_enhance first")
        
        return (len(reasons) == 0), reasons

    def idempotent_key(self, input_manifest: Dict[str, Any]) -> str:
        # Hash enhanced files + config for idempotency
        enhanced_dir = Path("enhanced_sessions")
        enhanced_files = []
        
        if enhanced_dir.exists():
            for f in enhanced_dir.glob("enhanced_*.json"):
                try:
                    stat = f.stat()
                    enhanced_files.append({
                        "path": str(f),
                        "size": stat.st_size,
                        "mtime": stat.st_mtime
                    })
                except Exception:
                    pass
        
        key_data = {
            "enhanced_files": sorted(enhanced_files, key=lambda x: x.get("path", "")),
            "config": self.config,
            "context_dir": str(self.context_dir)
        }
        return _hash_json(key_data)

    def run(self, input_manifest: Dict[str, Any]) -> Dict[str, Any]:
        ok, reasons = self.check_dependencies(input_manifest)
        if not ok:
            raise ValueError(f"Dependency check failed: {reasons}")

        start = time.time()
        self.context_dir.mkdir(parents=True, exist_ok=True)

        # Find enhanced session files
        enhanced_dir = Path("enhanced_sessions")
        enhanced_files = list(enhanced_dir.glob("enhanced_*.json"))
        
        self.logger.info(f"Generating HTF context for {len(enhanced_files)} enhanced sessions")

        context_files = []
        processed_count = 0
        
        for enhanced_file in enhanced_files:
            try:
                # Load enhanced session
                with enhanced_file.open("r") as f:
                    enhanced_data = json.load(f)
                
                # Extract session metadata
                level1_data = enhanced_data.get("level1_json", {})
                session_metadata = level1_data.get("session_metadata", {})
                session_type = session_metadata.get("session_type", "UNKNOWN")
                session_date = session_metadata.get("session_date", "unknown")
                
                # Generate HTF context (scaffold implementation)
                htf_context = self._generate_htf_context(enhanced_data)
                
                # Create context filename
                stem = enhanced_file.stem.replace("enhanced_", "")
                context_file = self.context_dir / f"htf_context_{stem}.json"
                
                # Save context
                with context_file.open("w") as f:
                    json.dump(htf_context, f, indent=2, default=str)
                
                context_files.append(str(context_file))
                processed_count += 1
                self.logger.info(f"HTF context: {enhanced_file.name} → {context_file.name}")
                
            except Exception as e:
                self.logger.error(f"Failed to generate HTF context for {enhanced_file}: {e}")

        # Generate summary context
        summary_context = self._generate_summary_context(context_files)
        summary_file = self.context_dir / "htf_summary.json"
        with summary_file.open("w") as f:
            json.dump(summary_context, f, indent=2, default=str)

        runtime = time.time() - start
        
        # Record artifacts
        self._artifacts = {
            "context_dir": str(self.context_dir),
            "context_files": context_files,
            "summary_file": str(summary_file),
            "processed_count": processed_count
        }
        
        # Storage heartbeat
        storage = create_storage_adapter()
        try:
            storage.put(f"artifact_{self.name}", {
                "compartment": self.name,
                "artifacts": self._artifacts,
                "runtime_seconds": round(runtime, 3),
                "processed_count": processed_count
            })
        finally:
            storage.close()

        self.logger.info(f"Completed: {processed_count} HTF contexts generated in {runtime:.2f}s")
        
        return {
            "compartment": self.name,
            "artifacts": self._artifacts,
            "runtime_seconds": round(runtime, 3),
            "processed_count": processed_count
        }

    def _generate_htf_context(self, enhanced_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate HTF context for a single enhanced session (scaffold)"""
        level1_data = enhanced_data.get("level1_json", {})
        enhanced_meta = enhanced_data.get("enhanced_data", {})
        
        # Extract basic session info
        session_metadata = level1_data.get("session_metadata", {})
        session_type = session_metadata.get("session_type", "UNKNOWN")
        session_date = session_metadata.get("session_date", "unknown")
        
        # Scaffold HTF analysis
        htf_context = {
            "session_info": {
                "session_type": session_type,
                "session_date": session_date,
                "quality_score": enhanced_meta.get("quality_score", 0.0)
            },
            "temporal_analysis": {
                "windows": self.config.get("temporal_windows", [5, 15, 30]),
                "volume_integration": self.config.get("volume_integration", True),
                "synthetic_volume_available": enhanced_meta.get("synthetic_volume_components") is not None
            },
            "context_features": {
                "density_adaptive_scaling": "s(d) = 15 - 5*log10(d)",
                "fisher_crystallization_ready": enhanced_meta.get("quality_score", 0) > 0.7,
                "hawkes_intensity_context": "multi-dimensional point process ready"
            },
            "processing_metadata": {
                "generated_by": self.name,
                "timestamp": time.time(),
                "config": self.config
            }
        }
        
        return htf_context

    def _generate_summary_context(self, context_files: List[str]) -> Dict[str, Any]:
        """Generate summary HTF context across all sessions"""
        return {
            "summary": {
                "total_contexts": len(context_files),
                "generation_timestamp": time.time(),
                "config": self.config
            },
            "context_files": context_files,
            "notes": [
                "HTF context provides temporal analysis for enhanced cascade prediction",
                "Integrates synthetic volume and density-adaptive scaling",
                "Ready for Fisher crystallization detection and Hawkes intensity modeling"
            ]
        }

    def artifacts(self) -> Dict[str, str]:
        return dict(self._artifacts)
