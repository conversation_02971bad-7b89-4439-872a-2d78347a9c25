#!/usr/bin/env python3
"""
Accuracy Resolution Framework - Comprehensive Validation System
==============================================================

Implements comprehensive accuracy validation with statistical testing,
confidence intervals, and performance benchmarking against historical baselines.

Key Features:
- Statistical significance testing with confidence intervals
- Temporal validation with out-of-time testing
- Baseline comparison against 91.1% Oracle accuracy
- Cross-validation with stability assessment
- Performance regression detection
- A/B testing preparation framework

Integration:
- Uses real ML models from ml_update compartment
- Leverages existing validation infrastructure
- Provides statistical rigor for production deployment
"""

import json
import time
import logging
import numpy as np
import pickle
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass
from scipy import stats
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix
from sklearn.model_selection import cross_val_score, TimeSeriesSplit

from compartments.base import Compartment, _hash_json
from storage.adapter import create_storage_adapter

@dataclass
class AccuracyMetrics:
    """Comprehensive accuracy metrics with statistical validation"""
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    confidence_interval_lower: float
    confidence_interval_upper: float
    p_value: float
    statistical_significance: bool
    sample_size: int
    baseline_comparison: float

@dataclass
class ValidationReport:
    """Complete validation report with all metrics"""
    overall_metrics: AccuracyMetrics
    temporal_validation: Dict[str, Any]
    cross_validation: Dict[str, Any]
    baseline_comparison: Dict[str, Any]
    statistical_tests: Dict[str, Any]
    production_readiness: Dict[str, Any]
    recommendations: List[str]

class AccuracyValidationCompartment(Compartment):
    name = "accuracy_validation"

    def __init__(self, validation_dir: str = "validation", config: Dict[str, Any] = None):
        self.validation_dir = Path(validation_dir)
        self.config = config or {
            "baseline_accuracy": 0.911,  # Oracle's 91.1% baseline
            "confidence_level": 0.95,
            "min_sample_size": 30,
            "significance_threshold": 0.05,
            "temporal_split_ratio": 0.8,
            "cv_folds": 5
        }
        self._artifacts: Dict[str, str] = {}
        self.logger = logging.getLogger(__name__)

    def run(self, input_manifest: Dict[str, Any]) -> Dict[str, Any]:
        """Run comprehensive accuracy validation"""
        self.logger.info("Starting comprehensive accuracy validation")
        
        start = time.time()
        self.validation_dir.mkdir(parents=True, exist_ok=True)

        # Load trained model and data
        model, X, y, feature_names = self._load_model_and_data()
        
        if model is None:
            self.logger.error("No trained model available for validation")
            return self._create_error_output("No trained model available")

        # Perform comprehensive validation
        validation_report = self._perform_comprehensive_validation(model, X, y, feature_names)
        
        # Check production readiness gates
        production_ready = self._assess_production_readiness(validation_report)
        
        # Save artifacts
        report_path = self.validation_dir / "accuracy_validation_report.json"
        metrics_path = self.validation_dir / "statistical_metrics.json"
        
        # Convert validation report to JSON-serializable format
        report_dict = self._make_json_serializable(validation_report.__dict__)
        metrics_dict = self._make_json_serializable(validation_report.overall_metrics.__dict__)

        with report_path.open("w") as f:
            json.dump(report_dict, f, indent=2, default=str)

        with metrics_path.open("w") as f:
            json.dump(metrics_dict, f, indent=2, default=str)

        runtime = time.time() - start
        
        # Record artifacts
        self._artifacts = {
            "validation_dir": str(self.validation_dir),
            "report_file": str(report_path),
            "metrics_file": str(metrics_path),
            "production_ready": production_ready
        }
        
        # Storage heartbeat
        storage = create_storage_adapter()
        try:
            storage_data = self._make_json_serializable({
                "compartment": self.name,
                "artifacts": self._artifacts,
                "runtime_seconds": round(runtime, 3),
                "production_ready": production_ready
            })
            storage.put(f"artifact_{self.name}", storage_data)
        finally:
            storage.close()

        return self._make_json_serializable({
            "compartment": self.name,
            "artifacts": self._artifacts,
            "runtime_seconds": round(runtime, 3),
            "production_ready": production_ready,
            "status": "READY" if production_ready else "GATED"
        })

    def _load_model_and_data(self) -> Tuple[Any, np.ndarray, np.ndarray, List[str]]:
        """Load trained model and validation data"""
        try:
            # Load trained model from ml_update compartment
            model_path = Path("models/trained_model.pkl")
            if not model_path.exists():
                self.logger.error("Trained model not found")
                return None, None, None, []
            
            with model_path.open("rb") as f:
                model = pickle.load(f)
            
            # Load enhanced session data for validation
            enhanced_dir = Path("enhanced_sessions")
            if not enhanced_dir.exists():
                self.logger.error("Enhanced sessions not found")
                return None, None, None, []
            
            # Extract features and labels (reuse logic from ml_update)
            X, y, feature_names = self._extract_validation_features()
            
            self.logger.info(f"Loaded model and {len(X)} validation samples")
            return model, X, y, feature_names
            
        except Exception as e:
            self.logger.error(f"Failed to load model and data: {e}")
            return None, None, None, []

    def _extract_validation_features(self) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """Extract features for validation (mirrors ml_update logic)"""
        enhanced_dir = Path("enhanced_sessions")
        enhanced_files = list(enhanced_dir.glob("enhanced_*.json"))
        
        features = []
        labels = []
        feature_names = [
            "session_duration", "price_range", "price_volatility", 
            "cascade_event_count", "price_movement_count",
            "session_type_encoded", "quality_score"
        ]
        
        session_type_map = {
            "ny_am": 1, "nyam": 1, "ny_pm": 2, "nypm": 2,
            "london": 3, "asia": 4, "lunch": 5, 
            "midnight": 6, "premarket": 7, "preasia": 7
        }
        
        for enhanced_file in enhanced_files:
            try:
                with enhanced_file.open("r") as f:
                    enhanced_data = json.load(f)
                
                level1_data = enhanced_data.get("level1_json", {})
                enhanced_meta = enhanced_data.get("enhanced_data", {})
                
                # Extract same features as ml_update
                session_metadata = level1_data.get("session_metadata", {})
                session_duration = session_metadata.get("session_duration", 120)
                session_type = session_metadata.get("session_type", "unknown").lower()
                session_type_encoded = session_type_map.get(session_type, 0)
                
                price_movements = level1_data.get("price_movements", [])
                prices = [pm.get("price_level", 0) for pm in price_movements if pm.get("price_level")]
                price_range = max(prices) - min(prices) if prices else 0
                price_volatility = np.std(prices) if len(prices) > 1 else 0
                price_movement_count = len(price_movements)
                
                cascade_events = level1_data.get("micro_timing_analysis", {}).get("cascade_events", [])
                cascade_event_count = len(cascade_events)
                
                quality_score = enhanced_meta.get("quality_score", 0.0)
                
                # Determine label (same logic as ml_update)
                label = self._determine_session_label(enhanced_file, level1_data)
                if label is None:
                    continue
                
                feature_vector = [
                    session_duration, price_range, price_volatility,
                    cascade_event_count, price_movement_count,
                    session_type_encoded, quality_score
                ]
                
                features.append(feature_vector)
                labels.append(label)
                
            except Exception as e:
                self.logger.warning(f"Failed to process {enhanced_file}: {e}")
                continue
        
        return np.array(features), np.array(labels), feature_names

    def _determine_session_label(self, enhanced_file: Path, level1_data: Dict[str, Any]) -> Optional[int]:
        """Determine cascade (1) vs non-cascade (0) label (mirrors ml_update logic)"""
        cascade_events = level1_data.get("micro_timing_analysis", {}).get("cascade_events", [])
        if len(cascade_events) > 0:
            for event in cascade_events:
                confidence = event.get("confidence", 0)
                event_type = event.get("event_type", "").lower()
                if confidence >= 3 or "cascade" in event_type:
                    return 1
        
        session_metadata = level1_data.get("session_metadata", {})
        session_type = session_metadata.get("session_type", "").lower()
        
        if session_type in ["lunch", "midnight"]:
            return 0
        
        price_movements = level1_data.get("price_movements", [])
        if len(price_movements) <= 5:
            return 0
        
        return 1 if len(cascade_events) > 0 else 0

    def _perform_comprehensive_validation(self, model, X: np.ndarray, y: np.ndarray,
                                        feature_names: List[str]) -> ValidationReport:
        """Perform comprehensive accuracy validation with statistical rigor"""
        self.logger.info("Performing comprehensive validation analysis")

        # 1. Overall accuracy metrics with confidence intervals
        overall_metrics = self._calculate_overall_metrics(model, X, y)

        # 2. Temporal validation (out-of-time testing)
        temporal_validation = self._perform_temporal_validation(model, X, y)

        # 3. Cross-validation with stability assessment
        cross_validation = self._perform_cross_validation(model, X, y)

        # 4. Baseline comparison against Oracle's 91.1%
        baseline_comparison = self._compare_against_baseline(overall_metrics)

        # 5. Statistical significance tests
        statistical_tests = self._perform_statistical_tests(model, X, y)

        # 6. Production readiness assessment
        production_readiness = self._assess_production_readiness_metrics(
            overall_metrics, temporal_validation, cross_validation, baseline_comparison
        )

        # 7. Generate recommendations
        recommendations = self._generate_recommendations(
            overall_metrics, temporal_validation, cross_validation, baseline_comparison
        )

        return ValidationReport(
            overall_metrics=overall_metrics,
            temporal_validation=temporal_validation,
            cross_validation=cross_validation,
            baseline_comparison=baseline_comparison,
            statistical_tests=statistical_tests,
            production_readiness=production_readiness,
            recommendations=recommendations
        )

    def _calculate_overall_metrics(self, model, X: np.ndarray, y: np.ndarray) -> AccuracyMetrics:
        """Calculate comprehensive accuracy metrics with confidence intervals"""
        # Make predictions
        y_pred = model.predict(X)

        # Basic metrics
        accuracy = accuracy_score(y, y_pred)
        precision, recall, f1, _ = precision_recall_fscore_support(y, y_pred, average='weighted')

        # Confidence interval for accuracy (Wilson score interval)
        n = len(y)
        z_score = stats.norm.ppf(1 - (1 - self.config["confidence_level"]) / 2)

        # Wilson score interval for binomial proportion
        p_hat = accuracy
        denominator = 1 + z_score**2 / n
        center = (p_hat + z_score**2 / (2 * n)) / denominator
        margin = z_score * np.sqrt((p_hat * (1 - p_hat) + z_score**2 / (4 * n)) / n) / denominator

        ci_lower = max(0, center - margin)
        ci_upper = min(1, center + margin)

        # Statistical significance test against random chance (50%)
        # Binomial test: H0: accuracy = 0.5, H1: accuracy > 0.5
        successes = int(accuracy * n)
        p_value = 1 - stats.binom.cdf(successes - 1, n, 0.5)
        statistical_significance = p_value < self.config["significance_threshold"]

        # Baseline comparison
        baseline_comparison = accuracy - self.config["baseline_accuracy"]

        return AccuracyMetrics(
            accuracy=accuracy,
            precision=precision,
            recall=recall,
            f1_score=f1,
            confidence_interval_lower=ci_lower,
            confidence_interval_upper=ci_upper,
            p_value=p_value,
            statistical_significance=statistical_significance,
            sample_size=n,
            baseline_comparison=baseline_comparison
        )

    def _perform_temporal_validation(self, model, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """Perform temporal validation with out-of-time testing"""
        # Split data temporally (80/20 split)
        split_point = int(len(X) * self.config["temporal_split_ratio"])

        X_train, X_test = X[:split_point], X[split_point:]
        y_train, y_test = y[:split_point], y[split_point:]

        # Train on early data, test on later data
        model.fit(X_train, y_train)
        y_pred = model.predict(X_test)

        # Calculate temporal metrics
        temporal_accuracy = accuracy_score(y_test, y_pred)
        temporal_precision, temporal_recall, temporal_f1, _ = precision_recall_fscore_support(
            y_test, y_pred, average='weighted'
        )

        # Temporal stability (compare train vs test performance)
        train_accuracy = model.score(X_train, y_train)
        stability_score = 1 - abs(train_accuracy - temporal_accuracy)

        return {
            "temporal_accuracy": temporal_accuracy,
            "temporal_precision": temporal_precision,
            "temporal_recall": temporal_recall,
            "temporal_f1": temporal_f1,
            "train_accuracy": train_accuracy,
            "stability_score": stability_score,
            "train_samples": len(X_train),
            "test_samples": len(X_test),
            "temporal_degradation": train_accuracy - temporal_accuracy
        }

    def _perform_cross_validation(self, model, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """Perform cross-validation with stability assessment"""
        # Time series cross-validation (respects temporal order)
        tscv = TimeSeriesSplit(n_splits=self.config["cv_folds"])
        cv_scores = cross_val_score(model, X, y, cv=tscv, scoring='accuracy')

        # Standard k-fold for comparison
        standard_cv_scores = cross_val_score(model, X, y, cv=self.config["cv_folds"], scoring='accuracy')

        return {
            "cv_mean": np.mean(cv_scores),
            "cv_std": np.std(cv_scores),
            "cv_scores": cv_scores.tolist(),
            "cv_stability": 1 - np.std(cv_scores),  # Higher is more stable
            "standard_cv_mean": np.mean(standard_cv_scores),
            "standard_cv_std": np.std(standard_cv_scores),
            "temporal_vs_standard_diff": np.mean(cv_scores) - np.mean(standard_cv_scores)
        }

    def _compare_against_baseline(self, overall_metrics: AccuracyMetrics) -> Dict[str, Any]:
        """Compare performance against Oracle's 91.1% baseline"""
        baseline = self.config["baseline_accuracy"]
        current_accuracy = overall_metrics.accuracy

        # Statistical test: is current accuracy significantly different from baseline?
        n = overall_metrics.sample_size
        z_score = (current_accuracy - baseline) / np.sqrt(baseline * (1 - baseline) / n)
        p_value_baseline = 2 * (1 - stats.norm.cdf(abs(z_score)))  # Two-tailed test

        # Effect size (Cohen's h for proportions)
        effect_size = 2 * (np.arcsin(np.sqrt(current_accuracy)) - np.arcsin(np.sqrt(baseline)))

        return {
            "baseline_accuracy": baseline,
            "current_accuracy": current_accuracy,
            "improvement": current_accuracy - baseline,
            "improvement_percentage": ((current_accuracy - baseline) / baseline) * 100,
            "z_score": z_score,
            "p_value": p_value_baseline,
            "statistically_significant": p_value_baseline < self.config["significance_threshold"],
            "effect_size": effect_size,
            "effect_size_interpretation": self._interpret_effect_size(effect_size),
            "meets_baseline": current_accuracy >= baseline,
            "confidence_interval_includes_baseline": (
                overall_metrics.confidence_interval_lower <= baseline <= overall_metrics.confidence_interval_upper
            )
        }

    def _perform_statistical_tests(self, model, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """Perform comprehensive statistical significance tests"""
        y_pred = model.predict(X)

        # McNemar's test for comparing with baseline (if we had baseline predictions)
        # For now, we'll use binomial tests

        # Test 1: Accuracy significantly better than random (50%)
        n = len(y)
        successes = np.sum(y == y_pred)
        random_p_value = 1 - stats.binom.cdf(successes - 1, n, 0.5)

        # Test 2: Precision and recall balance
        precision, recall, _, _ = precision_recall_fscore_support(y, y_pred, average='weighted')
        balance_score = 1 - abs(precision - recall)  # Higher is more balanced

        # Test 3: Class distribution test
        class_0_accuracy = accuracy_score(y[y == 0], y_pred[y == 0]) if np.sum(y == 0) > 0 else 0
        class_1_accuracy = accuracy_score(y[y == 1], y_pred[y == 1]) if np.sum(y == 1) > 0 else 0
        class_balance = 1 - abs(class_0_accuracy - class_1_accuracy)

        return {
            "random_chance_p_value": random_p_value,
            "significantly_better_than_random": random_p_value < self.config["significance_threshold"],
            "precision_recall_balance": balance_score,
            "class_0_accuracy": class_0_accuracy,
            "class_1_accuracy": class_1_accuracy,
            "class_balance_score": class_balance,
            "sample_size_adequate": n >= self.config["min_sample_size"]
        }

    def _assess_production_readiness_metrics(self, overall_metrics: AccuracyMetrics,
                                           temporal_validation: Dict[str, Any],
                                           cross_validation: Dict[str, Any],
                                           baseline_comparison: Dict[str, Any]) -> Dict[str, Any]:
        """Assess production readiness based on comprehensive metrics"""

        # Gate 1: Accuracy meets baseline
        accuracy_gate = overall_metrics.accuracy >= self.config["baseline_accuracy"]

        # Gate 2: Statistical significance
        significance_gate = overall_metrics.statistical_significance

        # Gate 3: Temporal stability
        temporal_stability_gate = temporal_validation["stability_score"] >= 0.95

        # Gate 4: Cross-validation stability
        cv_stability_gate = cross_validation["cv_std"] <= 0.05

        # Gate 5: Sample size adequacy
        sample_size_gate = overall_metrics.sample_size >= self.config["min_sample_size"]

        # Gate 6: Confidence interval doesn't include poor performance
        confidence_gate = overall_metrics.confidence_interval_lower >= 0.85

        gates_passed = sum([
            accuracy_gate, significance_gate, temporal_stability_gate,
            cv_stability_gate, sample_size_gate, confidence_gate
        ])

        production_ready = gates_passed >= 5  # At least 5/6 gates must pass

        return {
            "gates": {
                "accuracy_meets_baseline": accuracy_gate,
                "statistically_significant": significance_gate,
                "temporally_stable": temporal_stability_gate,
                "cross_validation_stable": cv_stability_gate,
                "adequate_sample_size": sample_size_gate,
                "confident_performance": confidence_gate
            },
            "gates_passed": gates_passed,
            "total_gates": 6,
            "production_ready": production_ready,
            "readiness_score": gates_passed / 6,
            "critical_issues": self._identify_critical_issues(
                accuracy_gate, significance_gate, temporal_stability_gate,
                cv_stability_gate, sample_size_gate, confidence_gate
            )
        }

    def _generate_recommendations(self, overall_metrics: AccuracyMetrics,
                                temporal_validation: Dict[str, Any],
                                cross_validation: Dict[str, Any],
                                baseline_comparison: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations based on validation results"""
        recommendations = []

        # Accuracy recommendations
        if overall_metrics.accuracy < self.config["baseline_accuracy"]:
            recommendations.append(
                f"CRITICAL: Accuracy {overall_metrics.accuracy:.3f} below baseline "
                f"{self.config['baseline_accuracy']:.3f}. Consider feature engineering or model tuning."
            )

        # Statistical significance recommendations
        if not overall_metrics.statistical_significance:
            recommendations.append(
                f"WARNING: Results not statistically significant (p={overall_metrics.p_value:.4f}). "
                "Consider collecting more data or improving model."
            )

        # Temporal stability recommendations
        if temporal_validation["stability_score"] < 0.95:
            recommendations.append(
                f"WARNING: Temporal instability detected (stability={temporal_validation['stability_score']:.3f}). "
                "Model may not generalize well to future data."
            )

        # Cross-validation recommendations
        if cross_validation["cv_std"] > 0.05:
            recommendations.append(
                f"WARNING: High cross-validation variance (std={cross_validation['cv_std']:.4f}). "
                "Consider regularization or more stable features."
            )

        # Sample size recommendations
        if overall_metrics.sample_size < self.config["min_sample_size"]:
            recommendations.append(
                f"CRITICAL: Sample size {overall_metrics.sample_size} below minimum "
                f"{self.config['min_sample_size']}. Collect more training data."
            )

        # Confidence interval recommendations
        if overall_metrics.confidence_interval_lower < 0.85:
            recommendations.append(
                f"WARNING: Lower confidence bound {overall_metrics.confidence_interval_lower:.3f} "
                "indicates potential for poor performance. Improve model reliability."
            )

        # Positive recommendations
        if overall_metrics.accuracy >= self.config["baseline_accuracy"]:
            recommendations.append(
                f"EXCELLENT: Accuracy {overall_metrics.accuracy:.3f} meets/exceeds baseline. "
                "Model shows strong performance."
            )

        if baseline_comparison["statistically_significant"] and baseline_comparison["improvement"] > 0:
            recommendations.append(
                f"EXCELLENT: Statistically significant improvement over baseline "
                f"({baseline_comparison['improvement_percentage']:.1f}% better)."
            )

        return recommendations

    def _interpret_effect_size(self, effect_size: float) -> str:
        """Interpret Cohen's h effect size"""
        abs_effect = abs(effect_size)
        if abs_effect < 0.2:
            return "negligible"
        elif abs_effect < 0.5:
            return "small"
        elif abs_effect < 0.8:
            return "medium"
        else:
            return "large"

    def _identify_critical_issues(self, accuracy_gate: bool, significance_gate: bool,
                                temporal_stability_gate: bool, cv_stability_gate: bool,
                                sample_size_gate: bool, confidence_gate: bool) -> List[str]:
        """Identify critical issues preventing production deployment"""
        issues = []

        if not accuracy_gate:
            issues.append("accuracy_below_baseline")
        if not significance_gate:
            issues.append("not_statistically_significant")
        if not temporal_stability_gate:
            issues.append("temporal_instability")
        if not cv_stability_gate:
            issues.append("cross_validation_instability")
        if not sample_size_gate:
            issues.append("insufficient_sample_size")
        if not confidence_gate:
            issues.append("low_confidence_bounds")

        return issues

    def _assess_production_readiness(self, validation_report: ValidationReport) -> bool:
        """Final production readiness assessment"""
        return validation_report.production_readiness["production_ready"]

    def _create_error_output(self, error_message: str) -> Dict[str, Any]:
        """Create error output when validation fails"""
        return {
            "compartment": self.name,
            "artifacts": {"error": error_message},
            "runtime_seconds": 0,
            "production_ready": False,
            "status": "ERROR"
        }

    def _make_json_serializable(self, obj):
        """Convert numpy types and other non-serializable objects to JSON-compatible types"""
        if isinstance(obj, dict):
            return {key: self._make_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.bool_, np.integer, np.floating)):
            return obj.item()
        elif hasattr(obj, '__dict__'):
            return self._make_json_serializable(obj.__dict__)
        else:
            return obj
