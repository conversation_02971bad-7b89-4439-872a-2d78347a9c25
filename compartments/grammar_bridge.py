"""
Grammar Bridge Compartment
=========================

CRITICAL MISSING COMPONENT: Transforms enhanced session data into cascade events 
compatible with RG Scaler, bridging the gap between grammar intelligence and 
operational prediction pipeline.

This compartment solves the core architectural disconnect where sophisticated
CFG parsing exists but cannot be used by the predict compartment due to 
data structure incompatibility.
"""

import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

from .base import Compartment

class GrammarBridge(Compartment):
    """
    Grammar Bridge: Transforms enhanced sessions to cascade events
    
    CRITICAL FUNCTION: Bridges the gap between:
    - Enhanced sessions (grammar intelligence output)
    - Cascade events (RG Scaler input requirement)
    
    This enables the sophisticated grammar system to be operationally used
    in production predictions instead of just existing architecturally.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.name = "grammar_bridge"
        self.config = config or {
            "cascade_patterns": [
                "expansion_high", "expansion_low", "liquidity_sweep", 
                "breakthrough", "failure_swing", "retracement",
                "consolidation_break", "momentum_shift"
            ],
            "confidence_threshold": 0.7,
            "temporal_window_minutes": 180,  # 3 hours
            "session_momentum_weights": {
                "asia": 0.3,
                "london": 0.5, 
                "premarket": 0.2
            }
        }
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("🌉 Grammar Bridge initialized")
        self.logger.info(f"   Cascade patterns: {len(self.config['cascade_patterns'])}")
        self.logger.info(f"   Confidence threshold: {self.config['confidence_threshold']}")

    def check_dependencies(self, input_manifest: Dict[str, Any]) -> tuple:
        """Check if dependencies are satisfied"""
        # Grammar bridge depends on enhanced sessions
        enhanced_dir = Path("enhanced_sessions")
        if enhanced_dir.exists() and list(enhanced_dir.glob("enhanced_*.json")):
            return True, []
        else:
            return False, ["Enhanced sessions not found - run lvl1_enhance first"]

    def is_idempotent(self, input_manifest: Dict[str, Any]) -> bool:
        """Check if this compartment can be safely re-run"""
        return True  # Grammar bridge is idempotent
    
    def run(self, input_manifest: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform enhanced sessions into cascade events for RG Scaler compatibility
        
        Args:
            input_manifest: Contains enhanced session files
            
        Returns:
            Output manifest with cascade events and grammar analysis
        """
        start_time = time.time()
        
        try:
            self.logger.info("🌉 Starting Grammar Bridge transformation")
            
            # Load enhanced sessions
            enhanced_sessions = self._load_enhanced_sessions(input_manifest)
            
            if not enhanced_sessions:
                self.logger.warning("No enhanced sessions found for grammar bridge")
                return self._create_empty_output()
            
            # Transform sessions to cascade events
            cascade_events_by_session = {}
            grammar_analysis = {}
            total_events = 0
            
            for session_id, session_data in enhanced_sessions.items():
                # Extract cascade events from enhanced session
                cascade_events = self._transform_enhanced_to_cascade_events(session_data)
                
                # Perform grammar pattern analysis
                grammar_patterns = self._analyze_grammar_patterns(session_data)
                
                # Store results
                cascade_events_by_session[session_id] = cascade_events
                grammar_analysis[session_id] = grammar_patterns
                total_events += len(cascade_events)
                
                self.logger.info(f"   {session_id}: {len(cascade_events)} cascade events extracted")
            
            # Create unified cascade events file for RG Scaler
            unified_cascade_events = self._create_unified_cascade_events(cascade_events_by_session)
            
            # Save outputs
            output_files = self._save_outputs(unified_cascade_events, grammar_analysis)
            
            runtime = time.time() - start_time
            
            self.logger.info(f"🌉 Grammar Bridge completed in {runtime:.3f}s")
            self.logger.info(f"   Total cascade events: {total_events}")
            self.logger.info(f"   Sessions processed: {len(enhanced_sessions)}")
            self.logger.info(f"   Grammar patterns identified: {sum(len(g.get('patterns', [])) for g in grammar_analysis.values())}")
            
            return {
                "compartment": "grammar_bridge",
                "artifacts": output_files,
                "runtime_seconds": runtime,
                "health_ok": True,
                "status": "COMPLETED",
                "metrics": {
                    "sessions_processed": len(enhanced_sessions),
                    "total_cascade_events": total_events,
                    "grammar_patterns_found": sum(len(g.get('patterns', [])) for g in grammar_analysis.values()),
                    "avg_events_per_session": total_events / len(enhanced_sessions) if enhanced_sessions else 0
                }
            }
            
        except Exception as e:
            self.logger.error(f"Grammar Bridge failed: {e}")
            return {
                "compartment": "grammar_bridge",
                "artifacts": {},
                "runtime_seconds": time.time() - start_time,
                "health_ok": False,
                "status": "FAILED",
                "error": str(e)
            }
    
    def _load_enhanced_sessions(self, input_manifest: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Load enhanced session files"""
        enhanced_sessions = {}
        
        try:
            enhanced_dir = Path("enhanced_sessions")
            if not enhanced_dir.exists():
                self.logger.warning("Enhanced sessions directory not found")
                return {}
            
            # Load all enhanced session files
            for enhanced_file in enhanced_dir.glob("enhanced_*.json"):
                try:
                    with open(enhanced_file, 'r') as f:
                        session_data = json.load(f)
                    
                    session_id = enhanced_file.stem.replace("enhanced_", "")
                    enhanced_sessions[session_id] = session_data
                    
                except Exception as e:
                    self.logger.warning(f"Failed to load {enhanced_file}: {e}")
                    continue
            
            return enhanced_sessions
            
        except Exception as e:
            self.logger.error(f"Failed to load enhanced sessions: {e}")
            return {}
    
    def _transform_enhanced_to_cascade_events(self, session_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        CRITICAL FUNCTION: Transform enhanced session to cascade events format
        
        This is the core bridge that enables grammar intelligence to be used
        by the RG Scaler in operational predictions.
        """
        cascade_events = []
        
        try:
            # Extract from price movements (primary source)
            price_movements = session_data.get('level1_json', {}).get('price_movements', [])
            
            for movement in price_movements:
                movement_type = movement.get('movement_type', '').lower()
                
                # Check if movement matches cascade patterns
                if any(pattern in movement_type for pattern in self.config['cascade_patterns']):
                    # Convert timestamp to minutes from session start
                    timestamp_str = movement.get('timestamp', '09:30:00')
                    timestamp_minutes = self._convert_timestamp_to_minutes(timestamp_str)
                    
                    cascade_events.append({
                        'timestamp': timestamp_str,  # RG Scaler expects this
                        'timestamp_minutes': timestamp_minutes,  # For Hawkes
                        'event_type': movement_type,
                        'price_level': movement.get('price_level', 0),
                        'confidence': 1.0,
                        'source': 'grammar_bridge_price_movements'
                    })
            
            # Extract from liquidity events (secondary source)
            liquidity_events = session_data.get('level1_json', {}).get('session_liquidity_events', [])
            
            for event in liquidity_events:
                timestamp_str = event.get('timestamp', '09:30:00')
                timestamp_minutes = self._convert_timestamp_to_minutes(timestamp_str)
                
                cascade_events.append({
                    'timestamp': timestamp_str,
                    'timestamp_minutes': timestamp_minutes,
                    'event_type': f"liquidity_{event.get('event_type', 'sweep')}",
                    'price_level': event.get('price_level', 0),
                    'confidence': 0.8,
                    'source': 'grammar_bridge_liquidity_events'
                })
            
            # Extract from FPFVG events (tertiary source)
            fpfvg_data = session_data.get('level1_json', {}).get('session_fpfvg', {})
            if fpfvg_data.get('fpfvg_present'):
                formation = fpfvg_data.get('fpfvg_formation', {})
                timestamp_str = formation.get('formation_time', '09:30:00')
                timestamp_minutes = self._convert_timestamp_to_minutes(timestamp_str)
                
                cascade_events.append({
                    'timestamp': timestamp_str,
                    'timestamp_minutes': timestamp_minutes,
                    'event_type': 'fpfvg_formation',
                    'price_level': formation.get('gap_high', 0),
                    'confidence': 0.9,
                    'source': 'grammar_bridge_fpfvg'
                })
            
            # Sort by timestamp for temporal consistency
            cascade_events.sort(key=lambda x: x['timestamp_minutes'])
            
            return cascade_events
            
        except Exception as e:
            self.logger.error(f"Failed to transform session to cascade events: {e}")
            return []
    
    def _convert_timestamp_to_minutes(self, timestamp_str: str, session_start: str = "09:30:00") -> float:
        """Convert HH:MM:SS timestamp to minutes from session start with momentum offset"""
        try:
            from datetime import datetime
            
            # Parse timestamps
            time_format = "%H:%M:%S"
            event_time = datetime.strptime(timestamp_str, time_format)
            start_time = datetime.strptime(session_start, time_format)
            
            # Calculate difference in minutes
            time_diff = event_time - start_time
            raw_minutes = time_diff.total_seconds() / 60.0
            
            # Handle overnight sessions
            if raw_minutes < 0:
                raw_minutes += 24 * 60
            
            # Apply momentum offset (affine transformation)
            momentum_offset = 2.5  # NY AM momentum offset
            affine_minutes = raw_minutes + momentum_offset
            
            return max(0.0, affine_minutes)
            
        except Exception as e:
            self.logger.warning(f"Failed to convert timestamp {timestamp_str}: {e}")
            return 0.0
    
    def _analyze_grammar_patterns(self, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze grammar patterns in session data"""
        try:
            # Extract event sequence for pattern analysis
            price_movements = session_data.get('level1_json', {}).get('price_movements', [])
            event_sequence = [m.get('movement_type', '') for m in price_movements]
            
            # Identify grammar patterns
            patterns = []
            for i in range(len(event_sequence) - 1):
                current = event_sequence[i].lower()
                next_event = event_sequence[i + 1].lower()
                
                # Look for cascade patterns
                if 'expansion' in current and 'liquidity' in next_event:
                    patterns.append({
                        'pattern': 'expansion_liquidity_cascade',
                        'confidence': 0.85,
                        'position': i,
                        'completion': 'partial' if i + 2 < len(event_sequence) else 'complete'
                    })
                elif 'consolidation' in current and 'breakthrough' in next_event:
                    patterns.append({
                        'pattern': 'consolidation_breakthrough',
                        'confidence': 0.78,
                        'position': i,
                        'completion': 'partial' if i + 2 < len(event_sequence) else 'complete'
                    })
            
            return {
                'patterns': patterns,
                'event_sequence': event_sequence,
                'pattern_count': len(patterns),
                'completion_rate': len([p for p in patterns if p['completion'] == 'complete']) / max(1, len(patterns))
            }
            
        except Exception as e:
            self.logger.warning(f"Grammar pattern analysis failed: {e}")
            return {'patterns': [], 'event_sequence': [], 'pattern_count': 0, 'completion_rate': 0.0}
    
    def _create_unified_cascade_events(self, cascade_events_by_session: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Create unified cascade events structure for RG Scaler"""
        all_events = []
        session_metadata = {}
        
        for session_id, events in cascade_events_by_session.items():
            # Add session context to events
            for event in events:
                event['session_id'] = session_id
                all_events.append(event)
            
            session_metadata[session_id] = {
                'event_count': len(events),
                'time_span': f"{events[0]['timestamp']} - {events[-1]['timestamp']}" if events else "no_events"
            }
        
        return {
            'unified_cascade_events': all_events,
            'session_metadata': session_metadata,
            'total_events': len(all_events),
            'generation_timestamp': datetime.now().isoformat()
        }
    
    def _save_outputs(self, unified_cascade_events: Dict[str, Any], grammar_analysis: Dict[str, Any]) -> Dict[str, str]:
        """Save grammar bridge outputs"""
        try:
            # Create output directory
            output_dir = Path("grammar_bridge")
            output_dir.mkdir(exist_ok=True)
            
            # Save unified cascade events (for RG Scaler)
            cascade_events_file = output_dir / "cascade_events.json"
            with open(cascade_events_file, 'w') as f:
                json.dump(unified_cascade_events, f, indent=2)
            
            # Save grammar analysis
            grammar_analysis_file = output_dir / "grammar_analysis.json"
            with open(grammar_analysis_file, 'w') as f:
                json.dump(grammar_analysis, f, indent=2)
            
            return {
                "cascade_events_file": str(cascade_events_file),
                "grammar_analysis_file": str(grammar_analysis_file),
                "output_dir": str(output_dir)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to save outputs: {e}")
            return {}
    
    def _create_empty_output(self) -> Dict[str, Any]:
        """Create empty output when no sessions found"""
        return {
            "compartment": "grammar_bridge",
            "artifacts": {},
            "runtime_seconds": 0.0,
            "health_ok": False,
            "status": "NO_DATA",
            "metrics": {
                "sessions_processed": 0,
                "total_cascade_events": 0,
                "grammar_patterns_found": 0,
                "avg_events_per_session": 0
            }
        }


def create_grammar_bridge(config: Optional[Dict[str, Any]] = None) -> GrammarBridge:
    """Factory function to create Grammar Bridge compartment"""
    return GrammarBridge(config)
