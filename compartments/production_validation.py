#!/usr/bin/env python3
"""
Production Validation Pipeline - End-to-End Validation Framework
===============================================================

Comprehensive production validation pipeline that integrates all validation
components with automated testing, regression detection, and deployment gates.

Key Features:
- End-to-end validation pipeline orchestration
- Automated regression detection and alerting
- Production readiness assessment with gate enforcement
- Continuous integration and deployment validation
- Performance monitoring and SLA validation
- Rollback triggers and safety mechanisms

Integration:
- Orchestrates accuracy_validation, ab_testing, and all compartments
- Provides production deployment gates and safety checks
- Integrates with existing validation infrastructure
- Supports CI/CD pipeline integration
"""

import json
import time
import logging
import subprocess
import numpy as np
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta

from compartments.base import Compartment, _hash_json
from storage.adapter import create_storage_adapter

@dataclass
class ValidationStage:
    """Individual validation stage configuration"""
    name: str
    compartment: str
    required: bool
    timeout_seconds: int
    retry_count: int
    dependencies: List[str]

@dataclass
class ProductionGate:
    """Production deployment gate"""
    name: str
    metric: str
    threshold: float
    operator: str  # ">=", "<=", "==", "!="
    critical: bool
    description: str

@dataclass
class ValidationResult:
    """Result from a validation stage"""
    stage_name: str
    success: bool
    duration_seconds: float
    metrics: Dict[str, Any]
    artifacts: Dict[str, str]
    error_message: Optional[str]

@dataclass
class ProductionValidationReport:
    """Complete production validation report"""
    validation_id: str
    timestamp: datetime
    pipeline_version: str
    stages_results: List[ValidationResult]
    gates_status: Dict[str, bool]
    overall_status: str  # "PASS", "FAIL", "WARNING"
    deployment_recommendation: str
    performance_metrics: Dict[str, float]
    regression_analysis: Dict[str, Any]
    recommendations: List[str]

class ProductionValidationCompartment(Compartment):
    name = "production_validation"

    def __init__(self, validation_dir: str = "production_validation", config: Dict[str, Any] = None):
        self.validation_dir = Path(validation_dir)
        self.config = config or {
            "pipeline_timeout_minutes": 30,
            "enable_regression_detection": True,
            "enable_performance_monitoring": True,
            "enable_rollback_triggers": True,
            "baseline_accuracy": 0.911,
            "max_latency_ms": 5000,
            "min_success_rate": 0.95
        }
        self._artifacts: Dict[str, str] = {}
        self.logger = logging.getLogger(__name__)
        
        # Define validation pipeline stages
        self.validation_stages = [
            ValidationStage("data_integrity", "lvl1_enhance", True, 300, 2, []),
            ValidationStage("ml_training", "ml_update", True, 600, 1, ["data_integrity"]),
            ValidationStage("accuracy_validation", "accuracy_validation", True, 300, 1, ["ml_training"]),
            ValidationStage("ab_testing", "ab_testing", False, 300, 1, ["accuracy_validation"]),
            ValidationStage("calibration", "calibration", True, 300, 2, ["ml_training"]),
            ValidationStage("prediction_service", "predict", True, 300, 2, ["calibration"])
        ]
        
        # Define production gates
        self.production_gates = [
            ProductionGate("accuracy_baseline", "accuracy", 0.911, ">=", True, "Model accuracy meets Oracle baseline"),
            ProductionGate("statistical_significance", "p_value", 0.05, "<=", True, "Results are statistically significant"),
            ProductionGate("temporal_stability", "temporal_stability", 0.90, ">=", True, "Model stable over time"),
            ProductionGate("latency_sla", "avg_latency_ms", 5000, "<=", True, "Prediction latency within SLA"),
            ProductionGate("success_rate", "success_rate", 0.95, ">=", True, "Prediction success rate adequate"),
            ProductionGate("sample_size", "sample_size", 30, ">=", False, "Adequate sample size for validation")
        ]

    def run(self, input_manifest: Dict[str, Any]) -> Dict[str, Any]:
        """Run complete production validation pipeline"""
        self.logger.info("Starting production validation pipeline")
        
        start = time.time()
        self.validation_dir.mkdir(parents=True, exist_ok=True)
        
        validation_id = f"prod_val_{int(time.time())}"
        
        # Run validation pipeline
        validation_report = self._run_validation_pipeline(validation_id, input_manifest)
        
        # Assess deployment readiness
        deployment_ready = self._assess_deployment_readiness(validation_report)
        
        # Generate regression analysis
        regression_analysis = self._perform_regression_analysis(validation_report)
        validation_report.regression_analysis = regression_analysis
        
        # Save comprehensive report
        report_path = self.validation_dir / f"validation_report_{validation_id}.json"
        summary_path = self.validation_dir / "latest_validation_summary.json"
        
        # Convert to JSON-serializable format
        report_dict = self._make_json_serializable(validation_report.__dict__)
        
        with report_path.open("w") as f:
            json.dump(report_dict, f, indent=2, default=str)
        
        # Create summary for CI/CD integration
        summary = {
            "validation_id": validation_id,
            "timestamp": validation_report.timestamp.isoformat(),
            "overall_status": validation_report.overall_status,
            "deployment_recommendation": validation_report.deployment_recommendation,
            "deployment_ready": deployment_ready,
            "critical_gates_passed": sum(1 for gate in self.production_gates if gate.critical and validation_report.gates_status.get(gate.name, False)),
            "total_critical_gates": sum(1 for gate in self.production_gates if gate.critical),
            "performance_summary": validation_report.performance_metrics,
            "recommendations": validation_report.recommendations[:3]  # Top 3 recommendations
        }
        
        with summary_path.open("w") as f:
            json.dump(summary, f, indent=2, default=str)

        runtime = time.time() - start
        
        # Record artifacts
        self._artifacts = {
            "validation_dir": str(self.validation_dir),
            "report_file": str(report_path),
            "summary_file": str(summary_path),
            "validation_id": validation_id,
            "deployment_ready": deployment_ready,
            "overall_status": validation_report.overall_status
        }
        
        # Storage heartbeat
        storage = create_storage_adapter()
        try:
            storage_data = self._make_json_serializable({
                "compartment": self.name,
                "artifacts": self._artifacts,
                "runtime_seconds": round(runtime, 3),
                "deployment_ready": deployment_ready,
                "validation_summary": summary
            })
            storage.put(f"artifact_{self.name}", storage_data)
        finally:
            storage.close()

        return self._make_json_serializable({
            "compartment": self.name,
            "artifacts": self._artifacts,
            "runtime_seconds": round(runtime, 3),
            "deployment_ready": deployment_ready,
            "status": validation_report.overall_status
        })

    def _run_validation_pipeline(self, validation_id: str, input_manifest: Dict[str, Any]) -> ProductionValidationReport:
        """Run the complete validation pipeline"""
        self.logger.info(f"Running validation pipeline {validation_id}")
        
        pipeline_start = time.time()
        stages_results = []
        
        # Execute validation stages in dependency order
        for stage in self.validation_stages:
            if not self._check_stage_dependencies(stage, stages_results):
                self.logger.error(f"Dependencies not met for stage {stage.name}")
                stages_results.append(ValidationResult(
                    stage_name=stage.name,
                    success=False,
                    duration_seconds=0,
                    metrics={},
                    artifacts={},
                    error_message="Dependencies not met"
                ))
                continue
            
            # Run validation stage
            stage_result = self._run_validation_stage(stage, input_manifest)
            stages_results.append(stage_result)
            
            # Check if critical stage failed
            if stage.required and not stage_result.success:
                self.logger.error(f"Critical stage {stage.name} failed - aborting pipeline")
                break
        
        # Evaluate production gates
        gates_status = self._evaluate_production_gates(stages_results)
        
        # Determine overall status
        overall_status = self._determine_overall_status(stages_results, gates_status)
        
        # Generate deployment recommendation
        deployment_recommendation = self._generate_deployment_recommendation(overall_status, gates_status)
        
        # Calculate performance metrics
        performance_metrics = self._calculate_performance_metrics(stages_results)
        
        # Generate recommendations
        recommendations = self._generate_pipeline_recommendations(stages_results, gates_status)
        
        pipeline_duration = time.time() - pipeline_start
        
        return ProductionValidationReport(
            validation_id=validation_id,
            timestamp=datetime.now(),
            pipeline_version="1.0",
            stages_results=stages_results,
            gates_status=gates_status,
            overall_status=overall_status,
            deployment_recommendation=deployment_recommendation,
            performance_metrics=performance_metrics,
            regression_analysis={},  # Will be filled later
            recommendations=recommendations
        )

    def _check_stage_dependencies(self, stage: ValidationStage, completed_stages: List[ValidationResult]) -> bool:
        """Check if stage dependencies are satisfied"""
        if not stage.dependencies:
            return True

        completed_names = {result.stage_name for result in completed_stages if result.success}
        return all(dep in completed_names for dep in stage.dependencies)

    def _run_validation_stage(self, stage: ValidationStage, input_manifest: Dict[str, Any]) -> ValidationResult:
        """Run a single validation stage"""
        self.logger.info(f"Running validation stage: {stage.name}")

        stage_start = time.time()

        try:
            # Run compartment using subprocess for isolation
            cmd = [
                "python3", "run_compartments.py",
                "--sequence", stage.compartment,
                "--manifest", "data_manifest_final.json"
            ]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=stage.timeout_seconds,
                cwd=Path.cwd()
            )

            duration = time.time() - stage_start

            if result.returncode == 0:
                # Parse output for metrics and artifacts
                metrics, artifacts = self._parse_stage_output(result.stdout, stage.compartment)

                return ValidationResult(
                    stage_name=stage.name,
                    success=True,
                    duration_seconds=duration,
                    metrics=metrics,
                    artifacts=artifacts,
                    error_message=None
                )
            else:
                return ValidationResult(
                    stage_name=stage.name,
                    success=False,
                    duration_seconds=duration,
                    metrics={},
                    artifacts={},
                    error_message=result.stderr or "Stage execution failed"
                )

        except subprocess.TimeoutExpired:
            return ValidationResult(
                stage_name=stage.name,
                success=False,
                duration_seconds=stage.timeout_seconds,
                metrics={},
                artifacts={},
                error_message=f"Stage timeout after {stage.timeout_seconds} seconds"
            )
        except Exception as e:
            return ValidationResult(
                stage_name=stage.name,
                success=False,
                duration_seconds=time.time() - stage_start,
                metrics={},
                artifacts={},
                error_message=str(e)
            )

    def _parse_stage_output(self, stdout: str, compartment: str) -> Tuple[Dict[str, Any], Dict[str, str]]:
        """Parse stage output to extract metrics and artifacts"""
        metrics = {}
        artifacts = {}

        try:
            # Look for JSON output in stdout
            lines = stdout.strip().split('\n')
            for line in lines:
                if line.strip().startswith('{') and '"compartment"' in line:
                    output_data = json.loads(line)
                    if output_data.get("compartment") == compartment:
                        # Extract metrics from output
                        if "output" in output_data:
                            output_section = output_data["output"]
                            metrics.update({
                                "runtime_seconds": output_section.get("runtime_seconds", 0),
                                "status": output_section.get("status", "UNKNOWN")
                            })

                            # Extract specific metrics based on compartment
                            if compartment == "accuracy_validation":
                                artifacts_section = output_section.get("artifacts", {})
                                if "report_file" in artifacts_section:
                                    # Load accuracy validation report
                                    report_path = Path(artifacts_section["report_file"])
                                    if report_path.exists():
                                        with report_path.open("r") as f:
                                            report_data = json.load(f)

                                        overall_metrics = report_data.get("overall_metrics", {})
                                        metrics.update({
                                            "accuracy": overall_metrics.get("accuracy", 0),
                                            "p_value": overall_metrics.get("p_value", 1),
                                            "sample_size": overall_metrics.get("sample_size", 0)
                                        })

                                        temporal_validation = report_data.get("temporal_validation", {})
                                        metrics["temporal_stability"] = temporal_validation.get("stability_score", 0)

                            elif compartment == "predict":
                                # Load prediction service stats
                                stats_path = Path("predictions/service_stats.json")
                                if stats_path.exists():
                                    with stats_path.open("r") as f:
                                        stats_data = json.load(f)

                                    metrics.update({
                                        "avg_latency_ms": stats_data.get("avg_latency_ms", 0),
                                        "success_rate": stats_data.get("success_rate", 0)
                                    })

                            artifacts.update(output_section.get("artifacts", {}))
                        break
        except Exception as e:
            self.logger.warning(f"Failed to parse stage output: {e}")

        return metrics, artifacts

    def _evaluate_production_gates(self, stages_results: List[ValidationResult]) -> Dict[str, bool]:
        """Evaluate all production gates against stage results"""
        gates_status = {}

        # Aggregate metrics from all stages
        all_metrics = {}
        for result in stages_results:
            all_metrics.update(result.metrics)

        # Load metrics from existing reports if not found in stage results
        if not all_metrics or len(all_metrics) < 6:
            all_metrics.update(self._load_metrics_from_reports())

        # Evaluate each gate
        for gate in self.production_gates:
            if gate.metric in all_metrics:
                value = all_metrics[gate.metric]
                passed = self._evaluate_gate_condition(value, gate.threshold, gate.operator)
                gates_status[gate.name] = passed

                self.logger.info(f"Gate {gate.name}: {value} {gate.operator} {gate.threshold} = {'PASS' if passed else 'FAIL'}")
            else:
                gates_status[gate.name] = False
                self.logger.warning(f"Gate {gate.name}: metric '{gate.metric}' not found - FAIL")

        return gates_status

    def _load_metrics_from_reports(self) -> Dict[str, Any]:
        """Load metrics from existing validation reports"""
        metrics = {}

        try:
            # Load accuracy validation report
            accuracy_report_path = Path("validation/accuracy_validation_report.json")
            if accuracy_report_path.exists():
                with accuracy_report_path.open("r") as f:
                    report_data = json.load(f)

                overall_metrics = report_data.get("overall_metrics", {})
                metrics.update({
                    "accuracy": overall_metrics.get("accuracy", 0),
                    "p_value": overall_metrics.get("p_value", 1),
                    "sample_size": overall_metrics.get("sample_size", 0)
                })

                temporal_validation = report_data.get("temporal_validation", {})
                metrics["temporal_stability"] = temporal_validation.get("stability_score", 0)

                self.logger.info(f"Loaded accuracy metrics: accuracy={metrics.get('accuracy', 0):.3f}, temporal_stability={metrics.get('temporal_stability', 0):.3f}")

            # Load prediction service stats
            stats_path = Path("predictions/service_stats.json")
            if stats_path.exists():
                with stats_path.open("r") as f:
                    stats_data = json.load(f)

                metrics.update({
                    "avg_latency_ms": stats_data.get("avg_latency_ms", 0),
                    "success_rate": stats_data.get("success_rate", 0)
                })

                self.logger.info(f"Loaded prediction metrics: latency={metrics.get('avg_latency_ms', 0):.1f}ms, success_rate={metrics.get('success_rate', 0):.3f}")

        except Exception as e:
            self.logger.warning(f"Failed to load metrics from reports: {e}")

        return metrics

    def _evaluate_gate_condition(self, value: float, threshold: float, operator: str) -> bool:
        """Evaluate a single gate condition"""
        if operator == ">=":
            return value >= threshold
        elif operator == "<=":
            return value <= threshold
        elif operator == "==":
            return abs(value - threshold) < 1e-6
        elif operator == "!=":
            return abs(value - threshold) >= 1e-6
        else:
            self.logger.error(f"Unknown operator: {operator}")
            return False

    def _determine_overall_status(self, stages_results: List[ValidationResult], gates_status: Dict[str, bool]) -> str:
        """Determine overall validation status"""
        # Check if any critical stages failed
        critical_stage_failures = [r for r in stages_results if not r.success and r.stage_name in ["data_integrity", "ml_training", "accuracy_validation"]]
        if critical_stage_failures:
            return "FAIL"

        # Check critical gates
        critical_gates_failed = [name for name, passed in gates_status.items()
                               if not passed and any(gate.critical for gate in self.production_gates if gate.name == name)]
        if critical_gates_failed:
            return "FAIL"

        # Check if any non-critical issues
        non_critical_failures = [r for r in stages_results if not r.success and r.stage_name not in ["data_integrity", "ml_training", "accuracy_validation"]]
        non_critical_gate_failures = [name for name, passed in gates_status.items()
                                    if not passed and not any(gate.critical for gate in self.production_gates if gate.name == name)]

        if non_critical_failures or non_critical_gate_failures:
            return "WARNING"

        return "PASS"

    def _generate_deployment_recommendation(self, overall_status: str, gates_status: Dict[str, bool]) -> str:
        """Generate deployment recommendation"""
        if overall_status == "PASS":
            return "DEPLOY"
        elif overall_status == "WARNING":
            return "DEPLOY_WITH_MONITORING"
        else:
            return "DO_NOT_DEPLOY"

    def _calculate_performance_metrics(self, stages_results: List[ValidationResult]) -> Dict[str, float]:
        """Calculate overall performance metrics"""
        metrics = {}

        # Pipeline performance
        total_duration = sum(r.duration_seconds for r in stages_results)
        successful_stages = sum(1 for r in stages_results if r.success)

        metrics.update({
            "total_pipeline_duration_seconds": total_duration,
            "successful_stages_ratio": successful_stages / len(stages_results) if stages_results else 0,
            "average_stage_duration_seconds": total_duration / len(stages_results) if stages_results else 0
        })

        # Aggregate key metrics from stages
        all_stage_metrics = {}
        for result in stages_results:
            all_stage_metrics.update(result.metrics)

        # Include key performance indicators
        key_metrics = ["accuracy", "temporal_stability", "avg_latency_ms", "success_rate"]
        for metric in key_metrics:
            if metric in all_stage_metrics:
                metrics[metric] = all_stage_metrics[metric]

        return metrics

    def _generate_pipeline_recommendations(self, stages_results: List[ValidationResult], gates_status: Dict[str, bool]) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []

        # Stage-specific recommendations
        failed_stages = [r for r in stages_results if not r.success]
        for failed_stage in failed_stages:
            recommendations.append(f"CRITICAL: {failed_stage.stage_name} failed - {failed_stage.error_message}")

        # Gate-specific recommendations
        failed_gates = [(name, gate) for name, passed in gates_status.items() if not passed
                       for gate in self.production_gates if gate.name == name]

        for gate_name, gate in failed_gates:
            if gate.critical:
                recommendations.append(f"CRITICAL GATE FAILURE: {gate.description}")
            else:
                recommendations.append(f"WARNING: {gate.description}")

        # Performance recommendations
        slow_stages = [r for r in stages_results if r.duration_seconds > 300]  # > 5 minutes
        for slow_stage in slow_stages:
            recommendations.append(f"PERFORMANCE: {slow_stage.stage_name} took {slow_stage.duration_seconds:.1f}s - consider optimization")

        # Success recommendations
        if not failed_stages and not failed_gates:
            recommendations.append("EXCELLENT: All validation stages and gates passed successfully")

        return recommendations

    def _perform_regression_analysis(self, validation_report: ProductionValidationReport) -> Dict[str, Any]:
        """Perform regression analysis against historical baselines"""
        regression_analysis = {
            "baseline_comparison": {},
            "trend_analysis": {},
            "anomaly_detection": {}
        }

        try:
            # Compare against known baselines
            current_accuracy = validation_report.performance_metrics.get("accuracy", 0)
            baseline_accuracy = self.config["baseline_accuracy"]

            regression_analysis["baseline_comparison"] = {
                "current_accuracy": current_accuracy,
                "baseline_accuracy": baseline_accuracy,
                "regression_detected": current_accuracy < baseline_accuracy - 0.02,  # 2% tolerance
                "improvement_detected": current_accuracy > baseline_accuracy + 0.02
            }

            # Simple trend analysis (would be more sophisticated with historical data)
            regression_analysis["trend_analysis"] = {
                "accuracy_trend": "stable",  # Would calculate from historical data
                "latency_trend": "stable",
                "success_rate_trend": "stable"
            }

            # Anomaly detection
            current_latency = validation_report.performance_metrics.get("avg_latency_ms", 0)
            max_expected_latency = self.config["max_latency_ms"]

            regression_analysis["anomaly_detection"] = {
                "latency_anomaly": current_latency > max_expected_latency * 1.5,
                "accuracy_anomaly": abs(current_accuracy - baseline_accuracy) > 0.05,
                "pipeline_duration_anomaly": validation_report.performance_metrics.get("total_pipeline_duration_seconds", 0) > 1800  # 30 minutes
            }

        except Exception as e:
            self.logger.warning(f"Regression analysis failed: {e}")
            regression_analysis["error"] = str(e)

        return regression_analysis

    def _assess_deployment_readiness(self, validation_report: ProductionValidationReport) -> bool:
        """Assess if system is ready for deployment"""
        return (validation_report.overall_status in ["PASS", "WARNING"] and
                validation_report.deployment_recommendation in ["DEPLOY", "DEPLOY_WITH_MONITORING"])

    def _make_json_serializable(self, obj):
        """Convert numpy types and other non-serializable objects to JSON-compatible types"""
        if isinstance(obj, dict):
            return {key: self._make_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.bool_, np.integer, np.floating)):
            return obj.item()
        elif hasattr(obj, '__dict__'):
            return self._make_json_serializable(obj.__dict__)
        else:
            return obj
