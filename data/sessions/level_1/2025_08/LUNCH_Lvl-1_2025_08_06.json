{"session_metadata": {"session_type": "lunch", "session_date": "2025-08-06", "session_start": "12:00:00", "session_end": "12:59:00", "session_duration": 59, "transcription_source": "live_market_data", "data_completeness": "complete_session", "timezone": "ET", "session_status": "completed"}, "session_fpfvg": {"fpfvg_present": true, "fpfvg_formation": {"formation_time": "12:03:00", "premium_high": 23372.25, "discount_low": 23369.0, "gap_size": 3.25, "interactions": [{"interaction_time": "12:10:00", "interaction_type": "redelivery", "price_level": 23369.0, "interaction_context": "first_lunch_fpfvg_redelivery"}, {"interaction_time": "12:15:00", "interaction_type": "rebalance", "price_level": 23372.25, "interaction_context": "first_lunch_fpfvg_rebalance"}, {"interaction_time": "12:22:00", "interaction_type": "rebalance", "price_level": 23372.25, "interaction_context": "second_lunch_fpfvg_rebalance"}, {"interaction_time": "12:26:00", "interaction_type": "rebalance", "price_level": 23372.25, "interaction_context": "third_lunch_fpfvg_rebalance"}, {"interaction_time": "12:30:00", "interaction_type": "redelivery", "price_level": 23369.0, "interaction_context": "second_lunch_fpfvg_redelivery"}, {"interaction_time": "12:41:00", "interaction_type": "redelivery", "price_level": 23369.0, "interaction_context": "third_lunch_fpfvg_redelivery"}, {"interaction_time": "12:49:00", "interaction_type": "rebalance", "price_level": 23372.25, "interaction_context": "fourth_lunch_fpfvg_rebalance"}]}}, "price_movements": [{"timestamp": "12:00:00", "price_level": 23367.75, "movement_type": "open"}, {"timestamp": "12:01:00", "price_level": 23357.0, "movement_type": "expansion_lower_start"}, {"timestamp": "12:05:00", "price_level": 23385.0, "movement_type": "expansion_higher"}, {"timestamp": "12:03:00", "price_level": 23375.5, "movement_type": "am_session_high_takeout"}, {"timestamp": "12:03:00", "price_level": 23372.25, "movement_type": "lunch_fpfvg_formation_premium"}, {"timestamp": "12:03:00", "price_level": 23369.0, "movement_type": "lunch_fpfvg_formation_discount"}, {"timestamp": "12:09:00", "price_level": 23382.0, "movement_type": "consolidation_high"}, {"timestamp": "12:09:00", "price_level": 23373.5, "movement_type": "consolidation_low"}, {"timestamp": "12:13:00", "price_level": 23361.5, "movement_type": "retracement_low"}, {"timestamp": "12:20:00", "price_level": 23385.75, "movement_type": "expansion_high"}, {"timestamp": "12:20:00", "price_level": 23385.75, "movement_type": "reversal_point_expansion_lower"}, {"timestamp": "12:28:00", "price_level": 23359.25, "movement_type": "expansion_low"}, {"timestamp": "12:30:00", "price_level": 23369.75, "movement_type": "retracement_high"}, {"timestamp": "12:31:00", "price_level": 23353.25, "movement_type": "expansion_low"}, {"timestamp": "12:33:00", "price_level": 23365.5, "movement_type": "retracement_high"}, {"timestamp": "12:34:00", "price_level": 23352.25, "movement_type": "expansion_low_reversal_point"}, {"timestamp": "12:37:00", "price_level": 23389.5, "movement_type": "session_high_expansion_end"}, {"timestamp": "12:37:00", "price_level": 23389.5, "movement_type": "reversal_point_expansion_lower"}, {"timestamp": "12:39:00", "price_level": 23363.5, "movement_type": "expansion_low"}, {"timestamp": "12:41:00", "price_level": 23376.5, "movement_type": "retracement_high"}, {"timestamp": "12:45:00", "price_level": 23345.75, "movement_type": "session_low_expansion_end"}, {"timestamp": "12:45:00", "price_level": 23345.75, "movement_type": "reversal_point_expansion_higher"}, {"timestamp": "12:54:00", "price_level": 23385.5, "movement_type": "expansion_high"}, {"timestamp": "12:56:00", "price_level": 23372.25, "movement_type": "retracement_low"}, {"timestamp": "12:58:00", "price_level": 23382.5, "movement_type": "expansion_high"}, {"timestamp": "12:59:00", "price_level": 23371.0, "movement_type": "close"}], "session_liquidity_events": [{"timestamp": "12:00:00", "event_type": "rebalance", "liquidity_type": "cross_session", "target_level": "previous_day_london_fpfvg", "magnitude": "medium", "context": "previous_day_london_fpfvg_rebalance_session_open"}, {"timestamp": "12:03:00", "event_type": "takeout", "liquidity_type": "cross_session", "target_level": "am_session_high", "magnitude": "high", "context": "am_session_high_takeout_liquidity_grab"}, {"timestamp": "12:03:00", "event_type": "fpfvg_formation", "liquidity_type": "native_session", "target_level": "lunch_session_fpfvg", "magnitude": "medium", "context": "lunch_native_fpfvg_formation_3_25_gap"}, {"timestamp": "12:05:00", "event_type": "redelivery", "liquidity_type": "cross_session", "target_level": "previous_day_premarket_fpfvg", "magnitude": "high", "context": "previous_day_premarket_fpfvg_redelivery"}, {"timestamp": "12:13:00", "event_type": "redelivery", "liquidity_type": "cross_session", "target_level": "previous_day_london_fpfvg", "magnitude": "medium", "context": "previous_day_london_fpfvg_redelivery"}, {"timestamp": "12:19:00", "event_type": "redelivery", "liquidity_type": "cross_session", "target_level": "previous_day_premarket_fpfvg", "magnitude": "high", "context": "previous_day_premarket_fpfvg_second_redelivery"}, {"timestamp": "12:20:00", "event_type": "rebalance", "liquidity_type": "cross_session", "target_level": "previous_day_premarket_fpfvg", "magnitude": "high", "context": "previous_day_premarket_fpfvg_rebalance"}, {"timestamp": "12:23:00", "event_type": "redelivery", "liquidity_type": "cross_session", "target_level": "previous_day_london_fpfvg", "magnitude": "medium", "context": "previous_day_london_fpfvg_second_redelivery"}, {"timestamp": "12:26:00", "event_type": "redelivery", "liquidity_type": "cross_session", "target_level": "previous_day_london_fpfvg", "magnitude": "medium", "context": "previous_day_london_fpfvg_third_redelivery"}, {"timestamp": "12:33:00", "event_type": "redelivery", "liquidity_type": "cross_session", "target_level": "previous_day_london_fpfvg", "magnitude": "medium", "context": "previous_day_london_fpfvg_fourth_redelivery"}, {"timestamp": "12:37:00", "event_type": "rebalance", "liquidity_type": "cross_session", "target_level": "previous_day_premarket_fpfvg", "magnitude": "high", "context": "previous_day_premarket_fpfvg_second_rebalance"}, {"timestamp": "12:39:00", "event_type": "rebalance", "liquidity_type": "cross_session", "target_level": "previous_day_london_fpfvg", "magnitude": "medium", "context": "previous_day_london_fpfvg_rebalance"}, {"timestamp": "12:48:00", "event_type": "rebalance", "liquidity_type": "cross_session", "target_level": "previous_day_london_fpfvg", "magnitude": "medium", "context": "previous_day_london_fpfvg_second_rebalance"}, {"timestamp": "12:53:00", "event_type": "rebalance", "liquidity_type": "cross_session", "target_level": "previous_day_premarket_fpfvg", "magnitude": "high", "context": "previous_day_premarket_fpfvg_third_rebalance"}, {"timestamp": "12:58:00", "event_type": "redelivery", "liquidity_type": "cross_session", "target_level": "previous_day_premarket_fpfvg", "magnitude": "high", "context": "previous_day_premarket_fpfvg_third_redelivery"}], "energy_state": {"energy_density": 0.76, "total_accumulated": 44.84, "energy_rate": 45.6, "energy_source": "mixed_native_and_cross_session", "session_duration": 59, "expansion_phases": 8, "retracement_phases": 6, "consolidation_phases": 2, "phase_transitions": 16, "session_status": "complete"}, "contamination_analysis": {"htf_contamination": {"immediate_cross_session_interaction": true, "previous_day_influence": true, "am_session_carryover": true, "extensive_cross_reference_activity": true, "htf_carryover_strength": 0.81, "cross_session_inheritance": 0.76}, "cross_session_inheritance": {"am_session_high_interactions": 1, "previous_day_london_fpfvg_interactions": 6, "previous_day_premarket_fpfvg_interactions": 6, "lunch_native_fpfvg": true, "lunch_fpfvg_gap_size": 3.25, "energy_carryover_coefficient": 0.76}}, "processing_metadata": {"original_file": "/Users/<USER>/grok-claude-automation/project_oracle/LUNCH_Lvl-1_2025_08_06.json", "standardization_date": "2025-08-07T19:29:57.180469", "conversion_type": "partial_schema_completed", "schema_version": "target_v1.0", "data_recovery": {"recovery_date": "2025-08-07T19:45:57.460958", "corrections_applied": ["Added recovered price movements"], "recovery_source": "backup_file_recovery"}}}