#!/usr/bin/env python3
"""
Seed label_overrides from overrides_template.json using conservative heuristics.

- Reads overrides_template.json (groups + unknown items)
- Applies token-based hints to infer 'non_cascade' for a subset of unknowns
- Limits seeding to a fraction per group (default 0.5)
- Writes label_overrides_seeded.json (does not alter existing label_overrides.json)

Usage:
  python seed_label_overrides.py \
    --template overrides_template.json \
    --output label_overrides_seeded.json \
    --non-cascade-tokens partial non_event negative nocascade non_cascade \
    --max-fraction-per-group 0.5
"""
from __future__ import annotations
import argparse
import json
from pathlib import Path
from typing import Dict, Any, List, Set


def load_json(path: Path) -> Dict[str, Any]:
    with path.open("r") as f:
        return json.load(f)


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--template", default="overrides_template.json", help="Path to overrides_template.json")
    ap.add_argument("--output", default="label_overrides_seeded.json", help="Path to write seeded overrides JSON")
    ap.add_argument("--non-cascade-tokens", nargs="*", default=["partial", "non_event", "non-event", "negative", "nocascade", "non_cascade"], help="Filename/path tokens to infer non_cascade")
    ap.add_argument("--max-fraction-per-group", type=float, default=0.5, help="Max fraction of a group's unknowns to seed")
    args = ap.parse_args()

    template_path = Path(args.template)
    if not template_path.exists():
        raise SystemExit(f"Template not found: {template_path}")

    tpl = load_json(template_path)
    groups: Dict[str, List[Dict[str, Any]]] = tpl.get("groups", {})

    seeded: Dict[str, str] = {}
    used_keys: Set[str] = set()

    def reserve_key(candidates: List[str]) -> str:
        for c in candidates:
            if c and c not in used_keys:
                used_keys.add(c)
                return c
        # fallback with numeric suffix
        base = candidates[-1] or candidates[0] or "unknown"
        idx = 1
        key = f"{base}_{idx}"
        while key in used_keys:
            idx += 1
            key = f"{base}_{idx}"
        used_keys.add(key)
        return key

    non_cascade_tokens = [t.lower() for t in args.non_cascade_tokens]

    total_candidates = 0
    total_seeded = 0

    for gkey, items in groups.items():
        n_group = len(items)
        if n_group == 0:
            continue
        max_seed = max(1, int(n_group * args.max_fraction_per_group))
        seeded_in_group = 0
        for it in items:
            path = str(it.get("path") or "")
            name = Path(path).name
            stem = Path(path).stem
            p_lower = path.lower()
            # high-confidence heuristic: token presence in path
            if any(tok in p_lower for tok in non_cascade_tokens):
                key = reserve_key([stem, name, path])
                seeded[key] = "non_cascade"
                seeded_in_group += 1
                total_seeded += 1
            total_candidates += 1
            if seeded_in_group >= max_seed:
                break

    output = {
        "_meta": {
            "source_template": str(template_path),
            "non_cascade_tokens": non_cascade_tokens,
            "max_fraction_per_group": args.max_fraction_per_group,
            "groups": {k: len(v) for k, v in groups.items()},
            "total_candidates": total_candidates,
            "total_seeded": total_seeded
        },
        "overrides": seeded
    }

    with open(args.output, "w") as f:
        json.dump(output, f, indent=2)

    print(f"Seeded {total_seeded} overrides out of {total_candidates} candidates across {len(groups)} groups.")
    print(f"Wrote seeded overrides to {args.output}")


if __name__ == "__main__":
    main()

