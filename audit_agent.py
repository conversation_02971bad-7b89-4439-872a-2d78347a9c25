#!/usr/bin/env python3
"""
Code Audit Guardian - Security-Focused Git & AST Analysis Agent
==============================================================

Specialized security agent that protects critical Python codebases from 
accidental modifications and ensures code integrity through comprehensive
git-based change detection, AST analysis, and guard decorator verification.

Primary Responsibilities:
1. Git Status Analysis - Detect uncommitted changes in critical files
2. Guard Decorator Verification - Ensure @guard.register decorators remain intact  
3. Semantic Drift Detection - Compare current code against last commit using AST
4. Concise Security Reporting - Generate clean, scannable audit reports

Usage:
    python3 audit_agent.py

Mathematical Foundation:
- Drift Score = (number_of_changes / total_functions) * 100
- Acceptability Threshold: <10% = Acceptable, 10-25% = Moderate, >25% = High Drift
"""

import ast
import git
import hashlib
import os
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime

@dataclass
class FileAuditResult:
    """Results from auditing a single file"""
    file_path: str
    git_status: str
    changes_count: int
    guards_found: int
    guards_expected: int
    drift_score: float
    missing_guards: List[str]
    semantic_changes: List[str]

@dataclass  
class AuditReport:
    """Complete audit report for the system"""
    timestamp: datetime
    total_files: int
    files_with_issues: int
    critical_issues: List[str]
    file_results: List[FileAuditResult]
    system_health: str

class GitAuditor:
    """
    Git-based code integrity auditor with AST analysis capabilities
    
    Focuses on detecting:
    - Uncommitted changes in critical files
    - Missing or modified @guard.register decorators  
    - Semantic drift through AST comparison
    - Architectural violations
    """
    
    def __init__(self, repo_path: str = "."):
        """Initialize auditor with repository path"""
        self.repo_path = Path(repo_path).absolute()
        self.repo = None
        self.critical_files = [
            "oracle_core.py",
            "invariants.py", 
            "production_oracle.py",
            "production_simple.py"
        ]
        
        # Initialize git repository connection - try current and parent directories
        try:
            self.repo = git.Repo(self.repo_path)
        except git.exc.InvalidGitRepositoryError:
            # Try parent directory (common case when running from subdirectory)
            try:
                parent_path = self.repo_path.parent
                self.repo = git.Repo(parent_path)
                self.repo_path = parent_path
            except git.exc.InvalidGitRepositoryError:
                print(f"⚠️ Warning: Neither {self.repo_path} nor {parent_path} is a git repository")
        except Exception as e:
            print(f"⚠️ Warning: Git initialization failed: {e}")
    
    def find_critical_files(self) -> Dict[str, Path]:
        """
        Locate critical system files in the repository
        
        Returns:
            Dict mapping filename to full path
        """
        found_files = {}
        
        for critical_file in self.critical_files:
            # Search for file in repository
            for root, dirs, files in os.walk(self.repo_path):
                if critical_file in files:
                    found_files[critical_file] = Path(root) / critical_file
                    break
        
        return found_files
    
    def get_git_status(self, file_path: Path) -> Tuple[str, int]:
        """
        Get git status for a specific file
        
        Args:
            file_path: Path to file to check
            
        Returns:
            Tuple of (status_string, changes_count)
        """
        if not self.repo:
            return "No Git", 0
        
        try:
            # Make path relative to repo root
            rel_path = str(file_path.relative_to(self.repo_path))
            
            # Check if file is in git index
            if rel_path in self.repo.index.entries:
                # Check for modifications
                if self.repo.is_dirty(path=rel_path):
                    # Get diff to count changes
                    try:
                        diff = self.repo.git.diff('HEAD', '--', rel_path)
                        changes = len([line for line in diff.split('\n') 
                                     if line.startswith('+') or line.startswith('-')])
                        if changes > 0:
                            return "Modified", changes
                    except:
                        return "Modified", 1
                
                # Check if staged
                staged_diff = self.repo.index.diff('HEAD')
                for item in staged_diff:
                    if item.a_path == rel_path or item.b_path == rel_path:
                        return "Staged", 1
                
                return "Clean", 0
            else:
                # File not tracked
                return "Untracked", 1
                
        except Exception as e:
            return f"Git Error", 0
    
    def parse_python_ast(self, file_path: Path) -> Optional[ast.AST]:
        """
        Parse Python file into AST
        
        Args:
            file_path: Path to Python file
            
        Returns:
            AST node or None if parsing fails
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            return ast.parse(source_code)
        except Exception as e:
            print(f"⚠️ AST parsing failed for {file_path}: {e}")
            return None
    
    def extract_guard_decorators(self, tree: ast.AST) -> Dict[str, List[str]]:
        """
        Extract @guard.register decorators and their associated functions
        
        Args:
            tree: AST tree to analyze
            
        Returns:
            Dict mapping function names to their guard decorator info
        """
        guard_functions = {}
        
        class GuardVisitor(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                # Check for @guard.register decorator
                for decorator in node.decorator_list:
                    if self._is_guard_decorator(decorator):
                        guard_info = self._extract_guard_info(decorator)
                        guard_functions[node.name] = guard_info
                self.generic_visit(node)
            
            def _is_guard_decorator(self, decorator):
                # Check if decorator is @guard.register
                if isinstance(decorator, ast.Call):
                    if isinstance(decorator.func, ast.Attribute):
                        if (isinstance(decorator.func.value, ast.Name) and 
                            decorator.func.value.id == 'guard' and
                            decorator.func.attr == 'register'):
                            return True
                return False
            
            def _extract_guard_info(self, decorator):
                info = []
                if isinstance(decorator, ast.Call):
                    for keyword in decorator.keywords:
                        if keyword.arg in ['name', 'purpose', 'inputs', 'outputs']:
                            if isinstance(keyword.value, ast.Constant):
                                info.append(f"{keyword.arg}={keyword.value.value}")
                return info
        
        visitor = GuardVisitor()
        visitor.visit(tree)
        return guard_functions
    
    def compare_with_last_commit(self, file_path: Path) -> Tuple[float, List[str]]:
        """
        Compare current file with last commit to detect semantic drift
        
        Args:
            file_path: Path to file to compare
            
        Returns:
            Tuple of (drift_percentage, list_of_changes)
        """
        if not self.repo:
            return 0.0, ["No git repository"]
        
        try:
            # Get relative path
            rel_path = str(file_path.relative_to(self.repo_path))
            
            # Get current AST
            current_ast = self.parse_python_ast(file_path)
            if not current_ast:
                return 100.0, ["Cannot parse current file"]
            
            # Get file content from last commit
            try:
                last_commit_content = self.repo.git.show(f'HEAD:{rel_path}')
                last_commit_ast = ast.parse(last_commit_content)
            except Exception:
                # File might be new or not in last commit
                return 0.0, ["File not in last commit"]
            
            # Compare ASTs
            drift_score, changes = self._compare_asts(current_ast, last_commit_ast)
            return drift_score, changes
            
        except Exception as e:
            return 0.0, [f"Comparison error: {str(e)}"]
    
    def _compare_asts(self, current: ast.AST, previous: ast.AST) -> Tuple[float, List[str]]:
        """
        Compare two AST trees to detect semantic differences
        
        Args:
            current: Current AST
            previous: Previous AST
            
        Returns:
            Tuple of (drift_percentage, list_of_changes)
        """
        changes = []
        
        # Extract function definitions
        current_functions = self._extract_function_signatures(current)
        previous_functions = self._extract_function_signatures(previous)
        
        # Check for added functions
        added_functions = set(current_functions.keys()) - set(previous_functions.keys())
        for func in added_functions:
            changes.append(f"Added function: {func}")
        
        # Check for removed functions  
        removed_functions = set(previous_functions.keys()) - set(current_functions.keys())
        for func in removed_functions:
            changes.append(f"Removed function: {func}")
        
        # Check for modified function signatures
        common_functions = set(current_functions.keys()) & set(previous_functions.keys())
        for func in common_functions:
            if current_functions[func] != previous_functions[func]:
                changes.append(f"Modified function: {func}")
        
        # Calculate drift score
        total_functions = max(len(current_functions), len(previous_functions), 1)
        drift_score = (len(changes) / total_functions) * 100
        
        return drift_score, changes
    
    def _extract_function_signatures(self, tree: ast.AST) -> Dict[str, str]:
        """Extract function signatures from AST"""
        functions = {}
        
        class FunctionVisitor(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                # Create signature string
                args = []
                for arg in node.args.args:
                    args.append(arg.arg)
                
                signature = f"{node.name}({', '.join(args)})"
                functions[node.name] = signature
                self.generic_visit(node)
        
        visitor = FunctionVisitor()
        visitor.visit(tree)
        return functions
    
    def audit_file(self, file_path: Path) -> FileAuditResult:
        """
        Perform comprehensive audit of a single file
        
        Args:
            file_path: Path to file to audit
            
        Returns:
            FileAuditResult with all findings
        """
        # Git status check
        git_status, changes_count = self.get_git_status(file_path)
        
        # Parse AST for guard decorator analysis
        tree = self.parse_python_ast(file_path)
        guards_found = 0
        missing_guards = []
        
        if tree:
            guard_decorators = self.extract_guard_decorators(tree)
            guards_found = len(guard_decorators)
            
            # Check for functions that should have guards
            all_functions = self._extract_function_signatures(tree)
            expected_guard_functions = self._get_expected_guard_functions(file_path.name)
            
            for expected_func in expected_guard_functions:
                if expected_func not in guard_decorators:
                    missing_guards.append(expected_func)
        
        guards_expected = len(self._get_expected_guard_functions(file_path.name))
        
        # Semantic drift analysis
        drift_score, semantic_changes = self.compare_with_last_commit(file_path)
        
        return FileAuditResult(
            file_path=str(file_path),
            git_status=git_status,
            changes_count=changes_count,
            guards_found=guards_found,
            guards_expected=guards_expected,
            drift_score=drift_score,
            missing_guards=missing_guards,
            semantic_changes=semantic_changes
        )
    
    def _get_expected_guard_functions(self, filename: str) -> List[str]:
        """
        Get list of functions expected to have guard decorators
        
        Args:
            filename: Name of the file
            
        Returns:
            List of function names that should be guarded
        """
        expected_guards = {
            "oracle_core.py": [
                "translate_taxonomy",
                "parse_cascade_grammar", 
                "predict_pattern_completion",
                "get_pattern_definition",
                "validate_grammatical_consistency"
            ],
            "production_oracle.py": [
                "translate_taxonomy",
                "parse_cascade_grammar",
                "predict_next_event"
            ],
            "production_simple.py": [],  # No guards expected in simple version
            "invariants.py": []  # Guards are defined here, not applied
        }
        
        return expected_guards.get(filename, [])
    
    def run_audit(self) -> AuditReport:
        """
        Run complete audit of all critical files
        
        Returns:
            Complete audit report
        """
        print("🔍 Starting Code Audit Guardian Analysis...")
        
        # Find critical files
        critical_files = self.find_critical_files()
        if not critical_files:
            print("❌ No critical files found!")
            return AuditReport(
                timestamp=datetime.now(),
                total_files=0,
                files_with_issues=0,
                critical_issues=["No critical files found"],
                file_results=[],
                system_health="CRITICAL"
            )
        
        # Audit each file
        file_results = []
        critical_issues = []
        files_with_issues = 0
        
        for filename, file_path in critical_files.items():
            print(f"   Auditing {filename}...")
            result = self.audit_file(file_path)
            file_results.append(result)
            
            # Check for issues
            has_issues = False
            
            if result.git_status not in ["Clean", "No Git"]:
                has_issues = True
            
            if result.missing_guards:
                has_issues = True
                critical_issues.append(f"Missing guards in {filename}: {', '.join(result.missing_guards)}")
            
            if result.drift_score > 25.0:
                has_issues = True
                critical_issues.append(f"High drift in {filename}: {result.drift_score:.1f}%")
            
            if has_issues:
                files_with_issues += 1
        
        # Determine overall system health
        if files_with_issues == 0:
            system_health = "EXCELLENT"
        elif files_with_issues <= len(critical_files) // 2:
            system_health = "GOOD" 
        else:
            system_health = "CRITICAL"
        
        return AuditReport(
            timestamp=datetime.now(),
            total_files=len(critical_files),
            files_with_issues=files_with_issues,
            critical_issues=critical_issues,
            file_results=file_results,
            system_health=system_health
        )
    
    def format_audit_report(self, report: AuditReport) -> str:
        """
        Format audit report in the specified table format
        
        Args:
            report: AuditReport to format
            
        Returns:
            Formatted report string
        """
        output = []
        output.append("🔍 CODE INTEGRITY AUDIT REPORT")
        output.append("=" * 40)
        output.append("")
        
        # Git Status Check
        output.append("📊 GIT STATUS CHECK")
        output.append("File                 | Status        | Changes")
        output.append("---------------------|---------------|--------")
        
        for result in report.file_results:
            filename = Path(result.file_path).name
            status_icon = self._get_status_icon(result.git_status, result.changes_count)
            status_text = f"{status_icon} {result.git_status}"
            
            output.append(f"{filename:<20} | {status_text:<13} | {result.changes_count}")
        
        output.append("")
        
        # Guard Decorator Check
        output.append("🛡️ GUARD DECORATOR CHECK")
        output.append("File                 | Guards Found  | Status")
        output.append("---------------------|---------------|--------")
        
        for result in report.file_results:
            filename = Path(result.file_path).name
            guard_status = "✅ Pass" if not result.missing_guards else f"❌ Missing @guard on {result.missing_guards[0]}()"
            guard_text = f"{result.guards_found}/{result.guards_expected}"
            
            output.append(f"{filename:<20} | {guard_text:<13} | {guard_status}")
        
        output.append("")
        
        # Semantic Drift Check
        output.append("📈 SEMANTIC DRIFT CHECK")
        output.append("File                 | Drift Score   | Status") 
        output.append("---------------------|---------------|--------")
        
        for result in report.file_results:
            filename = Path(result.file_path).name
            drift_status = self._get_drift_status(result.drift_score)
            drift_text = f"{result.drift_score:.1f}%"
            
            output.append(f"{filename:<20} | {drift_text:<13} | {drift_status}")
        
        output.append("")
        
        # Summary
        if report.critical_issues:
            issue_summary = f"{len(report.critical_issues)} issues detected"
            key_issue = report.critical_issues[0].split(":")[0] if report.critical_issues else ""
            output.append(f"🎯 SUMMARY: {issue_summary} - Review {key_issue}")
        else:
            output.append("🎯 SUMMARY: All checks passed - System integrity maintained")
        
        return "\n".join(output)
    
    def _get_status_icon(self, status: str, changes: int) -> str:
        """Get appropriate status icon for git status"""
        if status == "Clean" or status == "No Git":
            return "✅"
        elif status == "Staged" or changes <= 3:
            return "⚠️"
        else:
            return "❌"
    
    def _get_drift_status(self, drift_score: float) -> str:
        """Get drift status based on score"""
        if drift_score < 10.0:
            return "✅ Acceptable"
        elif drift_score < 25.0:
            return "⚠️ Moderate" 
        else:
            return "❌ High Drift"

def main():
    """Main entry point for audit agent"""
    
    # Initialize auditor
    auditor = GitAuditor()
    
    # Run comprehensive audit
    report = auditor.run_audit()
    
    # Display formatted report
    formatted_report = auditor.format_audit_report(report)
    print(formatted_report)
    
    # Save report to file
    report_file = Path("audit_report.md")
    with open(report_file, 'w') as f:
        f.write(formatted_report)
    
    print(f"\n📄 Detailed report saved to: {report_file.absolute()}")
    
    # Return appropriate exit code
    if report.system_health == "CRITICAL":
        sys.exit(1)
    elif report.system_health == "GOOD":
        sys.exit(0)
    else:
        sys.exit(0)

if __name__ == "__main__":
    main()