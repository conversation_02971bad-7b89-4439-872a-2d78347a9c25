#!/usr/bin/env python3
"""
Test RG Scaler Fix Implementation
=================================

Validates that the RG Scaler fixes work correctly with Grammar Bridge data.
Tests session isolation, timestamp conversion, and data structure alignment.
"""

import sys
import json
from pathlib import Path

# Add current directory to path
sys.path.append('.')

from compartments.predict import PredictCompartment
from core_predictor.rg_scaler_production import RGScaler

def test_rg_scaler_fixes():
    """Test the complete RG Scaler fix implementation"""
    
    print("🔧 TESTING RG SCALER FIXES")
    print("=" * 60)
    
    # Initialize components
    predict_comp = PredictCompartment()
    rg_scaler = RGScaler()
    
    # Load Grammar Bridge data
    cascade_events_file = Path("grammar_bridge/cascade_events.json")
    if not cascade_events_file.exists():
        print("❌ ERROR: Grammar Bridge cascade events not found")
        return False
    
    with cascade_events_file.open("r") as f:
        data = json.load(f)
    
    all_events = data['unified_cascade_events']
    
    # Test 1: Session Isolation
    print(f"\n🔬 TEST 1: SESSION ISOLATION")
    print(f"=" * 40)
    
    # Get unique sessions
    sessions = list(set(event.get('session_id', 'unknown') for event in all_events))
    test_session = 'NYAM_Lvl-1_2025_08_04_REAL'  # Known session with events
    
    print(f"Total sessions in Grammar Bridge: {len(sessions)}")
    print(f"Testing with session: {test_session}")
    
    # Test session loading
    session_events = predict_comp._load_grammar_bridge_events(f"enhanced_{test_session}")
    
    if session_events:
        print(f"✅ Session isolation SUCCESS: {len(session_events)} events loaded")
        
        # Validate all events belong to same session
        unique_sessions = set(event.get('session_id', '') for event in session_events)
        if len(unique_sessions) == 1:
            print(f"✅ Session purity confirmed: all events from {list(unique_sessions)[0]}")
        else:
            print(f"❌ Session contamination detected: {unique_sessions}")
            return False
    else:
        print(f"❌ Session isolation FAILED: no events loaded")
        return False
    
    # Test 2: Timestamp Conversion
    print(f"\n🔬 TEST 2: TIMESTAMP CONVERSION")
    print(f"=" * 40)
    
    # Check original timestamps
    original_timestamps = [event.get('timestamp_minutes', 0) for event in session_events]
    print(f"Original timestamp range: {min(original_timestamps):.1f} - {max(original_timestamps):.1f}")
    print(f"Original time span: {max(original_timestamps) - min(original_timestamps):.1f} minutes")
    
    # Test adapter
    adapted_data = predict_comp._adapt_cascade_events_to_rg_scaler(session_events)
    adapted_events = adapted_data.get('micro_timing_analysis', {}).get('cascade_events', [])
    
    if adapted_events:
        relative_timestamps = [event.get('timestamp_minutes', 0) for event in adapted_events]
        print(f"✅ Timestamp conversion SUCCESS: {len(adapted_events)} events adapted")
        print(f"Relative timestamp range: {min(relative_timestamps):.1f} - {max(relative_timestamps):.1f}")
        print(f"Relative time span: {max(relative_timestamps) - min(relative_timestamps):.1f} minutes")
        
        # Validate timestamps start from 0
        if min(relative_timestamps) == 0.0:
            print(f"✅ Timestamps correctly start from 0.0")
        else:
            print(f"❌ Timestamps don't start from 0: {min(relative_timestamps)}")
            return False
    else:
        print(f"❌ Timestamp conversion FAILED: no adapted events")
        return False
    
    # Test 3: RG Scaler Integration
    print(f"\n🔬 TEST 3: RG SCALER INTEGRATION")
    print(f"=" * 40)
    
    # Test with adapted session data
    session_data = {
        'micro_timing_analysis': {
            'cascade_events': adapted_events
        }
    }
    
    try:
        result = rg_scaler.transform_session_data(session_data)
        
        if result:
            print(f"✅ RG Scaler integration SUCCESS:")
            print(f"   Optimal Scale: {result.optimal_scale:.2f} minutes")
            print(f"   Event Density: {result.event_density:.3f} events/min")
            print(f"   Density Regime: {result.density_regime}")
            print(f"   Scaling Confidence: {result.scaling_confidence:.3f}")
            print(f"   Number of Bins: {result.num_bins}")
            print(f"   Duration: {result.duration_minutes:.1f} minutes")
        else:
            print(f"❌ RG Scaler integration FAILED: returned None")
            return False
            
    except Exception as e:
        print(f"❌ RG Scaler integration ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test 4: Multiple Sessions
    print(f"\n🔬 TEST 4: MULTIPLE SESSION PROCESSING")
    print(f"=" * 40)
    
    test_sessions = ['NYAM_Lvl-1_2025_08_04_REAL', 'NYPM_Lvl-1_2025_08_05_COMPLETE']
    success_count = 0
    
    for session in test_sessions:
        try:
            session_events = predict_comp._load_grammar_bridge_events(f"enhanced_{session}")
            if session_events:
                adapted_data = predict_comp._adapt_cascade_events_to_rg_scaler(session_events)
                adapted_events = adapted_data.get('micro_timing_analysis', {}).get('cascade_events', [])
                
                if adapted_events:
                    session_data = {'micro_timing_analysis': {'cascade_events': adapted_events}}
                    result = rg_scaler.transform_session_data(session_data)
                    
                    if result:
                        print(f"✅ {session}: scale {result.optimal_scale:.2f}, density {result.event_density:.3f}")
                        success_count += 1
                    else:
                        print(f"❌ {session}: RG Scaler failed")
                else:
                    print(f"❌ {session}: Adapter failed")
            else:
                print(f"❌ {session}: No events loaded")
                
        except Exception as e:
            print(f"❌ {session}: Error - {e}")
    
    print(f"\nMultiple session test: {success_count}/{len(test_sessions)} sessions successful")
    
    # Final assessment
    if success_count == len(test_sessions):
        print(f"\n🎉 RG SCALER FIXES: COMPLETE SUCCESS")
        print(f"   ✅ Session isolation working")
        print(f"   ✅ Timestamp conversion working") 
        print(f"   ✅ Data structure alignment working")
        print(f"   ✅ RG Scaler integration working")
        print(f"   ✅ Multiple session processing working")
        return True
    else:
        print(f"\n❌ RG SCALER FIXES: PARTIAL SUCCESS")
        print(f"   Some issues remain - check individual test results")
        return False

if __name__ == "__main__":
    success = test_rg_scaler_fixes()
    sys.exit(0 if success else 1)
