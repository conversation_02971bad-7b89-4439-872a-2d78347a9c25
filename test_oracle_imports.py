#!/usr/bin/env python3
"""Test Oracle module imports with correct paths"""

import os
import sys

# Add OpenMP fix
os.environ['DYLD_LIBRARY_PATH'] = '/opt/local/lib/libomp:' + os.environ.get('DYLD_LIBRARY_PATH', '')
os.environ['OMP_NUM_THREADS'] = '4'

print("Testing Project Oracle Module Structure...")
print("=" * 50)

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Try importing modules with different path structures
modules_to_test = [
    ('rg_scaler.rg_scaler', 'RG Scaler'),
    ('fisher_monitor.fisher_monitor', 'Fisher Monitor'),
    ('three_oracle_system.three_oracle_system', 'Three Oracle System'),
    ('oracle', 'Main Oracle'),
]

for module_path, name in modules_to_test:
    try:
        module = __import__(module_path, fromlist=[''])
        print(f"✅ {name}: imported from {module_path}")
    except ImportError as e:
        print(f"❌ {name}: {e}")
        # Try alternative paths
        alt_path = module_path.split('.')[0]
        try:
            module = __import__(alt_path, fromlist=[''])
            print(f"  ↳ Found at: {alt_path}")
        except:
            pass

print("\n" + "=" * 50)
print("Checking for XGBoost models...")

# Look for XGBoost models
import glob
xgb_files = glob.glob('**/*.xgb', recursive=True)
pkl_files = glob.glob('**/*.pkl', recursive=True)
joblib_files = glob.glob('**/*.joblib', recursive=True)

if xgb_files:
    print(f"Found {len(xgb_files)} .xgb files:")
    for f in xgb_files[:5]:
        print(f"  - {f}")
elif pkl_files:
    print(f"Found {len(pkl_files)} .pkl files (possible models):")
    for f in pkl_files[:5]:
        print(f"  - {f}")
else:
    print("⚠️ No model files found")

print("\n" + "=" * 50)
print("Import test complete!")
