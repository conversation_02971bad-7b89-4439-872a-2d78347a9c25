"""
Create Auto-Trigger System for Level-1 JSON Processing
Monitor directory for new Level-1 files and automatically process them
"""

import os
import time
import json
import subprocess
from pathlib import Path
from datetime import datetime
import logging
import argparse

class AutoTriggerSystem:
    """Automatically processes new Level-1 JSON files"""
    
    def __init__(self, watch_directory: str, state_file: str, log_file: str):
        self.watch_dir = Path(watch_directory)
        self.processed_files = set()
        self.state_file = Path(state_file)
        self.log_file = Path(log_file)
        self.setup_logging()
        self.load_processed_state()

    def setup_logging(self):
        """Setup logging for auto-trigger system"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def load_processed_state(self):
        """Load list of previously processed files"""
        if self.state_file.exists():
            try:
                with open(self.state_file, 'r') as f:
                    data = json.load(f)
                    self.processed_files = set(data.get('processed_files', []))
                self.logger.info(f"Loaded {len(self.processed_files)} previously processed files")
            except Exception as e:
                self.logger.warning(f"Could not load processed state: {e}")
                
    def save_processed_state(self):
        """Save list of processed files"""
        try:
            with open(self.state_file, 'w') as f:
                json.dump({
                    'processed_files': list(self.processed_files),
                    'last_updated': datetime.now().isoformat()
                }, f, indent=2)
        except Exception as e:
            self.logger.error(f"Could not save processed state: {e}")
            
    def scan_for_new_files(self):
        """Scan directory for new Level-1 JSON files"""
        if not self.watch_dir.exists():
            self.logger.error(f"Watch directory does not exist: {self.watch_dir}")
            return []
            
        new_files = []
        for json_file in self.watch_dir.glob("*Lvl-1*.json"):
            if str(json_file) not in self.processed_files:
                new_files.append(json_file)
                
        return new_files
        
    def validate_level1_file(self, file_path):
        """Validate that file is proper Level-1 format"""
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
                
            # Check for required Level-1 structure
            required_keys = ['level1_json']
            if not all(key in data for key in required_keys):
                return False, "Missing level1_json structure"
                
            level1 = data['level1_json']
            required_session_keys = ['session_metadata', 'price_movements']
            if not all(key in level1 for key in required_session_keys):
                return False, "Missing required session data"
                
            return True, "Valid Level-1 file"
            
        except Exception as e:
            return False, f"File validation error: {e}"
            
    def process_level1_file(self, file_path):
        """Process a single Level-1 file through Oracle system"""
        
        self.logger.info(f"🚀 PROCESSING: {file_path.name}")
        
        # Validate file first
        is_valid, message = self.validate_level1_file(file_path)
        if not is_valid:
            self.logger.error(f"❌ INVALID FILE: {message}")
            return False
            
        try:
            # Create processing script for this specific file
            process_script = f"""
import sys
import os
import json
from pathlib import Path

# Setup XGBoost environment
os.environ['DYLD_LIBRARY_PATH'] = '/opt/local/lib/libomp:' + os.environ.get('DYLD_LIBRARY_PATH', '')
os.environ['OMP_NUM_THREADS'] = '4'

sys.path.append(str(Path(__file__).resolve().parent))
from three_oracle_architecture import create_three_oracle_system
from datetime import datetime, timedelta

# Load the Level-1 file
with open('{file_path}', 'r') as f:
    session_data = json.load(f)

# Extract session info
level1 = session_data['level1_json']
session_type = level1['session_metadata']['session_type']
session_date = level1['session_metadata']['session_date']

print(f"🎯 AUTO-PROCESSING: {{session_type}} session from {{session_date}}")

# Create input format for Three-Oracle system
oracle_input = {{
    'session_metadata': level1['session_metadata'],
    'micro_timing_analysis': {{
        'cascade_events': []
    }},
    'energy_data': []
}}

# Extract cascade events from price movements
for movement in level1.get('price_movements', []):
    if any(keyword in movement.get('movement_type', '') for keyword in 
           ['cascade', 'fpfvg', 'session_high', 'session_low', 'takeout', 'rebalance']):
        oracle_input['micro_timing_analysis']['cascade_events'].append({{
            'timestamp': movement['timestamp'],
            'price_level': movement['price_level'],
            'event_type': movement['movement_type']
        }})

# Add energy data if available
if 'energy_state' in level1 and 'contamination_analysis' in level1:
    oracle_input['energy_data'].append({{
        'energy_weight': 0.5,  # Default prediction
        'contamination_weight': 0.5,
        'energy_density': level1['energy_state']['energy_density'],
        'actual_contamination': level1['contamination_analysis']['htf_contamination']['cross_session_inheritance'],
        'timestamp': level1['session_metadata']['session_start']
    }})

# Process with Three-Oracle system
oracle_system = create_three_oracle_system({{'log_level': 'INFO'}})
result = oracle_system.predict_cascade_timing(oracle_input, optimize_parameters=True)

# Generate results
timing_decision = result['timing_decision']
energy_results = result.get('energy_results')

# Convert prediction to actual time
if session_type.lower() == 'ny_pm':
    session_start = datetime.strptime('13:30:00', '%H:%M:%S')
elif session_type.lower() == 'ny_am':
    session_start = datetime.strptime('09:30:00', '%H:%M:%S')
else:
    session_start = datetime.strptime('09:30:00', '%H:%M:%S')

predicted_time = session_start + timedelta(minutes=timing_decision.final_prediction)

print(f"\\n📊 AUTO-PROCESSING RESULTS:")
print(f"   🕐 Predicted Cascade: {{predicted_time.strftime('%H:%M:%S')}}")
print(f"   ⚖️ Oracle Choice: {{timing_decision.chosen_oracle.upper()}}")
print(f"   🏥 System Health: {{timing_decision.system_health['status']}}")
if energy_results:
    print(f"   🔋 Energy Validation: {{len(energy_results['predictions'])}} predictions")
    if any(energy_results.get('divergence_detected', [])):
        print(f"   🚨 Energy Divergence: DETECTED")

# Save results
results_dir = Path.cwd() / 'auto_trigger_results'
results_dir.mkdir(parents=True, exist_ok=True)
results_file = results_dir / f"auto_results_{session_type}_{session_date}.json"
with open(results_file, 'w') as f:
    json.dump({{
        'file_processed': str(file_path),
        'processing_time': datetime.now().isoformat(),
        'predicted_cascade_time': predicted_time.strftime('%H:%M:%S'),
        'oracle_choice': timing_decision.chosen_oracle,
        'system_health': timing_decision.system_health['status'],
        'energy_divergence_detected': bool(energy_results and any(energy_results.get('divergence_detected', []))) if energy_results else False
    }}, f, indent=2)

print(f"✅ Results saved: {{results_file}}")
"""
            
            # Write and execute the processing script
            script_file = f"/tmp/process_{file_path.stem}.py"
            with open(script_file, 'w') as f:
                f.write(process_script)
                
            # Execute the processing
            result = subprocess.run([
                'python3', script_file
            ], capture_output=True, text=True, cwd=str(Path(__file__).resolve().parent))

            if result.returncode == 0:
                self.logger.info(f"✅ SUCCESS: {file_path.name} processed successfully")
                self.logger.info(f"Output: {result.stdout}")
                
                # Mark as processed
                self.processed_files.add(str(file_path))
                self.save_processed_state()
                return True
            else:
                self.logger.error(f"❌ PROCESSING FAILED: {file_path.name}")
                self.logger.error(f"Error: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ PROCESSING ERROR: {e}")
            return False
            
    def run_continuous_monitoring(self, check_interval=30):
        """Run continuous monitoring for new files"""
        self.logger.info(f"🔍 STARTING AUTO-TRIGGER MONITORING")
        self.logger.info(f"   Watch Directory: {self.watch_dir}")
        self.logger.info(f"   Check Interval: {check_interval} seconds")
        self.logger.info(f"   Previously Processed: {len(self.processed_files)} files")
        
        try:
            while True:
                new_files = self.scan_for_new_files()
                
                if new_files:
                    self.logger.info(f"📁 FOUND {len(new_files)} new Level-1 files")
                    
                    for file_path in new_files:
                        self.process_level1_file(file_path)
                        time.sleep(2)  # Brief pause between files
                else:
                    self.logger.debug("No new files found")
                    
                time.sleep(check_interval)
                
        except KeyboardInterrupt:
            self.logger.info("🛑 AUTO-TRIGGER MONITORING STOPPED")
            
    def run_single_scan(self):
        """Run a single scan without continuous monitoring"""
        self.logger.info(f"🔍 SINGLE SCAN FOR NEW LEVEL-1 FILES")
        
        new_files = self.scan_for_new_files()
        
        if new_files:
            self.logger.info(f"📁 FOUND {len(new_files)} new Level-1 files")
            
            for file_path in new_files:
                success = self.process_level1_file(file_path)
                if success:
                    self.logger.info(f"✅ Completed: {file_path.name}")
                else:
                    self.logger.error(f"❌ Failed: {file_path.name}")
        else:
            self.logger.info("📋 No new Level-1 files to process")
            
        return len(new_files)

def main():
    print("🤖 AUTO-TRIGGER SYSTEM FOR LEVEL-1 JSON PROCESSING")
    print("=" * 60)

    parser = argparse.ArgumentParser()
    parser.add_argument("--watch-dir", default=os.getenv("ORACLE_WATCH_DIR", "data/sessions/level_1"), help="Directory to watch for Level-1 JSON files")
    parser.add_argument("--state-file", default=os.getenv("ORACLE_STATE_FILE", "processed_files.json"), help="Path to processed files state JSON")
    parser.add_argument("--log-file", default=os.getenv("ORACLE_LOG_FILE", "auto_trigger.log"), help="Path to log file")
    parser.add_argument("--continuous", action="store_true", help="Run continuous monitoring")
    parser.add_argument("--interval", type=int, default=int(os.getenv("ORACLE_WATCH_INTERVAL", "30")), help="Check interval seconds in continuous mode")
    args = parser.parse_args()

    auto_trigger = AutoTriggerSystem(args.watch_dir, args.state_file, args.log_file)

    if args.continuous:
        print("🔄 CONTINUOUS MONITORING MODE")
        auto_trigger.run_continuous_monitoring(check_interval=args.interval)
    else:
        print("🔍 SINGLE SCAN MODE")
        files_processed = auto_trigger.run_single_scan()
        print(f"\n📊 SCAN COMPLETE: {files_processed} files processed")

if __name__ == "__main__":
    main()