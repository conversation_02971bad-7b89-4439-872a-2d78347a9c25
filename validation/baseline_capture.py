"""Baseline Performance Capture - Pre-Migration Validation

Captures current HawkesCascadePredictor performance to ensure NO DEGRADATION
during multi-dimensional enhancement migration.

Critical: Any performance degradation halts migration immediately.
"""

import sys
import os
import json
import numpy as np
from datetime import datetime
from typing import Dict, Any, Optional

# Add paths for existing system
sys.path.append('/Users/<USER>/grok-claude-automation/src')
sys.path.append('/Users/<USER>/grok-claude-automation')

try:
    from hawkes_cascade_predictor import HawkesCascadePredictor
    from utils import load_json_data
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Ensure grok-claude-automation paths are correct")
    exit(1)

class BaselineCapture:
    """Capture and validate baseline performance of existing Hawkes system"""
    
    def __init__(self):
        self.baseline_data = {}
        self.test_files = [
            '/Users/<USER>/grok-claude-automation/data/sessions/level_1/NYAM_Lvl-1_2025_07_25.json',
            '/Users/<USER>/grok-claude-automation/data/sessions/level_1/ASIA_Lvl-1_2025_07_31.json'
        ]
        self.known_cascades = {
            'NYAM_Lvl-1_2025_07_25.json': 8.0,  # Known cascade at 8 minutes
            'ASIA_Lvl-1_2025_07_31.json': 23.0  # Asia session low at ~23 minutes
        }
    
    def capture_baseline_performance(self) -> Dict[str, Any]:
        """Capture current system performance across test cases"""
        
        print("📊 BASELINE PERFORMANCE CAPTURE")
        print("=" * 50)
        
        predictor = HawkesCascadePredictor()
        
        baseline_results = {
            'capture_timestamp': datetime.now().isoformat(),
            'system_version': 'grok-claude-automation-current',
            'test_cases': {},
            'overall_metrics': {}
        }
        
        total_errors = []
        successful_predictions = 0
        
        for test_file in self.test_files:
            if not os.path.exists(test_file):
                print(f"⚠️ Skipping {test_file} - file not found")
                continue
                
            filename = os.path.basename(test_file)
            print(f"\n🔍 Testing: {filename}")
            
            try:
                # Load session data
                session_data = load_json_data(filename.replace('.json', ''))
                
                # Generate prediction
                prediction = predictor.predict_cascade_timing(session_data)
                
                # Get actual cascade time
                actual_time = self.known_cascades.get(filename)
                if actual_time is None:
                    print(f"   ⚠️ No ground truth for {filename}")
                    continue
                
                # Calculate metrics
                prediction_error = abs(prediction.predicted_cascade_time - actual_time)
                
                # Validate prediction
                validation = predictor.validate_against_actual(prediction, actual_time)
                
                test_result = {
                    'predicted_time': prediction.predicted_cascade_time,
                    'actual_time': actual_time,
                    'error_minutes': prediction_error,
                    'accuracy_grade': validation['accuracy_grade'],
                    'confidence': prediction.prediction_confidence,
                    'parameters': {
                        'mu': prediction.parameters_used.mu,
                        'alpha': prediction.parameters_used.alpha,
                        'beta': prediction.parameters_used.beta,
                        'threshold': prediction.parameters_used.threshold
                    },
                    'intensity_points': len(prediction.intensity_buildup),
                    'triggering_events': len(prediction.triggering_events)
                }
                
                baseline_results['test_cases'][filename] = test_result
                total_errors.append(prediction_error)
                
                if validation['accuracy_grade'] in ['excellent', 'good']:
                    successful_predictions += 1
                
                print(f"   🎯 Predicted: {prediction.predicted_cascade_time:.1f} min")
                print(f"   ✅ Actual: {actual_time:.1f} min")
                print(f"   📏 Error: {prediction_error:.1f} min ({validation['accuracy_grade']})")
                print(f"   📊 Confidence: {prediction.prediction_confidence:.3f}")
                
            except Exception as e:
                print(f"   ❌ Error testing {filename}: {e}")
                baseline_results['test_cases'][filename] = {
                    'error': str(e),
                    'status': 'failed'
                }
        
        # Calculate overall metrics
        if total_errors:
            mean_error = np.mean(total_errors)
            median_error = np.median(total_errors)
            max_error = np.max(total_errors)
            success_rate = successful_predictions / len(total_errors)
            
            baseline_results['overall_metrics'] = {
                'mean_absolute_error': mean_error,
                'median_absolute_error': median_error,
                'max_absolute_error': max_error,
                'success_rate': success_rate,
                'total_test_cases': len(total_errors),
                'successful_predictions': successful_predictions
            }
            
            print(f"\n📈 BASELINE METRICS:")
            print(f"   Mean Error: {mean_error:.2f} minutes")
            print(f"   Median Error: {median_error:.2f} minutes")
            print(f"   Max Error: {max_error:.2f} minutes")
            print(f"   Success Rate: {success_rate:.1%} ({successful_predictions}/{len(total_errors)})")
            
            # Determine baseline quality
            if mean_error <= 5.0 and success_rate >= 0.6:
                baseline_quality = "excellent"
            elif mean_error <= 10.0 and success_rate >= 0.4:
                baseline_quality = "good"
            elif mean_error <= 20.0 and success_rate >= 0.2:
                baseline_quality = "moderate"
            else:
                baseline_quality = "poor"
            
            baseline_results['overall_metrics']['baseline_quality'] = baseline_quality
            
            print(f"   🏆 Baseline Quality: {baseline_quality}")
            
        else:
            print("❌ No successful test cases - cannot establish baseline")
            baseline_results['overall_metrics'] = {
                'status': 'no_successful_tests',
                'baseline_quality': 'failed'
            }
        
        # Save baseline data
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        baseline_file = f"/Users/<USER>/grok-claude-automation/project_oracle/validation/baseline_performance_{timestamp}.json"
        
        with open(baseline_file, 'w') as f:
            json.dump(baseline_results, f, indent=2)
        
        print(f"\n💾 Baseline saved: {baseline_file}")
        
        self.baseline_data = baseline_results
        return baseline_results
    
    def validate_performance_requirements(self) -> bool:
        """Validate that baseline meets minimum performance requirements"""
        
        if not self.baseline_data:
            print("❌ No baseline data available for validation")
            return False
        
        metrics = self.baseline_data.get('overall_metrics', {})
        
        # Minimum requirements for migration
        min_success_rate = 0.5  # 50% success rate
        max_mean_error = 30.0   # 30 minutes max mean error
        
        success_rate = metrics.get('success_rate', 0.0)
        mean_error = metrics.get('mean_absolute_error', float('inf'))
        
        print(f"\n🔍 PERFORMANCE REQUIREMENTS CHECK:")
        print(f"   Success Rate: {success_rate:.1%} (required: {min_success_rate:.1%})")
        print(f"   Mean Error: {mean_error:.1f} min (required: <{max_mean_error:.1f} min)")
        
        meets_requirements = (
            success_rate >= min_success_rate and 
            mean_error <= max_mean_error
        )
        
        if meets_requirements:
            print("✅ Baseline meets performance requirements for migration")
        else:
            print("❌ Baseline does not meet minimum requirements")
            print("🚨 MIGRATION BLOCKED - Fix baseline performance first")
        
        return meets_requirements
    
    def get_performance_thresholds(self) -> Dict[str, float]:
        """Get performance thresholds that enhanced system must not exceed"""
        
        if not self.baseline_data:
            return {}
        
        metrics = self.baseline_data.get('overall_metrics', {})
        
        # Set thresholds with small buffer for enhanced system
        thresholds = {
            'max_allowed_mean_error': metrics.get('mean_absolute_error', 30.0) * 1.1,  # 10% buffer
            'min_required_success_rate': max(0.4, metrics.get('success_rate', 0.5) * 0.9),  # 10% buffer
            'max_allowed_max_error': metrics.get('max_absolute_error', 60.0) * 1.2  # 20% buffer
        }
        
        print(f"\n🎯 PERFORMANCE THRESHOLDS FOR ENHANCED SYSTEM:")
        for metric, threshold in thresholds.items():
            print(f"   {metric}: {threshold:.2f}")
        
        return thresholds


def main():
    """Main execution for baseline capture"""
    
    print("🚀 HAWKES MIGRATION - BASELINE CAPTURE")
    print("=" * 60)
    
    # Initialize baseline capture
    baseline = BaselineCapture()
    
    # Capture current performance
    baseline_results = baseline.capture_baseline_performance()
    
    # Validate performance requirements
    meets_requirements = baseline.validate_performance_requirements()
    
    if not meets_requirements:
        print("\n🚨 CRITICAL: Baseline performance insufficient")
        print("❌ Migration cannot proceed until baseline is improved")
        exit(1)
    
    # Get thresholds for enhanced system
    thresholds = baseline.get_performance_thresholds()
    
    print(f"\n✅ BASELINE CAPTURE COMPLETE")
    print(f"🔄 Ready for Hawkes Engine enhancement migration")
    print(f"📋 Enhanced system must not exceed performance thresholds")
    
    return baseline_results, thresholds


if __name__ == "__main__":
    main()