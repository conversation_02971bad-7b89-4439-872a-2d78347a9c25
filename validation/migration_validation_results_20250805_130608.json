{"validation_metadata": {"timestamp": "2025-08-05T13:06:08.843132", "validator_version": "migration_validator_v1.0", "baseline_system": "HawkesCascadePredictor", "enhanced_system": "ProjectOracle", "baseline_threshold_buffer": 0.1}, "test_cases": [{"case_id": "NYAM_2025_07_25", "session_data": {"session_metadata": {"session_type": "NY_AM", "duration_minutes": 120, "date": "2025-08-05"}, "price_data": {"high": 23550, "low": 23450, "range": 100, "session_character": "expansion_consolidation"}, "micro_timing_analysis": {"cascade_events": [{"timestamp": "09:30", "price_level": 23500, "event_type": "open"}, {"timestamp": "09:40", "price_level": 23520, "event_type": "move"}, {"timestamp": "09:50", "price_level": 23480, "event_type": "low"}, {"timestamp": "10:00", "price_level": 23550, "event_type": "cascade"}]}}, "ground_truth_cascade_time": 8.0, "session_type": "NY_AM", "expected_difficulty": "moderate", "notes": "Classic expansion cascade, well-documented ground truth"}, {"case_id": "ASIA_2025_07_31", "session_data": {"session_metadata": {"session_type": "NY_AM", "duration_minutes": 120, "date": "2025-08-05"}, "price_data": {"high": 23550, "low": 23450, "range": 100, "session_character": "expansion_consolidation"}, "micro_timing_analysis": {"cascade_events": [{"timestamp": "09:30", "price_level": 23500, "event_type": "open"}, {"timestamp": "09:40", "price_level": 23520, "event_type": "move"}, {"timestamp": "09:50", "price_level": 23480, "event_type": "low"}, {"timestamp": "10:00", "price_level": 23550, "event_type": "cascade"}]}}, "ground_truth_cascade_time": 23.0, "session_type": "ASIA", "expected_difficulty": "hard", "notes": "Asia session low event, HTF coupling test case"}, {"case_id": "SYNTHETIC_LOW_VOLATILITY", "session_data": {"session_metadata": {"session_type": "NY_AM", "duration_minutes": 120, "date": "2025-08-05"}, "price_data": {"high": 23520, "low": 23500, "range": 20, "session_character": "consolidation_dominant"}, "micro_timing_analysis": {"cascade_events": [{"timestamp": "09:30", "price_level": 23510, "event_type": "open"}, {"timestamp": "09:45", "price_level": 23515, "event_type": "move"}, {"timestamp": "10:00", "price_level": 23505, "event_type": "move"}, {"timestamp": "10:15", "price_level": 23520, "event_type": "cascade"}]}}, "ground_truth_cascade_time": 45.0, "session_type": "SYNTHETIC", "expected_difficulty": "easy", "notes": "Low volatility consolidation test"}, {"case_id": "SYNTHETIC_HIGH_VOLATILITY", "session_data": {"session_metadata": {"session_type": "NY_AM", "duration_minutes": 120, "date": "2025-08-05"}, "price_data": {"high": 23600, "low": 23400, "range": 200, "session_character": "expansion_consolidation_final_expansion"}, "micro_timing_analysis": {"cascade_events": [{"timestamp": "09:30", "price_level": 23500, "event_type": "open"}, {"timestamp": "09:35", "price_level": 23450, "event_type": "low"}, {"timestamp": "09:40", "price_level": 23550, "event_type": "high"}, {"timestamp": "09:42", "price_level": 23600, "event_type": "cascade"}]}}, "ground_truth_cascade_time": 12.0, "session_type": "SYNTHETIC", "expected_difficulty": "moderate", "notes": "High volatility expansion test"}], "comparison_results": [{"test_case_id": "NYAM_2025_07_25", "ground_truth": 8.0, "baseline_prediction": {"predicted_time": 6.2390563895865645, "confidence": 0.7, "processing_time": 3.504753112792969e-05, "methodology": "mock_baseline_system", "parameters_used": {"mock": true}, "additional_metrics": {"note": "baseline_system_unavailable"}}, "baseline_error": 1.7609436104134355, "baseline_accuracy_grade": "good", "enhanced_prediction": {"predicted_time": 0, "confidence": 0.1, "processing_time": 0.0014760494232177734, "methodology": "integrated_oracle_system", "parameters_used": {"rg_scaler": {"optimal_scale": 15.0, "density": 0.1, "confidence": 0.5, "classification": "medium"}, "hawkes_engine": {"base_available": false, "enhancement_active": true, "confidence_boost": 0.0, "methodology": "multi_dimensional_hawkes_only"}, "vqe_optimization": false}, "additional_metrics": {"enhancement_active": true, "confidence_boost": 0.0, "domain_constraints_satisfied": true, "processing_breakdown": {"rg_scaling_confidence": 0.5, "hawkes_enhancement_active": true, "vqe_optimization_active": false, "processing_time_seconds": 0.001268, "events_processed": 4, "scaled_events": 3}}}, "enhanced_error": 8.0, "enhanced_accuracy_grade": "moderate", "error_improvement": -6.2390563895865645, "confidence_improvement": -0.6, "processing_time_ratio": 42.1156462585034, "performance_maintained": false, "performance_improved": false, "regression_detected": true}, {"test_case_id": "ASIA_2025_07_31", "ground_truth": 23.0, "baseline_prediction": {"predicted_time": 29.115441437400325, "confidence": 0.7, "processing_time": 1.2874603271484375e-05, "methodology": "mock_baseline_system", "parameters_used": {"mock": true}, "additional_metrics": {"note": "baseline_system_unavailable"}}, "baseline_error": 6.1154414374003245, "baseline_accuracy_grade": "moderate", "enhanced_prediction": {"predicted_time": 0, "confidence": 0.1, "processing_time": 0.0011970996856689453, "methodology": "integrated_oracle_system", "parameters_used": {"rg_scaler": {"optimal_scale": 15.0, "density": 0.1, "confidence": 0.5, "classification": "medium"}, "hawkes_engine": {"base_available": false, "enhancement_active": true, "confidence_boost": 0.0, "methodology": "multi_dimensional_hawkes_only"}, "vqe_optimization": false}, "additional_metrics": {"enhancement_active": true, "confidence_boost": 0.0, "domain_constraints_satisfied": true, "processing_breakdown": {"rg_scaling_confidence": 0.5, "hawkes_enhancement_active": true, "vqe_optimization_active": false, "processing_time_seconds": 0.001008, "events_processed": 4, "scaled_events": 3}}}, "enhanced_error": 23.0, "enhanced_accuracy_grade": "very_poor", "error_improvement": -16.884558562599675, "confidence_improvement": -0.6, "processing_time_ratio": 92.98148148148148, "performance_maintained": false, "performance_improved": false, "regression_detected": true}, {"test_case_id": "SYNTHETIC_LOW_VOLATILITY", "ground_truth": 45.0, "baseline_prediction": {"predicted_time": 42.77578409299313, "confidence": 0.7, "processing_time": 1.4066696166992188e-05, "methodology": "mock_baseline_system", "parameters_used": {"mock": true}, "additional_metrics": {"note": "baseline_system_unavailable"}}, "baseline_error": 2.2242159070068723, "baseline_accuracy_grade": "good", "enhanced_prediction": {"predicted_time": 0, "confidence": 0.1, "processing_time": 0.0010440349578857422, "methodology": "integrated_oracle_system", "parameters_used": {"rg_scaler": {"optimal_scale": 15.0, "density": 0.1, "confidence": 0.5, "classification": "medium"}, "hawkes_engine": {"base_available": false, "enhancement_active": true, "confidence_boost": 0.0, "methodology": "multi_dimensional_hawkes_only"}, "vqe_optimization": false}, "additional_metrics": {"enhancement_active": true, "confidence_boost": 0.0, "domain_constraints_satisfied": true, "processing_breakdown": {"rg_scaling_confidence": 0.5, "hawkes_enhancement_active": true, "vqe_optimization_active": false, "processing_time_seconds": 0.00087, "events_processed": 4, "scaled_events": 4}}}, "enhanced_error": 45.0, "enhanced_accuracy_grade": "very_poor", "error_improvement": -42.77578409299313, "confidence_improvement": -0.6, "processing_time_ratio": 74.22033898305085, "performance_maintained": false, "performance_improved": false, "regression_detected": true}, {"test_case_id": "SYNTHETIC_HIGH_VOLATILITY", "ground_truth": 12.0, "baseline_prediction": {"predicted_time": 12.184621881568136, "confidence": 0.7, "processing_time": 1.0013580322265625e-05, "methodology": "mock_baseline_system", "parameters_used": {"mock": true}, "additional_metrics": {"note": "baseline_system_unavailable"}}, "baseline_error": 0.18462188156813575, "baseline_accuracy_grade": "excellent", "enhanced_prediction": {"predicted_time": 0, "confidence": 0.1, "processing_time": 0.0011451244354248047, "methodology": "integrated_oracle_system", "parameters_used": {"rg_scaler": {"optimal_scale": 15.0, "density": 0.1, "confidence": 0.5, "classification": "medium"}, "hawkes_engine": {"base_available": false, "enhancement_active": true, "confidence_boost": 0.0, "methodology": "multi_dimensional_hawkes_only"}, "vqe_optimization": false}, "additional_metrics": {"enhancement_active": true, "confidence_boost": 0.0, "domain_constraints_satisfied": true, "processing_breakdown": {"rg_scaling_confidence": 0.5, "hawkes_enhancement_active": true, "vqe_optimization_active": false, "processing_time_seconds": 0.000958, "events_processed": 4, "scaled_events": 1}}}, "enhanced_error": 12.0, "enhanced_accuracy_grade": "poor", "error_improvement": -11.815378118431864, "confidence_improvement": -0.6, "processing_time_ratio": 114.35714285714286, "performance_maintained": false, "performance_improved": false, "regression_detected": true}], "validation_report": {"validation_timestamp": "2025-08-05T13:06:08.842971", "total_test_cases": 4, "baseline_metrics": {"mean_absolute_error": 2.571305709097192, "median_absolute_error": 1.992579758710154, "max_absolute_error": 6.1154414374003245, "success_rate": 0.75}, "enhanced_metrics": {"mean_absolute_error": 22.0, "median_absolute_error": 17.5, "max_absolute_error": 45.0, "success_rate": 0.0}, "performance_maintained_count": 0, "performance_improved_count": 0, "regression_detected_count": 4, "mean_error_improvement": -19.428694290902808, "confidence_improvement": 0, "statistical_significance": {"sample_size": 4, "baseline_error_stats": {"mean": 2.571305709097192, "std": 2.1814167308724945, "median": 1.992579758710154, "min": 0.18462188156813575, "max": 6.1154414374003245}, "enhanced_error_stats": {"mean": 22.0, "std": 14.370107863199914, "median": 17.5, "min": 8.0, "max": 45.0}, "error_improvement_stats": {"mean": -19.428694290902808, "std": 13.995431039635255, "median": -14.349968340515769, "percentage_improved": 0.0}, "statistical_tests": {"error_paired_ttest": {"statistic": -2.4044622520925274, "pvalue": 0.09549070729868861, "significant": "False"}, "confidence_paired_ttest": {"statistic": Infinity, "pvalue": 0.0, "significant": "True"}, "error_cohens_d": -1.8903901055292707, "effect_size_interpretation": "large"}}, "migration_recommended": false, "migration_confidence": 0.05, "critical_issues": ["High regression rate: 100.0%", "Mean error degradation: 755.6%", "Success rate drop: 75.0%"], "recommendations": ["❌ Migration not recommended - address critical issues first", "🔧 Fix: High regression rate: 100.0%", "🔧 Fix: Mean error degradation: 755.6%", "🔧 Fix: Success rate drop: 75.0%"]}}