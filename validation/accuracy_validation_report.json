{"overall_metrics": {"accuracy": 0.9701492537313433, "precision": 0.9732914375490967, "recall": 0.9701492537313433, "f1_score": 0.9706738416759737, "confidence_interval_lower": 0.8975344443664799, "confidence_interval_upper": 0.991775305161102, "p_value": 0.0, "statistical_significance": true, "sample_size": 67, "baseline_comparison": 0.059149253731343276}, "temporal_validation": {"temporal_accuracy": 0.9285714285714286, "temporal_precision": 0.9404761904761906, "temporal_recall": 0.9285714285714286, "temporal_f1": 0.9297173414820473, "train_accuracy": 0.9811320754716981, "stability_score": 0.9474393530997305, "train_samples": 53, "test_samples": 14, "temporal_degradation": 0.052560646900269514}, "cross_validation": {"cv_mean": 0.8727272727272727, "cv_std": 0.16861124537264918, "cv_scores": [0.5454545454545454, 0.9090909090909091, 1.0, 0.9090909090909091, 1.0], "cv_stability": 0.8313887546273508, "standard_cv_mean": 0.9549450549450549, "standard_cv_std": 0.036841878273055385, "temporal_vs_standard_diff": -0.0822177822177822}, "baseline_comparison": {"baseline_accuracy": 0.911, "current_accuracy": 0.9701492537313433, "improvement": 0.059149253731343276, "improvement_percentage": 6.492783066009141, "z_score": 1.7003271096592116, "p_value": 0.08906941389935619, "statistically_significant": false, "effect_size": 0.2585922044510536, "effect_size_interpretation": "small", "meets_baseline": true, "confidence_interval_includes_baseline": true}, "statistical_tests": {"random_chance_p_value": 0.0, "significantly_better_than_random": true, "precision_recall_balance": 0.9968578161822466, "class_0_accuracy": 0.96, "class_1_accuracy": 1.0, "class_balance_score": 0.96, "sample_size_adequate": true}, "production_readiness": {"gates": {"accuracy_meets_baseline": true, "statistically_significant": true, "temporally_stable": false, "cross_validation_stable": false, "adequate_sample_size": true, "confident_performance": true}, "gates_passed": 4, "total_gates": 6, "production_ready": false, "readiness_score": 0.6666666666666666, "critical_issues": ["temporal_instability", "cross_validation_instability"]}, "recommendations": ["WARNING: Temporal instability detected (stability=0.947). Model may not generalize well to future data.", "WARNING: High cross-validation variance (std=0.1686). Consider regularization or more stable features.", "EXCELLENT: Accuracy 0.970 meets/exceeds baseline. Model shows strong performance."]}