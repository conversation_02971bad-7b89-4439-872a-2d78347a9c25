#!/usr/bin/env python3
"""
Out-of-Time Validation Harness (scaffold)

Performs temporal split planning on data_manifest.json and produces
summary reports. If minority class is below threshold, it emits a
SKIPPED report with guidance. This is model-agnostic; plug training
and evaluation later via compartments/ml_update.

Outputs:
- validation/out_of_time_eval_report.json
- validation/learning_curves.json

Usage:
  python -m validation.out_of_time_eval \
    --manifest data_manifest.json \
    --min-non-cascade 30 \
    --report validation/out_of_time_eval_report.json \
    --learning-curves validation/learning_curves.json
"""
from __future__ import annotations
import argparse
import json
from pathlib import Path
from typing import Dict, Any, List, Tuple
from collections import defaultdict


def load_json(path: Path) -> Dict[str, Any]:
    with path.open("r") as f:
        return json.load(f)


def parse_manifest_items(manifest: Dict[str, Any]) -> List[Dict[str, Any]]:
    items = manifest.get("items", [])
    # Consider only structurally valid items with known labels
    out = []
    for it in items:
        if not it.get("valid"):
            continue
        if it.get("label") in ("cascade", "non_cascade"):
            out.append(it)
    return out


def split_by_date(items: List[Dict[str, Any]], cutoff_date: str | None = None) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], str]:
    # Sort unique dates
    dates = sorted({it.get("session_date") for it in items if it.get("session_date")})
    if not dates:
        return [], items, "no_dates"
    if cutoff_date and cutoff_date in dates:
        cutoff_idx = dates.index(cutoff_date) + 1
    else:
        # 80/20 temporal split
        cutoff_idx = max(1, int(len(dates) * 0.8))
    left = set(dates[:cutoff_idx])
    train = [it for it in items if it.get("session_date") in left]
    test = [it for it in items if it.get("session_date") not in left]
    chosen_cutoff = dates[cutoff_idx - 1] if cutoff_idx - 1 < len(dates) else dates[-1]
    return train, test, chosen_cutoff


def summarize_counts(items: List[Dict[str, Any]]) -> Dict[str, int]:
    c = defaultdict(int)
    for it in items:
        c[it.get("label")] += 1
    return {"cascade": c.get("cascade", 0), "non_cascade": c.get("non_cascade", 0)}


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--manifest", default="data_manifest.json")
    ap.add_argument("--min-non-cascade", type=int, default=30)
    ap.add_argument("--cutoff-date", default=None)
    ap.add_argument("--report", default="validation/out_of_time_eval_report.json")
    ap.add_argument("--learning-curves", default="validation/learning_curves.json")
    args = ap.parse_args()

    manifest_path = Path(args.manifest)
    report_path = Path(args.report)
    curves_path = Path(args.learning_curves)
    report_path.parent.mkdir(parents=True, exist_ok=True)

    if not manifest_path.exists():
        out = {
            "status": "ERROR",
            "error": f"manifest_not_found:{manifest_path}",
        }
        report_path.write_text(json.dumps(out, indent=2))
        print(json.dumps(out))
        return

    manifest = load_json(manifest_path)
    items = parse_manifest_items(manifest)
    counts_all = summarize_counts(items)

    # Gate on non-cascade count
    if counts_all["non_cascade"] < args.min_non_cascade:
        out = {
            "status": "SKIPPED",
            "reason": "insufficient_non_cascade",
            "threshold": args.min_non_cascade,
            "counts_all": counts_all,
            "hint": "Curate label_overrides to reach >= min-non-cascade then re-run.",
        }
        report_path.write_text(json.dumps(out, indent=2))
        # Write empty curves
        curves_path.write_text(json.dumps({"status": "SKIPPED"}, indent=2))
        print(json.dumps(out))
        return

    train, test, cutoff = split_by_date(items, args.cutoff_date)
    counts_train = summarize_counts(train)
    counts_test = summarize_counts(test)

    # Placeholder metrics until model is integrated
    out = {
        "status": "PLANNED",
        "cutoff_date": cutoff,
        "counts_all": counts_all,
        "counts_train": counts_train,
        "counts_test": counts_test,
        "notes": [
            "No model evaluation performed in scaffold.",
            "Integrate ml_update compartment to evaluate out-of-time performance.",
        ],
    }
    report_path.write_text(json.dumps(out, indent=2))

    # Learning curve scaffold: fractions of training data
    fractions = [0.2, 0.4, 0.6, 0.8, 1.0]
    lc = {
        "status": "PLANNED",
        "fractions": fractions,
        "counts_train": counts_train,
        "notes": [
            "Compute accuracy and CI at each fraction once model is wired.",
            "Stop when d(accuracy)/dn < 0.001.",
        ],
    }
    curves_path.write_text(json.dumps(lc, indent=2))

    print(json.dumps({"status": "OK", "cutoff": cutoff, "train": counts_train, "test": counts_test}))


if __name__ == "__main__":
    main()

