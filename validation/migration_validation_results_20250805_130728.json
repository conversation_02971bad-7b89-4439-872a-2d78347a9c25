{"validation_metadata": {"timestamp": "2025-08-05T13:07:28.215979", "validator_version": "migration_validator_v1.0", "baseline_system": "HawkesCascadePredictor", "enhanced_system": "ProjectOracle", "baseline_threshold_buffer": 0.1}, "test_cases": [{"case_id": "NYAM_2025_07_25", "session_data": {"session_metadata": {"session_type": "NY_AM", "duration_minutes": 120, "date": "2025-08-05"}, "price_data": {"high": 23550, "low": 23450, "range": 100, "session_character": "expansion_consolidation"}, "micro_timing_analysis": {"cascade_events": [{"timestamp": "09:30", "price_level": 23500, "event_type": "open"}, {"timestamp": "09:40", "price_level": 23520, "event_type": "move"}, {"timestamp": "09:50", "price_level": 23480, "event_type": "low"}, {"timestamp": "10:00", "price_level": 23550, "event_type": "cascade"}]}}, "ground_truth_cascade_time": 8.0, "session_type": "NY_AM", "expected_difficulty": "moderate", "notes": "Classic expansion cascade, well-documented ground truth"}, {"case_id": "ASIA_2025_07_31", "session_data": {"session_metadata": {"session_type": "NY_AM", "duration_minutes": 120, "date": "2025-08-05"}, "price_data": {"high": 23550, "low": 23450, "range": 100, "session_character": "expansion_consolidation"}, "micro_timing_analysis": {"cascade_events": [{"timestamp": "09:30", "price_level": 23500, "event_type": "open"}, {"timestamp": "09:40", "price_level": 23520, "event_type": "move"}, {"timestamp": "09:50", "price_level": 23480, "event_type": "low"}, {"timestamp": "10:00", "price_level": 23550, "event_type": "cascade"}]}}, "ground_truth_cascade_time": 23.0, "session_type": "ASIA", "expected_difficulty": "hard", "notes": "Asia session low event, HTF coupling test case"}, {"case_id": "SYNTHETIC_LOW_VOLATILITY", "session_data": {"session_metadata": {"session_type": "NY_AM", "duration_minutes": 120, "date": "2025-08-05"}, "price_data": {"high": 23520, "low": 23500, "range": 20, "session_character": "consolidation_dominant"}, "micro_timing_analysis": {"cascade_events": [{"timestamp": "09:30", "price_level": 23510, "event_type": "open"}, {"timestamp": "09:45", "price_level": 23515, "event_type": "move"}, {"timestamp": "10:00", "price_level": 23505, "event_type": "move"}, {"timestamp": "10:15", "price_level": 23520, "event_type": "cascade"}]}}, "ground_truth_cascade_time": 45.0, "session_type": "SYNTHETIC", "expected_difficulty": "easy", "notes": "Low volatility consolidation test"}, {"case_id": "SYNTHETIC_HIGH_VOLATILITY", "session_data": {"session_metadata": {"session_type": "NY_AM", "duration_minutes": 120, "date": "2025-08-05"}, "price_data": {"high": 23600, "low": 23400, "range": 200, "session_character": "expansion_consolidation_final_expansion"}, "micro_timing_analysis": {"cascade_events": [{"timestamp": "09:30", "price_level": 23500, "event_type": "open"}, {"timestamp": "09:35", "price_level": 23450, "event_type": "low"}, {"timestamp": "09:40", "price_level": 23550, "event_type": "high"}, {"timestamp": "09:42", "price_level": 23600, "event_type": "cascade"}]}}, "ground_truth_cascade_time": 12.0, "session_type": "SYNTHETIC", "expected_difficulty": "moderate", "notes": "High volatility expansion test"}], "comparison_results": [{"test_case_id": "NYAM_2025_07_25", "ground_truth": 8.0, "baseline_prediction": {"predicted_time": 2.73724189330789, "confidence": 0.7, "processing_time": 8.296966552734375e-05, "methodology": "mock_baseline_system", "parameters_used": {"mock": true}, "additional_metrics": {"note": "baseline_system_unavailable"}}, "baseline_error": 5.26275810669211, "baseline_accuracy_grade": "moderate", "enhanced_prediction": {"predicted_time": 0, "confidence": 0.1, "processing_time": 0.0035610198974609375, "methodology": "integrated_oracle_system", "parameters_used": {"rg_scaler": {"optimal_scale": 15.0, "density": 0.1, "confidence": 0.5, "classification": "medium"}, "hawkes_engine": {"base_available": false, "enhancement_active": true, "confidence_boost": 0.0, "methodology": "multi_dimensional_hawkes_only"}, "vqe_optimization": false}, "additional_metrics": {"enhancement_active": true, "confidence_boost": 0.0, "domain_constraints_satisfied": true, "processing_breakdown": {"rg_scaling_confidence": 0.5, "hawkes_enhancement_active": true, "vqe_optimization_active": false, "processing_time_seconds": 0.003012, "events_processed": 4, "scaled_events": 3}}}, "enhanced_error": 8.0, "enhanced_accuracy_grade": "moderate", "error_improvement": -2.73724189330789, "confidence_improvement": -0.6, "processing_time_ratio": 42.91954022988506, "performance_maintained": false, "performance_improved": false, "regression_detected": true}, {"test_case_id": "ASIA_2025_07_31", "ground_truth": 23.0, "baseline_prediction": {"predicted_time": 28.55695225085378, "confidence": 0.7, "processing_time": 1.6927719116210938e-05, "methodology": "mock_baseline_system", "parameters_used": {"mock": true}, "additional_metrics": {"note": "baseline_system_unavailable"}}, "baseline_error": 5.556952250853779, "baseline_accuracy_grade": "moderate", "enhanced_prediction": {"predicted_time": 0, "confidence": 0.1, "processing_time": 0.001233816146850586, "methodology": "integrated_oracle_system", "parameters_used": {"rg_scaler": {"optimal_scale": 15.0, "density": 0.1, "confidence": 0.5, "classification": "medium"}, "hawkes_engine": {"base_available": false, "enhancement_active": true, "confidence_boost": 0.0, "methodology": "multi_dimensional_hawkes_only"}, "vqe_optimization": false}, "additional_metrics": {"enhancement_active": true, "confidence_boost": 0.0, "domain_constraints_satisfied": true, "processing_breakdown": {"rg_scaling_confidence": 0.5, "hawkes_enhancement_active": true, "vqe_optimization_active": false, "processing_time_seconds": 0.001044, "events_processed": 4, "scaled_events": 3}}}, "enhanced_error": 23.0, "enhanced_accuracy_grade": "very_poor", "error_improvement": -17.44304774914622, "confidence_improvement": -0.6, "processing_time_ratio": 72.88732394366197, "performance_maintained": false, "performance_improved": false, "regression_detected": true}, {"test_case_id": "SYNTHETIC_LOW_VOLATILITY", "ground_truth": 45.0, "baseline_prediction": {"predicted_time": 48.572200878412495, "confidence": 0.7, "processing_time": 1.2159347534179688e-05, "methodology": "mock_baseline_system", "parameters_used": {"mock": true}, "additional_metrics": {"note": "baseline_system_unavailable"}}, "baseline_error": 3.5722008784124952, "baseline_accuracy_grade": "good", "enhanced_prediction": {"predicted_time": 0, "confidence": 0.1, "processing_time": 0.001007080078125, "methodology": "integrated_oracle_system", "parameters_used": {"rg_scaler": {"optimal_scale": 15.0, "density": 0.1, "confidence": 0.5, "classification": "medium"}, "hawkes_engine": {"base_available": false, "enhancement_active": true, "confidence_boost": 0.0, "methodology": "multi_dimensional_hawkes_only"}, "vqe_optimization": false}, "additional_metrics": {"enhancement_active": true, "confidence_boost": 0.0, "domain_constraints_satisfied": true, "processing_breakdown": {"rg_scaling_confidence": 0.5, "hawkes_enhancement_active": true, "vqe_optimization_active": false, "processing_time_seconds": 0.000837, "events_processed": 4, "scaled_events": 4}}}, "enhanced_error": 45.0, "enhanced_accuracy_grade": "very_poor", "error_improvement": -41.427799121587505, "confidence_improvement": -0.6, "processing_time_ratio": 82.82352941176471, "performance_maintained": false, "performance_improved": false, "regression_detected": true}, {"test_case_id": "SYNTHETIC_HIGH_VOLATILITY", "ground_truth": 12.0, "baseline_prediction": {"predicted_time": 10.4955794817147, "confidence": 0.7, "processing_time": 7.867813110351562e-06, "methodology": "mock_baseline_system", "parameters_used": {"mock": true}, "additional_metrics": {"note": "baseline_system_unavailable"}}, "baseline_error": 1.5044205182853005, "baseline_accuracy_grade": "good", "enhanced_prediction": {"predicted_time": 0, "confidence": 0.1, "processing_time": 0.0011000633239746094, "methodology": "integrated_oracle_system", "parameters_used": {"rg_scaler": {"optimal_scale": 15.0, "density": 0.1, "confidence": 0.5, "classification": "medium"}, "hawkes_engine": {"base_available": false, "enhancement_active": true, "confidence_boost": 0.0, "methodology": "multi_dimensional_hawkes_only"}, "vqe_optimization": false}, "additional_metrics": {"enhancement_active": true, "confidence_boost": 0.0, "domain_constraints_satisfied": true, "processing_breakdown": {"rg_scaling_confidence": 0.5, "hawkes_enhancement_active": true, "vqe_optimization_active": false, "processing_time_seconds": 0.000931, "events_processed": 4, "scaled_events": 1}}}, "enhanced_error": 12.0, "enhanced_accuracy_grade": "poor", "error_improvement": -10.4955794817147, "confidence_improvement": -0.6, "processing_time_ratio": 139.8181818181818, "performance_maintained": false, "performance_improved": false, "regression_detected": true}], "validation_report": {"validation_timestamp": "2025-08-05T13:07:28.215843", "total_test_cases": 4, "baseline_metrics": {"mean_absolute_error": 3.9740829385609215, "median_absolute_error": 4.417479492552303, "max_absolute_error": 5.556952250853779, "success_rate": 0.5}, "enhanced_metrics": {"mean_absolute_error": 22.0, "median_absolute_error": 17.5, "max_absolute_error": 45.0, "success_rate": 0.0}, "performance_maintained_count": 0, "performance_improved_count": 0, "regression_detected_count": 4, "mean_error_improvement": -18.025917061439078, "confidence_improvement": 0, "statistical_significance": {"sample_size": 4, "baseline_error_stats": {"mean": 3.9740829385609215, "std": 1.6145355840881297, "median": 4.417479492552303, "min": 1.5044205182853005, "max": 5.556952250853779}, "enhanced_error_stats": {"mean": 22.0, "std": 14.370107863199914, "median": 17.5, "min": 8.0, "max": 45.0}, "error_improvement_stats": {"mean": -18.025917061439078, "std": 14.477891706439426, "median": -13.96931361543046, "percentage_improved": 0.0}, "statistical_tests": {"error_paired_ttest": {"statistic": -2.1565159372996576, "pvalue": 0.119968713345412, "significant": "False"}, "confidence_paired_ttest": {"statistic": Infinity, "pvalue": 0.0, "significant": "True"}, "error_cohens_d": -1.7629027940167192, "effect_size_interpretation": "large"}}, "migration_recommended": false, "migration_confidence": 0.05, "critical_issues": ["High regression rate: 100.0%", "Mean error degradation: 453.6%", "Success rate drop: 50.0%"], "recommendations": ["❌ Migration not recommended - address critical issues first", "🔧 Fix: High regression rate: 100.0%", "🔧 Fix: Mean error degradation: 453.6%", "🔧 Fix: Success rate drop: 50.0%"]}}