{"validation_metadata": {"timestamp": "2025-08-05T13:07:54.451369", "validator_version": "migration_validator_v1.0", "baseline_system": "HawkesCascadePredictor", "enhanced_system": "ProjectOracle", "baseline_threshold_buffer": 0.1}, "test_cases": [{"case_id": "NYAM_2025_07_25", "session_data": {"session_metadata": {"session_type": "NY_AM", "duration_minutes": 120, "date": "2025-08-05"}, "price_data": {"high": 23550, "low": 23450, "range": 100, "session_character": "expansion_consolidation"}, "micro_timing_analysis": {"cascade_events": [{"timestamp": "09:30", "price_level": 23500, "event_type": "open"}, {"timestamp": "09:40", "price_level": 23520, "event_type": "move"}, {"timestamp": "09:50", "price_level": 23480, "event_type": "low"}, {"timestamp": "10:00", "price_level": 23550, "event_type": "cascade"}]}}, "ground_truth_cascade_time": 8.0, "session_type": "NY_AM", "expected_difficulty": "moderate", "notes": "Classic expansion cascade, well-documented ground truth"}, {"case_id": "ASIA_2025_07_31", "session_data": {"session_metadata": {"session_type": "NY_AM", "duration_minutes": 120, "date": "2025-08-05"}, "price_data": {"high": 23550, "low": 23450, "range": 100, "session_character": "expansion_consolidation"}, "micro_timing_analysis": {"cascade_events": [{"timestamp": "09:30", "price_level": 23500, "event_type": "open"}, {"timestamp": "09:40", "price_level": 23520, "event_type": "move"}, {"timestamp": "09:50", "price_level": 23480, "event_type": "low"}, {"timestamp": "10:00", "price_level": 23550, "event_type": "cascade"}]}}, "ground_truth_cascade_time": 23.0, "session_type": "ASIA", "expected_difficulty": "hard", "notes": "Asia session low event, HTF coupling test case"}, {"case_id": "SYNTHETIC_LOW_VOLATILITY", "session_data": {"session_metadata": {"session_type": "NY_AM", "duration_minutes": 120, "date": "2025-08-05"}, "price_data": {"high": 23520, "low": 23500, "range": 20, "session_character": "consolidation_dominant"}, "micro_timing_analysis": {"cascade_events": [{"timestamp": "09:30", "price_level": 23510, "event_type": "open"}, {"timestamp": "09:45", "price_level": 23515, "event_type": "move"}, {"timestamp": "10:00", "price_level": 23505, "event_type": "move"}, {"timestamp": "10:15", "price_level": 23520, "event_type": "cascade"}]}}, "ground_truth_cascade_time": 45.0, "session_type": "SYNTHETIC", "expected_difficulty": "easy", "notes": "Low volatility consolidation test"}, {"case_id": "SYNTHETIC_HIGH_VOLATILITY", "session_data": {"session_metadata": {"session_type": "NY_AM", "duration_minutes": 120, "date": "2025-08-05"}, "price_data": {"high": 23600, "low": 23400, "range": 200, "session_character": "expansion_consolidation_final_expansion"}, "micro_timing_analysis": {"cascade_events": [{"timestamp": "09:30", "price_level": 23500, "event_type": "open"}, {"timestamp": "09:35", "price_level": 23450, "event_type": "low"}, {"timestamp": "09:40", "price_level": 23550, "event_type": "high"}, {"timestamp": "09:42", "price_level": 23600, "event_type": "cascade"}]}}, "ground_truth_cascade_time": 12.0, "session_type": "SYNTHETIC", "expected_difficulty": "moderate", "notes": "High volatility expansion test"}], "comparison_results": [{"test_case_id": "NYAM_2025_07_25", "ground_truth": 8.0, "baseline_prediction": {"predicted_time": 6.9658816743604355, "confidence": 0.7, "processing_time": 3.0994415283203125e-05, "methodology": "mock_baseline_system", "parameters_used": {"mock": true}, "additional_metrics": {"note": "baseline_system_unavailable"}}, "baseline_error": 1.0341183256395645, "baseline_accuracy_grade": "good", "enhanced_prediction": {"predicted_time": 48.0, "confidence": 0.1, "processing_time": 0.0008919239044189453, "methodology": "integrated_oracle_system", "parameters_used": {"rg_scaler": {"optimal_scale": 15.0, "density": 0.1, "confidence": 0.5, "classification": "medium"}, "hawkes_engine": {"base_available": false, "enhancement_active": true, "confidence_boost": 0.0, "methodology": "multi_dimensional_hawkes_only"}, "vqe_optimization": false}, "additional_metrics": {"enhancement_active": true, "confidence_boost": 0.0, "domain_constraints_satisfied": true, "processing_breakdown": {"rg_scaling_confidence": 0.5, "hawkes_enhancement_active": true, "vqe_optimization_active": false, "processing_time_seconds": 0.000705, "events_processed": 4, "scaled_events": 3}}}, "enhanced_error": 40.0, "enhanced_accuracy_grade": "very_poor", "error_improvement": -38.96588167436043, "confidence_improvement": -0.6, "processing_time_ratio": 28.776923076923076, "performance_maintained": false, "performance_improved": false, "regression_detected": true}, {"test_case_id": "ASIA_2025_07_31", "ground_truth": 23.0, "baseline_prediction": {"predicted_time": 22.21225452661635, "confidence": 0.7, "processing_time": 9.775161743164062e-06, "methodology": "mock_baseline_system", "parameters_used": {"mock": true}, "additional_metrics": {"note": "baseline_system_unavailable"}}, "baseline_error": 0.7877454733836515, "baseline_accuracy_grade": "excellent", "enhanced_prediction": {"predicted_time": 48.0, "confidence": 0.1, "processing_time": 0.0007779598236083984, "methodology": "integrated_oracle_system", "parameters_used": {"rg_scaler": {"optimal_scale": 15.0, "density": 0.1, "confidence": 0.5, "classification": "medium"}, "hawkes_engine": {"base_available": false, "enhancement_active": true, "confidence_boost": 0.0, "methodology": "multi_dimensional_hawkes_only"}, "vqe_optimization": false}, "additional_metrics": {"enhancement_active": true, "confidence_boost": 0.0, "domain_constraints_satisfied": true, "processing_breakdown": {"rg_scaling_confidence": 0.5, "hawkes_enhancement_active": true, "vqe_optimization_active": false, "processing_time_seconds": 0.0006, "events_processed": 4, "scaled_events": 3}}}, "enhanced_error": 25.0, "enhanced_accuracy_grade": "very_poor", "error_improvement": -24.21225452661635, "confidence_improvement": -0.6, "processing_time_ratio": 79.58536585365853, "performance_maintained": false, "performance_improved": false, "regression_detected": true}, {"test_case_id": "SYNTHETIC_LOW_VOLATILITY", "ground_truth": 45.0, "baseline_prediction": {"predicted_time": 46.11451545898747, "confidence": 0.7, "processing_time": 8.821487426757812e-06, "methodology": "mock_baseline_system", "parameters_used": {"mock": true}, "additional_metrics": {"note": "baseline_system_unavailable"}}, "baseline_error": 1.1145154589874693, "baseline_accuracy_grade": "good", "enhanced_prediction": {"predicted_time": 48.0, "confidence": 0.1, "processing_time": 0.0006320476531982422, "methodology": "integrated_oracle_system", "parameters_used": {"rg_scaler": {"optimal_scale": 15.0, "density": 0.1, "confidence": 0.5, "classification": "medium"}, "hawkes_engine": {"base_available": false, "enhancement_active": true, "confidence_boost": 0.0, "methodology": "multi_dimensional_hawkes_only"}, "vqe_optimization": false}, "additional_metrics": {"enhancement_active": true, "confidence_boost": 0.0, "domain_constraints_satisfied": true, "processing_breakdown": {"rg_scaling_confidence": 0.5, "hawkes_enhancement_active": true, "vqe_optimization_active": false, "processing_time_seconds": 0.000463, "events_processed": 4, "scaled_events": 4}}}, "enhanced_error": 3.0, "enhanced_accuracy_grade": "good", "error_improvement": -1.8854845410125307, "confidence_improvement": -0.6, "processing_time_ratio": 71.64864864864865, "performance_maintained": false, "performance_improved": false, "regression_detected": true}, {"test_case_id": "SYNTHETIC_HIGH_VOLATILITY", "ground_truth": 12.0, "baseline_prediction": {"predicted_time": 13.910767180029099, "confidence": 0.7, "processing_time": 7.152557373046875e-06, "methodology": "mock_baseline_system", "parameters_used": {"mock": true}, "additional_metrics": {"note": "baseline_system_unavailable"}}, "baseline_error": 1.910767180029099, "baseline_accuracy_grade": "good", "enhanced_prediction": {"predicted_time": 48.0, "confidence": 0.1, "processing_time": 0.0007259845733642578, "methodology": "integrated_oracle_system", "parameters_used": {"rg_scaler": {"optimal_scale": 15.0, "density": 0.1, "confidence": 0.5, "classification": "medium"}, "hawkes_engine": {"base_available": false, "enhancement_active": true, "confidence_boost": 0.0, "methodology": "multi_dimensional_hawkes_only"}, "vqe_optimization": false}, "additional_metrics": {"enhancement_active": true, "confidence_boost": 0.0, "domain_constraints_satisfied": true, "processing_breakdown": {"rg_scaling_confidence": 0.5, "hawkes_enhancement_active": true, "vqe_optimization_active": false, "processing_time_seconds": 0.000559, "events_processed": 4, "scaled_events": 1}}}, "enhanced_error": 36.0, "enhanced_accuracy_grade": "very_poor", "error_improvement": -34.0892328199709, "confidence_improvement": -0.6, "processing_time_ratio": 101.5, "performance_maintained": false, "performance_improved": false, "regression_detected": true}], "validation_report": {"validation_timestamp": "2025-08-05T13:07:54.451227", "total_test_cases": 4, "baseline_metrics": {"mean_absolute_error": 1.211786609509946, "median_absolute_error": 1.074316892313517, "max_absolute_error": 1.910767180029099, "success_rate": 1.0}, "enhanced_metrics": {"mean_absolute_error": 26.0, "median_absolute_error": 30.5, "max_absolute_error": 40.0, "success_rate": 0.25}, "performance_maintained_count": 0, "performance_improved_count": 0, "regression_detected_count": 4, "mean_error_improvement": -24.788213390490053, "confidence_improvement": 0, "statistical_significance": {"sample_size": 4, "baseline_error_stats": {"mean": 1.211786609509946, "std": 0.4211331199597023, "median": 1.074316892313517, "min": 0.7877454733836515, "max": 1.910767180029099}, "enhanced_error_stats": {"mean": 26.0, "std": 14.370107863199914, "median": 30.5, "min": 3.0, "max": 40.0}, "error_improvement_stats": {"mean": -24.788213390490053, "std": 14.251157477206974, "median": -29.150743673293626, "percentage_improved": 0.0}, "statistical_tests": {"error_paired_ttest": {"statistic": -3.0126987993681547, "pvalue": 0.05708913727631266, "significant": "False"}, "confidence_paired_ttest": {"statistic": Infinity, "pvalue": 0.0, "significant": "True"}, "error_cohens_d": -2.4384495711046243, "effect_size_interpretation": "large"}}, "migration_recommended": false, "migration_confidence": 0.05, "critical_issues": ["High regression rate: 100.0%", "Mean error degradation: 2045.6%", "Success rate drop: 75.0%"], "recommendations": ["❌ Migration not recommended - address critical issues first", "🔧 Fix: High regression rate: 100.0%", "🔧 Fix: Mean error degradation: 2045.6%", "🔧 Fix: Success rate drop: 75.0%"]}}