"""
Echo Detector Module - Phase 1 Implementation
Detects when Oracle is predicting its own predictions vs market reality

USAGE: Add to any prediction pipeline to detect metacognitive loops
"""

from datetime import datetime
import logging

class EchoDetector:
    """Detects temporal echoes in Oracle predictions"""
    
    def __init__(self, echo_threshold=20):
        self.echo_threshold = echo_threshold  # minutes
        self.prediction_history = []
        self.logger = logging.getLogger(__name__)
    
    def compute_echo_strength(self, virgin_time, contaminated_time, actual_time):
        """
        Calculate echo strength between predictions
        
        Args:
            virgin_time: Original prediction time (string HH:MM:SS)
            contaminated_time: Updated prediction time (string HH:MM:SS)
            actual_time: Actual event time (string HH:MM:SS)
            
        Returns:
            float: Echo strength in minutes (positive = echo detected)
        """
        
        try:
            virgin_dt = datetime.strptime(virgin_time, '%H:%M:%S')
            contaminated_dt = datetime.strptime(contaminated_time, '%H:%M:%S')
            actual_dt = datetime.strptime(actual_time, '%H:%M:%S')
            
            # Calculate errors
            virgin_error = abs((virgin_dt - actual_dt).total_seconds() / 60)
            contaminated_error = abs((contaminated_dt - actual_dt).total_seconds() / 60)
            
            # Echo strength = how much worse contaminated is vs virgin
            echo_strength = contaminated_error - virgin_error
            
            return echo_strength
            
        except Exception as e:
            self.logger.error(f"Echo calculation error: {e}")
            return 0.0
    
    def detect_metacognition(self, virgin_prediction, contaminated_prediction, actual_event=None):
        """
        Main detection function
        
        Returns:
            dict: Detection results with recommendations
        """
        
        if actual_event:
            echo_strength = self.compute_echo_strength(
                virgin_prediction, contaminated_prediction, actual_event
            )
        else:
            # Without actual event, use divergence as proxy
            virgin_dt = datetime.strptime(virgin_prediction, '%H:%M:%S')
            contaminated_dt = datetime.strptime(contaminated_prediction, '%H:%M:%S')
            echo_strength = abs((contaminated_dt - virgin_dt).total_seconds() / 60)
        
        # Store for trend analysis
        self.prediction_history.append({
            'virgin': virgin_prediction,
            'contaminated': contaminated_prediction,
            'echo_strength': echo_strength,
            'timestamp': datetime.now().isoformat()
        })
        
        # Detection logic
        metacognition_detected = echo_strength > self.echo_threshold
        
        result = {
            'echo_strength': echo_strength,
            'metacognition_detected': metacognition_detected,
            'recommendation': self.get_recommendation(echo_strength),
            'confidence': self.calculate_confidence(echo_strength)
        }
        
        if metacognition_detected:
            self.logger.warning(f"🚨 METACOGNITION DETECTED: Echo strength {echo_strength:.1f} minutes")
        
        return result
    
    def get_recommendation(self, echo_strength):
        """Get action recommendation based on echo strength"""
        
        if echo_strength > 40:
            return "USE_VIRGIN_IMMEDIATELY"
        elif echo_strength > 20:
            return "USE_VIRGIN_WITH_MONITORING"
        elif echo_strength > 10:
            return "MONITOR_CLOSELY"
        else:
            return "USE_CONTAMINATED_NORMAL"
    
    def calculate_confidence(self, echo_strength):
        """Calculate confidence in metacognition detection"""
        
        # Simple confidence function based on echo strength
        if echo_strength > 40:
            return 0.95
        elif echo_strength > 20:
            return 0.80
        elif echo_strength > 10:
            return 0.60
        else:
            return 0.30

# Simple usage function
def quick_echo_check(virgin_time, contaminated_time, actual_time=None):
    """Quick echo detection for immediate use"""
    
    detector = EchoDetector()
    result = detector.detect_metacognition(virgin_time, contaminated_time, actual_time)
    
    print(f"🔍 Echo Detection Results:")
    print(f"   Echo Strength: {result['echo_strength']:.1f} minutes")
    print(f"   Metacognition: {'🚨 DETECTED' if result['metacognition_detected'] else '✅ Normal'}")
    print(f"   Recommendation: {result['recommendation']}")
    print(f"   Confidence: {result['confidence']:.1%}")
    
    return result

if __name__ == "__main__":
    # Test with your known data
    print("🧪 Testing Echo Detector with August 5 data:")
    
    # Your actual case
    result = quick_echo_check(
        virgin_time="10:18:00",        # Original prediction  
        contaminated_time="10:58:00",   # Updated prediction
        actual_time="10:18:00"          # Actual 3-day FPFVG redelivery
    )
    
    print(f"\n💡 Action: {result['recommendation']}")
    if result['metacognition_detected']:
        print("🎯 Oracle should use VIRGIN prediction for better accuracy!")