#!/usr/bin/env python3
"""
Test Grammar Pattern Detection Fix
==================================

Validates that the grammar pattern detection fix is working correctly
by comparing before/after behavior and testing with real Grammar Bridge data.
"""

import sys
import json
from pathlib import Path

# Add current directory to path
sys.path.append('.')

from compartments.predict import PredictCompartment

def test_grammar_pattern_detection():
    """Test the fixed grammar pattern detection system"""
    
    print("🔍 TESTING GRAMMAR PATTERN DETECTION FIX")
    print("=" * 60)
    
    # Initialize predict compartment
    predict_comp = PredictCompartment()
    
    # Load real cascade events from Grammar Bridge
    cascade_events_file = Path("grammar_bridge/cascade_events.json")
    if not cascade_events_file.exists():
        print("❌ ERROR: Grammar Bridge cascade events not found")
        return False
    
    with cascade_events_file.open("r") as f:
        data = json.load(f)
    
    all_events = data['unified_cascade_events']
    
    print(f"📊 Testing with {len(all_events)} total cascade events")
    
    # Test different sample sizes
    test_cases = [
        {"size": 25, "description": "Small sample"},
        {"size": 50, "description": "Medium sample"}, 
        {"size": 100, "description": "Large sample"},
        {"size": 200, "description": "Extra large sample"}
    ]
    
    results = []
    
    for test_case in test_cases:
        size = test_case["size"]
        if len(all_events) >= size:
            sample_events = all_events[:size]
            result = predict_comp._analyze_grammar_patterns(sample_events)
            
            results.append({
                "size": size,
                "completion_probability": result["completion_probability"],
                "pattern_type": result["pattern_type"],
                "high_value_events": result.get("high_value_events", 0),
                "unique_events": result["unique_events"]
            })
            
            print(f"\n✅ {test_case['description']} ({size} events):")
            print(f"   Completion Probability: {result['completion_probability']:.3f}")
            print(f"   Pattern Type: {result['pattern_type']}")
            print(f"   High Value Events: {result.get('high_value_events', 0)}")
            print(f"   Unique Event Types: {result['unique_events']}")
            
            # Check deterministic mode trigger
            if result['completion_probability'] > 0.85:
                print(f"   🎯 TRIGGERS DETERMINISTIC MODE (>{0.85})")
            else:
                print(f"   📊 Uses probabilistic mode (<={0.85})")
    
    # Validation checks
    print(f"\n🔬 VALIDATION RESULTS:")
    print(f"=" * 40)
    
    all_working = True
    
    # Check 1: All tests should detect patterns (completion > 0)
    zero_completion = [r for r in results if r["completion_probability"] == 0.0]
    if zero_completion:
        print(f"❌ FAILED: {len(zero_completion)} tests had zero completion probability")
        all_working = False
    else:
        print(f"✅ PASSED: All tests detected patterns (completion > 0)")
    
    # Check 2: Should detect meaningful patterns (not just 'unknown')
    unknown_patterns = [r for r in results if r["pattern_type"] == "unknown"]
    if unknown_patterns:
        print(f"⚠️ WARNING: {len(unknown_patterns)} tests returned 'unknown' pattern")
    else:
        print(f"✅ PASSED: All tests identified specific patterns")
    
    # Check 3: Should detect high-value events
    no_high_value = [r for r in results if r["high_value_events"] == 0]
    if no_high_value:
        print(f"⚠️ WARNING: {len(no_high_value)} tests found no high-value events")
    else:
        print(f"✅ PASSED: All tests detected high-value events")
    
    # Check 4: Completion probability should be reasonable (not always 1.0)
    all_perfect = [r for r in results if r["completion_probability"] == 1.0]
    if len(all_perfect) == len(results):
        print(f"⚠️ NOTE: All tests had perfect completion (may indicate overly permissive patterns)")
    else:
        print(f"✅ PASSED: Varied completion probabilities detected")
    
    print(f"\n📈 SUMMARY STATISTICS:")
    print(f"   Average Completion: {sum(r['completion_probability'] for r in results) / len(results):.3f}")
    print(f"   Average High-Value Events: {sum(r['high_value_events'] for r in results) / len(results):.1f}")
    print(f"   Pattern Types Found: {len(set(r['pattern_type'] for r in results))}")
    
    # Final assessment
    if all_working:
        print(f"\n🎉 GRAMMAR PATTERN DETECTION FIX: SUCCESS")
        print(f"   ✅ Detects real patterns from Grammar Bridge events")
        print(f"   ✅ Provides meaningful completion probabilities") 
        print(f"   ✅ Identifies specific pattern types")
        print(f"   ✅ Can trigger deterministic vs probabilistic modes")
        print(f"   ✅ Handles various sample sizes robustly")
        return True
    else:
        print(f"\n❌ GRAMMAR PATTERN DETECTION FIX: ISSUES DETECTED")
        return False

if __name__ == "__main__":
    success = test_grammar_pattern_detection()
    sys.exit(0 if success else 1)
