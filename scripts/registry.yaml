# Canonical entrypoints and allowed top-level scripts
# Any new top-level *.py at repo root must be added here or placed under approved module dirs

approved_roots:
  - production_simple.py
  - oracle.py
  - oracle_subprocess_runner.py
  - production_oracle.py
  - three_oracle_architecture.py
  - enhanced_oracle_integration.py
  - cascade_prediction_api.py
  - audit_data_integrity.py
  - run_compartments.py
  - generate_overrides_template.py
  - seed_label_overrides.py
  - generate_overrides_review_csv.py

# Top-level directories considered module roots
approved_dirs:
  - core_predictor
  - data_pipeline
  - optimization_shell
  - validation
  - processors
  - schema
  - storage
  - documentation
  - deployment
  - enhanced_sessions
  - enhanced_sessions_batch
  - rg_scaler
  - self_optimization
  - data
  - scripts
  - compartments

