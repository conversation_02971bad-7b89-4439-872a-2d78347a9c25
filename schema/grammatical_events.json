{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Grammatical Event Classification Reference", "description": "Enhanced 16-symbol alphabet for market event classification with liquidity context", "definitions": {"core_grammar_symbols": {"description": "Core 9 grammatical symbols validated for Type-2 context-free patterns", "enum": ["OPEN", "CONSOLIDATION", "EXPANSION", "EXPANSION_HIGH", "EXPANSION_LOW", "RETRACEMENT", "REDELIVERY", "FPFVG_FORMATION", "REVERSAL"]}, "liquidity_enhanced_symbols": {"description": "Additional 7 symbols for liquidity context enhancement", "enum": ["REBALANCE", "INTERACTION", "TAKEOUT", "LIQUIDITY_GRAB", "LIQUIDITY_SWEEP", "VOLUME_SPIKE", "CLOSE"]}}, "event_definitions": {"OPEN": {"description": "Session opening price establishment", "pattern_context": "Session initiation, often followed by CONSOLIDATION", "liquidity_significance": "Establishes initial liquidity reference", "typical_duration": "1-2 minutes", "cascade_potential": "low"}, "CONSOLIDATION": {"description": "Price range-bound movement with defined high/low boundaries", "pattern_context": "Often precedes EXPANSION, part of accumulation phase", "liquidity_significance": "Builds liquidity at range boundaries", "typical_duration": "5-30 minutes", "cascade_potential": "medium_when_broken"}, "EXPANSION": {"description": "Directional price movement breaking consolidation boundaries", "pattern_context": "Primary trend movement, often triggers REDELIVERY events", "liquidity_significance": "Consumes liquidity, creates new reference levels", "typical_duration": "2-15 minutes", "cascade_potential": "high"}, "EXPANSION_HIGH": {"description": "Expansion movement creating session/local high", "pattern_context": "Often followed by REVERSAL or RETRACEMENT", "liquidity_significance": "Creates resistance level, potential reversal zone", "typical_duration": "1-5 minutes", "cascade_potential": "very_high_if_violated"}, "EXPANSION_LOW": {"description": "Expansion movement creating session/local low", "pattern_context": "Often followed by REVERSAL or continuation", "liquidity_significance": "Creates support level, potential reversal zone", "typical_duration": "1-5 minutes", "cascade_potential": "very_high_if_violated"}, "RETRACEMENT": {"description": "Partial price reversal after expansion movement", "pattern_context": "Temporary counter-trend, often precedes continuation", "liquidity_significance": "Tests previous levels, creates entry opportunities", "typical_duration": "3-10 minutes", "cascade_potential": "medium"}, "REDELIVERY": {"description": "Price returning to previously established FPFVG or significant level", "pattern_context": "Critical pattern completion event, high cascade probability", "liquidity_significance": "Tests institutional levels, triggers algorithmic responses", "typical_duration": "1-3 minutes", "cascade_potential": "very_high"}, "FPFVG_FORMATION": {"description": "Creation of Fair Value Gap through rapid price movement", "pattern_context": "Establishes future target levels, begins FPFVG lifecycle", "liquidity_significance": "Creates inefficiency requiring future rebalancing", "typical_duration": "1-2 minutes", "cascade_potential": "high_future_reference"}, "REVERSAL": {"description": "Significant directional change, often at key levels", "pattern_context": "Pattern termination/initiation, creates new trend direction", "liquidity_significance": "Major liquidity shift, invalidates previous structure", "typical_duration": "1-5 minutes", "cascade_potential": "maximum"}, "REBALANCE": {"description": "Partial interaction with FPFVG without full redelivery", "pattern_context": "Intermediate FPFVG interaction, maintains structure validity", "liquidity_significance": "Partial liquidity satisfaction, keeps level active", "typical_duration": "1-2 minutes", "cascade_potential": "medium"}, "INTERACTION": {"description": "Any price engagement with significant liquidity level", "pattern_context": "Broad category for level testing without clear outcome", "liquidity_significance": "Level acknowledgment, may or may not trigger response", "typical_duration": "1-3 minutes", "cascade_potential": "variable"}, "TAKEOUT": {"description": "Complete violation/clearing of significant liquidity level", "pattern_context": "Aggressive level breach, often triggers cascades", "liquidity_significance": "Complete liquidity consumption, level invalidation", "typical_duration": "1-2 minutes", "cascade_potential": "maximum"}, "LIQUIDITY_GRAB": {"description": "Sharp move to collect stops/liquidity then immediate reversal", "pattern_context": "False breakout pattern, often precedes true directional move", "liquidity_significance": "Liquidity collection mechanism, clears weak positions", "typical_duration": "1-3 minutes", "cascade_potential": "high_in_opposite_direction"}, "LIQUIDITY_SWEEP": {"description": "Sustained directional movement clearing multiple liquidity levels", "pattern_context": "Strong trend component, cascading level violations", "liquidity_significance": "Mass liquidity consumption, momentum continuation", "typical_duration": "5-20 minutes", "cascade_potential": "maximum"}, "VOLUME_SPIKE": {"description": "Significant increase in trading volume at price level", "pattern_context": "Institutional activity marker, often precedes major moves", "liquidity_significance": "High participation, validates price level significance", "typical_duration": "1-5 minutes", "cascade_potential": "high_with_directional_context"}, "CLOSE": {"description": "Session closing price establishment", "pattern_context": "Session termination, establishes closing reference", "liquidity_significance": "Final session liquidity settlement", "typical_duration": "1-2 minutes", "cascade_potential": "low_immediate"}}, "pattern_relationships": {"high_probability_sequences": [["CONSOLIDATION", "EXPANSION", "REDELIVERY"], ["FPFVG_FORMATION", "REBALANCE", "REDELIVERY"], ["EXPANSION_HIGH", "RETRACEMENT", "REDELIVERY"], ["LIQUIDITY_GRAB", "REVERSAL", "EXPANSION"], ["REDELIVERY", "EXPANSION", "TAKEOUT"]], "cascade_trigger_patterns": [["REDELIVERY", "TAKEOUT"], ["EXPANSION_HIGH", "TAKEOUT"], ["LIQUIDITY_SWEEP", "VOLUME_SPIKE"], ["REVERSAL", "LIQUIDITY_SWEEP"]], "completion_patterns": [{"incomplete": ["FPFVG_FORMATION"], "expects": ["REBALANCE", "REDELIVERY"], "probability": 0.87, "time_window": "5-45 minutes"}, {"incomplete": ["CONSOLIDATION", "EXPANSION"], "expects": ["RETRACEMENT", "REDELIVERY"], "probability": 0.82, "time_window": "3-15 minutes"}, {"incomplete": ["LIQUIDITY_GRAB"], "expects": ["REVERSAL", "EXPANSION"], "probability": 0.79, "time_window": "1-8 minutes"}]}, "liquidity_context_modifiers": {"cross_session_influence": {"none": {"multiplier": 1.0, "description": "Native session event"}, "immediate": {"multiplier": 1.2, "description": "Same-day cross-session reference"}, "previous_day": {"multiplier": 1.4, "description": "Previous day reference"}, "historical": {"multiplier": 1.6, "description": "Multi-day historical reference"}}, "magnitude_scaling": {"minimal": {"points_range": "0.5-2.0", "cascade_multiplier": 0.8}, "low": {"points_range": "2.0-5.0", "cascade_multiplier": 0.9}, "medium": {"points_range": "5.0-15.0", "cascade_multiplier": 1.0}, "high": {"points_range": "15.0-40.0", "cascade_multiplier": 1.3}, "extreme": {"points_range": "40.0+", "cascade_multiplier": 1.6}}, "fpfvg_interaction_types": {"formation": {"cascade_probability": 0.25, "description": "FPFVG creation"}, "rebalance": {"cascade_probability": 0.45, "description": "Partial fill"}, "redelivery": {"cascade_probability": 0.87, "description": "Complete interaction"}, "takeout": {"cascade_probability": 0.94, "description": "Level violation"}}}}