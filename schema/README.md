# Project Oracle Dual-Layer Architecture Schema

## Overview

The dual-layer architecture preserves ALL existing liquidity intelligence while adding grammatical event classification for enhanced pattern recognition and cascade prediction.

## Architecture Principle

**ZERO INFORMATION LOSS**: All existing FPFVG analysis, contamination metrics, and liquidity intelligence is preserved in Layer 1, while Layer 2 adds grammatical enhancement.

## Schema Files

### `dual_layer_schema.json`
Complete JSON schema defining the dual-layer structure:
- **Layer 1**: Preserves existing `level1_json` structure
- **Layer 2**: Adds `grammatical_intelligence` with event classification

### `grammatical_events.json`
Reference documentation for the 16-symbol grammatical alphabet:
- **Core Grammar (9 symbols)**: OPEN, CONSOLIDATION, EXPANSION, etc.
- **Liquidity Enhanced (7 symbols)**: REBALANCE, TAKEOUT, VOLUME_SPIKE, etc.

## Key Benefits

### Enhanced Prediction Accuracy
```
Basic Pattern: FPFVG → REDELIVERY (87% cascade probability)
Enhanced: FPFVG_FORMATION → REDELIVERY[cross_session] (92% cascade probability)
```

### Context-Aware Analysis
- **Native Session Events**: Standard probability multipliers
- **Cross-Session References**: 1.2x-1.6x probability enhancement
- **Historical References**: Maximum cascade potential

### Backward Compatibility
- Existing systems can continue using Layer 1 data
- New systems leverage both layers for enhanced intelligence
- Migration path preserves all current functionality

## Event Classification System

### Core Grammar Symbols (Type-2 Context-Free)
```
OPEN → Session initiation
CONSOLIDATION → Range-bound accumulation
EXPANSION → Directional breakout
REDELIVERY → Pattern completion (87% cascade rate)
REVERSAL → Trend change (maximum cascade potential)
```

### Liquidity Enhancement Symbols
```
LIQUIDITY_SWEEP → Mass liquidity consumption
TAKEOUT → Level violation (94% cascade rate)
VOLUME_SPIKE → Institutional activity marker
REBALANCE → Partial FPFVG interaction
```

## Pattern Recognition

### High-Probability Sequences
1. `CONSOLIDATION → EXPANSION → REDELIVERY` (82% completion)
2. `FPFVG_FORMATION → REBALANCE → REDELIVERY` (87% completion)
3. `LIQUIDITY_GRAB → REVERSAL → EXPANSION` (79% completion)

### Cascade Trigger Patterns
1. `REDELIVERY → TAKEOUT` (94% cascade probability)
2. `EXPANSION_HIGH → TAKEOUT` (91% cascade probability)
3. `REVERSAL → LIQUIDITY_SWEEP` (89% cascade probability)

## Implementation Guidelines

### For Existing Systems
- Continue using `level1_json` data unchanged
- All FPFVG analysis, contamination metrics preserved
- Zero breaking changes to current workflows

### For Enhanced Systems
- Access both layers for maximum intelligence
- Use grammatical patterns for prediction enhancement
- Leverage context modifiers for probability scaling

### Processing Requirements
1. **Preserve Layer 1**: All existing data must be maintained exactly
2. **Enhance Layer 2**: Add grammatical classification on top
3. **Validate Compatibility**: Ensure backward compatibility maintained
4. **Pattern Recognition**: Extract event sequences for analysis

## Usage Examples

### Legacy System Access
```python
session_data = load_dual_layer_file()
fpfvg_data = session_data["level1_json"]["session_fpfvg"]
# Existing code continues to work unchanged
```

### Enhanced System Access
```python
session_data = load_dual_layer_file()
events = session_data["grammatical_intelligence"]["event_classification"]
patterns = session_data["grammatical_intelligence"]["pattern_analysis"]
# New grammatical intelligence available
```

## Validation Requirements

- **Schema Compliance**: All files must validate against dual_layer_schema.json
- **Layer 1 Integrity**: Existing data structure must be preserved exactly
- **Event Consistency**: Grammatical events must align with Layer 1 data
- **Pattern Validity**: Event sequences must follow Type-2 grammar rules

## Migration Strategy

1. **Phase 1**: Convert existing Lvl-1 files to dual-layer format
2. **Phase 2**: Validate grammatical classification accuracy
3. **Phase 3**: Integrate with existing prediction systems
4. **Phase 4**: Deploy enhanced pattern recognition

This architecture enables the revolutionary shift from statistical to grammatical market analysis while preserving all existing intelligence and maintaining full backward compatibility.