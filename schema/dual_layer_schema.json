{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Project Oracle Dual-Layer Architecture Schema", "description": "Enhanced schema combining existing liquidity intelligence with grammatical event classification", "type": "object", "required": ["level1_json", "grammatical_intelligence"], "properties": {"level1_json": {"description": "LAYER 1: Preserve ALL existing intelligence - zero information loss", "type": "object", "required": ["session_metadata", "session_fpfvg", "session_liquidity_events", "contamination_analysis"], "properties": {"session_metadata": {"type": "object", "properties": {"session_type": {"type": "string", "enum": ["asia", "midnight", "london", "premarket", "ny_am", "lunch", "ny_pm"]}, "session_date": {"type": "string", "format": "date"}, "session_start": {"type": "string", "format": "time"}, "session_end": {"type": "string", "format": "time"}, "session_duration": {"type": "number"}, "data_completeness": {"type": "string", "enum": ["complete_session", "partial_session"]}, "session_status": {"type": "string", "enum": ["completed", "in_progress"]}, "timezone": {"type": "string"}}}, "session_fpfvg": {"type": "object", "properties": {"fpfvg_present": {"type": "boolean"}, "fpfvg_formation": {"type": "object", "properties": {"formation_time": {"type": "string", "format": "time"}, "premium_high": {"type": "number"}, "discount_low": {"type": "number"}, "gap_size": {"type": "number"}, "interactions": {"type": "array", "items": {"type": "object", "properties": {"interaction_time": {"type": "string", "format": "time"}, "interaction_type": {"type": "string", "enum": ["redelivery", "rebalance", "takeout", "formation"]}, "price_level": {"type": "number"}, "interaction_context": {"type": "string"}}}}}}}}, "session_liquidity_events": {"type": "array", "items": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "time"}, "event_type": {"type": "string", "enum": ["fpfvg_formation", "redelivery", "rebalance", "takeout", "sweep"]}, "liquidity_type": {"type": "string", "enum": ["native_session", "cross_session", "historical"]}, "target_level": {"type": "string"}, "magnitude": {"type": "string", "enum": ["minimal", "very_low", "low", "medium", "high", "extreme"]}, "context": {"type": "string"}}}}, "price_movements": {"type": "array", "items": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "time"}, "price_level": {"type": "number"}, "movement_type": {"type": "string"}, "contamination_risk": {"type": "boolean"}}}}, "energy_state": {"type": "object", "properties": {"energy_density": {"type": "number"}, "total_accumulated": {"type": "number"}, "energy_rate": {"type": "number"}, "energy_source": {"type": "string"}, "session_duration": {"type": "number"}, "expansion_phases": {"type": "number"}, "retracement_phases": {"type": "number"}, "consolidation_phases": {"type": "number"}, "phase_transitions": {"type": "number"}}}, "contamination_analysis": {"type": "object", "properties": {"htf_contamination": {"type": "object", "properties": {"htf_carryover_strength": {"type": "number", "minimum": 0, "maximum": 1}, "cross_session_inheritance": {"type": "number", "minimum": 0, "maximum": 1}}}, "cross_session_inheritance": {"type": "object"}}}}}, "grammatical_intelligence": {"description": "LAYER 2: ADD grammatical classification and pattern recognition", "type": "object", "required": ["event_classification", "event_sequences", "pattern_analysis"], "properties": {"event_classification": {"type": "array", "description": "Market events classified into grammatical symbols", "items": {"type": "object", "required": ["event_type", "timestamp", "price", "session_time_minutes"], "properties": {"event_type": {"type": "string", "enum": ["OPEN", "CONSOLIDATION", "EXPANSION", "EXPANSION_HIGH", "EXPANSION_LOW", "RETRACEMENT", "REDELIVERY", "REBALANCE", "FPFVG_FORMATION", "INTERACTION", "REVERSAL", "TAKEOUT", "LIQUIDITY_GRAB", "LIQUIDITY_SWEEP", "VOLUME_SPIKE", "CLOSE"]}, "timestamp": {"type": "string", "format": "time"}, "price": {"type": "number"}, "session_time_minutes": {"type": "number"}, "magnitude": {"type": "number", "minimum": 0}, "liquidity_context": {"type": "object", "properties": {"source_reference": {"type": "string"}, "interaction_type": {"type": "string"}, "cross_session_influence": {"type": "string", "enum": ["none", "immediate", "previous_day", "historical"]}, "magnitude": {"type": "string", "enum": ["minimal", "low", "medium", "high", "extreme"]}, "fpfvg_interaction": {"type": "string"}, "volume": {"type": "number"}, "orderflow_direction": {"type": "string", "enum": ["bullish", "bearish", "neutral"]}, "liquidity_level": {"type": "string", "enum": ["thin", "normal", "thick"]}}}, "pattern_context": {"type": "object", "properties": {"preceding_events": {"type": "array", "items": {"type": "string"}}, "completion_probability": {"type": "number", "minimum": 0, "maximum": 1}, "expected_continuation": {"type": "string"}, "pattern_significance": {"type": "number", "minimum": 0, "maximum": 1}, "cascade_trigger_potential": {"type": "number", "minimum": 0, "maximum": 1}}}}}}, "event_sequences": {"type": "array", "description": "Sequences of events forming grammatical patterns", "items": {"type": "object", "properties": {"sequence_id": {"type": "string"}, "events": {"type": "array", "items": {"type": "string"}}, "pattern_type": {"type": "string", "enum": ["Type-0", "Type-1", "Type-2", "Type-3"]}, "completion_status": {"type": "string", "enum": ["incomplete", "partial", "complete"]}, "completion_probability": {"type": "number", "minimum": 0, "maximum": 1}, "time_window": {"type": "object", "properties": {"start_time": {"type": "string", "format": "time"}, "end_time": {"type": "string", "format": "time"}, "duration_minutes": {"type": "number"}}}}}}, "pattern_analysis": {"type": "object", "description": "High-level pattern recognition and cascade prediction", "properties": {"active_patterns": {"type": "array", "items": {"type": "object", "properties": {"pattern_name": {"type": "string"}, "pattern_type": {"type": "string"}, "completion_percentage": {"type": "number", "minimum": 0, "maximum": 100}, "expected_completion": {"type": "string"}, "cascade_probability": {"type": "number", "minimum": 0, "maximum": 1}}}}, "completion_expectations": {"type": "array", "items": {"type": "object", "properties": {"expected_event": {"type": "string"}, "probability": {"type": "number", "minimum": 0, "maximum": 1}, "time_window": {"type": "string"}, "conditions": {"type": "array", "items": {"type": "string"}}}}}, "cascade_analysis": {"type": "object", "properties": {"cascade_probability": {"type": "number", "minimum": 0, "maximum": 1}, "cascade_type": {"type": "string", "enum": ["primer", "micro", "standard", "major", "macro"]}, "trigger_events": {"type": "array", "items": {"type": "string"}}, "timing_prediction": {"type": "string"}, "magnitude_estimate": {"type": "number"}}}}}}}, "processing_metadata": {"type": "object", "properties": {"schema_version": {"type": "string", "default": "dual_layer_v1.0"}, "processing_timestamp": {"type": "string", "format": "date-time"}, "layer1_preserved": {"type": "boolean", "default": true}, "grammatical_enhancement": {"type": "boolean", "default": true}, "validation_status": {"type": "string", "enum": ["pending", "validated", "failed"]}, "compatibility": {"type": "object", "properties": {"legacy_systems": {"type": "boolean"}, "grammatical_parser": {"type": "boolean"}, "prediction_engine": {"type": "boolean"}}}}}}}