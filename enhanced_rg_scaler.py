"""
Enhanced RG Scaler - High-Density Micro-Event Support
=====================================================

Extends the production RG Scaler with high-density mode optimized for 
micro-event streams (15-20 events/hour). Provides 1-minute bin resolution
and enhanced density-adaptive scaling for optimal Fisher Information input.

Key Enhancements:
- High-density mode with 1-minute bins
- Adaptive bin sizing based on event density  
- Enhanced scaling for micro-event significance weighting
- Direct integration with Fisher Information Monitor

Mathematical Foundation: s(d) = 15 - 5*log₁₀(d) with density-adaptive refinements
"""

import numpy as np
import logging
from typing import Tuple, Optional, List, Dict, Any, Union
from dataclasses import dataclass
from datetime import datetime, timedelta

# Import base RG Scaler
import sys
import os
sys.path.append(os.path.dirname(__file__))
from core_predictor.rg_scaler_production import RGScaler, RGScalingResult

@dataclass
class EnhancedRGScalingResult(RGScalingResult):
    """Enhanced RG scaling result with micro-event support"""
    bin_size_minutes: float
    significance_weighted: bool
    micro_event_count: int
    density_mode: str
    crystallization_readiness: float  # 0.0-1.0 readiness for Fisher analysis

class EnhancedRGScaler:
    """
    Enhanced RG Scaler with High-Density Micro-Event Support
    
    Extends the proven RG scaling approach with optimizations for dense
    micro-event streams from the enhanced extraction pipeline.
    
    Key Features:
    - Adaptive bin sizing (1-15 minutes based on density)
    - Significance-weighted event counting
    - Direct Fisher Information Monitor integration
    - Enhanced crystallization detection support
    """
    
    def __init__(self, density_mode: str = "adaptive", significance_weighting: bool = True):
        """
        Initialize Enhanced RG Scaler
        
        Args:
            density_mode: Density handling mode - "adaptive", "high_density", "sparse"
            significance_weighting: Weight events by significance scores
        """
        self.density_mode = density_mode
        self.significance_weighting = significance_weighting
        
        # Base RG Scaler for core functionality
        self.base_scaler = RGScaler()
        
        # Enhanced density configuration
        self.density_config = {
            'high_density': {
                'bin_size_minutes': 1.0,           # 1-minute bins
                'events_per_hour_threshold': 15.0,
                'scaling_adjustment': 0.8,         # Slightly reduce scale for density
                'fisher_optimization': True
            },
            'sparse': {
                'bin_size_minutes': 5.0,           # 5-minute bins
                'events_per_hour_threshold': 5.0,
                'scaling_adjustment': 1.0,         # Original scaling
                'fisher_optimization': False
            },
            'adaptive': {
                'bin_size_minutes': 2.0,           # 2-minute bins (compromise)
                'events_per_hour_threshold': 10.0,
                'scaling_adjustment': 0.9,         # Slight density adjustment
                'fisher_optimization': True
            }
        }
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"🚀 ENHANCED RG SCALER: Initialized for {density_mode} mode")
        self.logger.info(f"   Bin Size: {self.density_config[density_mode]['bin_size_minutes']} minutes")
        self.logger.info(f"   Significance Weighting: {significance_weighting}")
    
    def transform_micro_events(self, micro_events: List[Dict], session_duration_minutes: Optional[float] = None) -> EnhancedRGScalingResult:
        """
        Transform micro-events into RG-scaled bins optimized for Fisher analysis
        
        This is the main method for processing micro-event data from the enhanced
        extraction pipeline into Fisher Information Monitor input.
        
        Args:
            micro_events: List of micro-events with timestamps and significance scores
            session_duration_minutes: Optional session duration override
            
        Returns:
            EnhancedRGScalingResult optimized for Fisher Information analysis
        """
        if not micro_events:
            return self._empty_result()
        
        # Extract timestamps and convert to minutes
        timestamps = []
        significance_scores = []
        
        for event in micro_events:
            try:
                # Handle different timestamp formats
                if isinstance(event.get('timestamp'), str):
                    time_str = event['timestamp']
                    if ':' in time_str:
                        # Format: "HH:MM:SS"
                        parts = time_str.split(':')
                        minutes = int(parts[0]) * 60 + int(parts[1]) + int(parts[2]) / 60.0
                    else:
                        minutes = float(time_str)
                else:
                    minutes = float(event.get('timestamp', 0))
                
                timestamps.append(minutes)
                
                # Extract significance score
                if self.significance_weighting and 'significance_score' in event:
                    significance_scores.append(float(event['significance_score']))
                else:
                    significance_scores.append(1.0)  # Equal weighting
                    
            except (ValueError, TypeError) as e:
                self.logger.warning(f"Error parsing event timestamp: {e}")
                continue
        
        if not timestamps:
            return self._empty_result()
        
        # Convert to numpy arrays
        timestamps = np.array(timestamps)
        significance_scores = np.array(significance_scores)
        
        # Calculate session bounds
        start_time = np.min(timestamps)
        end_time = np.max(timestamps)
        
        if session_duration_minutes:
            # Use provided duration to maintain consistent binning
            duration = session_duration_minutes
            end_time = start_time + duration
        else:
            duration = end_time - start_time
        
        # Determine optimal bin size based on event density
        events_per_hour = (len(timestamps) / duration) * 60
        optimal_bin_size = self._calculate_optimal_bin_size(events_per_hour)
        
        # Create bins
        num_bins = max(1, int(np.ceil(duration / optimal_bin_size)))
        bin_edges = np.linspace(start_time, start_time + (num_bins * optimal_bin_size), num_bins + 1)
        
        # Bin the events with significance weighting
        binned_counts = np.zeros(num_bins)
        
        for i, timestamp in enumerate(timestamps):
            bin_index = int((timestamp - start_time) / optimal_bin_size)
            if 0 <= bin_index < num_bins:
                if self.significance_weighting:
                    binned_counts[bin_index] += significance_scores[i]
                else:
                    binned_counts[bin_index] += 1.0
        
        # Calculate density metrics
        event_density = events_per_hour / 60.0  # events per minute
        
        # Apply enhanced RG scaling
        if event_density > 0:
            optimal_scale = self._enhanced_scaling_law(event_density)
        else:
            optimal_scale = self.base_scaler.max_scale
        
        # Determine density regime
        density_regime = self._classify_density_regime(events_per_hour)
        
        # Calculate scaling confidence based on density and event count
        scaling_confidence = self._calculate_scaling_confidence(len(timestamps), event_density)
        
        # Calculate crystallization readiness for Fisher analysis
        crystallization_readiness = self._calculate_crystallization_readiness(
            binned_counts, events_per_hour, optimal_bin_size
        )
        
        # Count micro-events (events from micro-event extraction)
        micro_event_count = len([e for e in micro_events if 'micro_' in e.get('event_type', '')])
        
        result = EnhancedRGScalingResult(
            binned_counts=binned_counts,
            optimal_scale=optimal_scale,
            event_density=event_density,
            density_regime=density_regime,
            scaling_confidence=scaling_confidence,
            num_bins=num_bins,
            duration_minutes=duration,
            bin_size_minutes=optimal_bin_size,
            significance_weighted=self.significance_weighting,
            micro_event_count=micro_event_count,
            density_mode=self.density_mode,
            crystallization_readiness=crystallization_readiness
        )
        
        self.logger.info(f"🔬 ENHANCED RG SCALING COMPLETE:")
        self.logger.info(f"   Events: {len(timestamps)} ({micro_event_count} micro-events)")
        self.logger.info(f"   Density: {events_per_hour:.1f} events/hour")
        self.logger.info(f"   Bin Size: {optimal_bin_size:.1f} minutes")
        self.logger.info(f"   Bins: {num_bins} ({np.count_nonzero(binned_counts)} non-zero)")
        self.logger.info(f"   Crystallization Readiness: {crystallization_readiness:.3f}")
        
        return result
    
    def _calculate_optimal_bin_size(self, events_per_hour: float) -> float:
        """Calculate optimal bin size based on event density"""
        
        config = self.density_config[self.density_mode]
        base_bin_size = config['bin_size_minutes']
        threshold = config['events_per_hour_threshold']
        
        if events_per_hour >= 20.0:
            # Very high density - use 1-minute bins
            return 1.0
        elif events_per_hour >= 15.0:
            # High density - use configured size or slightly smaller
            return min(base_bin_size, 1.5)
        elif events_per_hour >= 10.0:
            # Medium density - use configured size
            return base_bin_size
        elif events_per_hour >= 5.0:
            # Lower density - increase bin size
            return base_bin_size * 1.5
        else:
            # Very low density - use larger bins
            return min(15.0, base_bin_size * 3.0)
    
    def _enhanced_scaling_law(self, density: float) -> float:
        """
        Enhanced scaling law with density mode adjustments
        
        Base: s(d) = 15 - 5*log₁₀(d)
        Enhancement: Apply density mode scaling adjustments
        """
        # Use base scaling law
        base_scale = self.base_scaler.inverse_scaling_law(density)
        
        # Apply density mode adjustment
        config = self.density_config[self.density_mode]
        adjustment = config['scaling_adjustment']
        
        enhanced_scale = base_scale * adjustment
        
        # Enforce bounds
        return max(self.base_scaler.min_scale, min(self.base_scaler.max_scale, enhanced_scale))
    
    def _classify_density_regime(self, events_per_hour: float) -> str:
        """Classify event density regime"""
        
        if events_per_hour >= 20.0:
            return 'extreme'
        elif events_per_hour >= 15.0:
            return 'high'
        elif events_per_hour >= 8.0:
            return 'medium'
        else:
            return 'low'
    
    def _calculate_scaling_confidence(self, num_events: int, event_density: float) -> float:
        """Calculate confidence in scaling result"""
        
        # Base confidence from event count
        if num_events >= 20:
            count_confidence = 1.0
        elif num_events >= 10:
            count_confidence = 0.8
        elif num_events >= 5:
            count_confidence = 0.6
        else:
            count_confidence = 0.4
        
        # Density confidence
        target_density = self.density_config[self.density_mode]['events_per_hour_threshold'] / 60.0
        if event_density >= target_density:
            density_confidence = 1.0
        elif event_density >= target_density * 0.5:
            density_confidence = 0.8
        else:
            density_confidence = 0.6
        
        # Combined confidence
        return (count_confidence + density_confidence) / 2.0
    
    def _calculate_crystallization_readiness(self, binned_counts: np.ndarray, events_per_hour: float, bin_size: float) -> float:
        """
        Calculate readiness for Fisher Information crystallization analysis
        
        High readiness indicates optimal conditions for Fisher spike detection
        """
        readiness_factors = []
        
        # 1. Density factor (target 15+ events/hour)
        if events_per_hour >= 15.0:
            density_factor = 1.0
        elif events_per_hour >= 10.0:
            density_factor = 0.8
        elif events_per_hour >= 5.0:
            density_factor = 0.6
        else:
            density_factor = 0.4
        
        readiness_factors.append(density_factor)
        
        # 2. Bin resolution factor (prefer 1-2 minute bins for crystallization)
        if bin_size <= 1.5:
            resolution_factor = 1.0
        elif bin_size <= 3.0:
            resolution_factor = 0.8
        elif bin_size <= 5.0:
            resolution_factor = 0.6
        else:
            resolution_factor = 0.4
        
        readiness_factors.append(resolution_factor)
        
        # 3. Data distribution factor (prefer active bins for pattern detection)
        if len(binned_counts) > 0:
            non_zero_ratio = np.count_nonzero(binned_counts) / len(binned_counts)
            if non_zero_ratio >= 0.3:  # 30%+ bins active
                distribution_factor = 1.0
            elif non_zero_ratio >= 0.2:
                distribution_factor = 0.8
            elif non_zero_ratio >= 0.1:
                distribution_factor = 0.6
            else:
                distribution_factor = 0.4
        else:
            distribution_factor = 0.0
        
        readiness_factors.append(distribution_factor)
        
        # 4. Variance factor (Fisher needs variance for information calculation)
        if len(binned_counts) > 1:
            variance = np.var(binned_counts)
            if variance > 1.0:
                variance_factor = 1.0
            elif variance > 0.5:
                variance_factor = 0.8
            elif variance > 0.1:
                variance_factor = 0.6
            else:
                variance_factor = 0.4
        else:
            variance_factor = 0.0
        
        readiness_factors.append(variance_factor)
        
        # Calculate weighted average (density and resolution most important)
        weights = [0.3, 0.3, 0.2, 0.2]  # density, resolution, distribution, variance
        crystallization_readiness = sum(f * w for f, w in zip(readiness_factors, weights))
        
        return crystallization_readiness
    
    def _empty_result(self) -> EnhancedRGScalingResult:
        """Return empty result for edge cases"""
        
        return EnhancedRGScalingResult(
            binned_counts=np.array([]),
            optimal_scale=self.base_scaler.max_scale,
            event_density=0.0,
            density_regime='low',
            scaling_confidence=0.0,
            num_bins=0,
            duration_minutes=0.0,
            bin_size_minutes=self.density_config[self.density_mode]['bin_size_minutes'],
            significance_weighted=self.significance_weighting,
            micro_event_count=0,
            density_mode=self.density_mode,
            crystallization_readiness=0.0
        )
    
    def create_fisher_input(self, micro_events: List[Dict], session_duration_minutes: Optional[float] = None) -> np.ndarray:
        """
        Convenience method to create Fisher Information Monitor input directly
        
        Args:
            micro_events: List of micro-events from enhanced extraction
            session_duration_minutes: Optional session duration
            
        Returns:
            Numpy array ready for Fisher Information Monitor
        """
        result = self.transform_micro_events(micro_events, session_duration_minutes)
        return result.binned_counts


def create_enhanced_rg_scaler(density_mode: str = "high_density") -> EnhancedRGScaler:
    """Factory for production enhanced RG scaler optimized for micro-events"""
    return EnhancedRGScaler(density_mode=density_mode, significance_weighting=True)


if __name__ == "__main__":
    """
    Test enhanced RG scaler with micro-event data
    """
    print("🚀 ENHANCED RG SCALER: High-Density Micro-Event Testing")
    print("=" * 65)
    
    # Create test micro-events (simulating enhanced extraction output)
    test_micro_events = []
    
    # Generate dense micro-event stream (15+ events/hour)
    base_time = 9 * 60 + 30  # 09:30 in minutes
    for i in range(38):  # 38 events like our real test
        event = {
            'timestamp': f"{(base_time + i * 3) // 60:02d}:{(base_time + i * 3) % 60:02d}:00",
            'event_type': 'micro_fpfvg' if i % 3 == 0 else 'liquidity_grab',
            'significance_score': 0.5 + (i % 5) * 0.1,  # Varying significance
            'event_tier': 1 if i % 2 == 0 else 2
        }
        test_micro_events.append(event)
    
    print(f"📊 Test Data: {len(test_micro_events)} micro-events")
    
    # Test different density modes
    modes = ['sparse', 'adaptive', 'high_density']
    
    for mode in modes:
        print(f"\n🔬 TESTING {mode.upper()} MODE:")
        print("-" * 40)
        
        scaler = create_enhanced_rg_scaler(density_mode=mode)
        result = scaler.transform_micro_events(test_micro_events, session_duration_minutes=150)
        
        print(f"   Bin Size: {result.bin_size_minutes:.1f} minutes")
        print(f"   Bins: {result.num_bins} ({np.count_nonzero(result.binned_counts)} non-zero)")
        print(f"   Event Density: {result.event_density * 60:.1f} events/hour")
        print(f"   Density Regime: {result.density_regime}")
        print(f"   Scaling Confidence: {result.scaling_confidence:.3f}")
        print(f"   Crystallization Readiness: {result.crystallization_readiness:.3f}")
        print(f"   Total Event Weight: {np.sum(result.binned_counts):.1f}")
        
        # Test Fisher input creation
        fisher_input = scaler.create_fisher_input(test_micro_events, 150)
        print(f"   Fisher Array Shape: {fisher_input.shape}")
        print(f"   Max Bin Value: {np.max(fisher_input):.2f}")
    
    # Assess results
    print(f"\n🎯 ASSESSMENT:")
    print("-" * 20)
    
    high_density_scaler = create_enhanced_rg_scaler("high_density")
    hd_result = high_density_scaler.transform_micro_events(test_micro_events, 150)
    
    if hd_result.crystallization_readiness >= 0.8:
        print(f"✅ HIGH DENSITY MODE: Excellent crystallization readiness ({hd_result.crystallization_readiness:.3f})")
    elif hd_result.crystallization_readiness >= 0.6:
        print(f"✅ HIGH DENSITY MODE: Good crystallization readiness ({hd_result.crystallization_readiness:.3f})")
    else:
        print(f"⚠️ HIGH DENSITY MODE: Needs improvement ({hd_result.crystallization_readiness:.3f})")
    
    if hd_result.bin_size_minutes <= 2.0:
        print(f"✅ OPTIMAL BIN SIZE: {hd_result.bin_size_minutes:.1f} minutes (≤2.0 for crystallization)")
    else:
        print(f"⚠️ BIN SIZE: {hd_result.bin_size_minutes:.1f} minutes (>2.0, may reduce sensitivity)")
    
    print(f"\n🚀 ENHANCED RG SCALER: Ready for Fisher Information Monitor integration")
    print("   Optimized for 15-20 events/hour micro-event density")