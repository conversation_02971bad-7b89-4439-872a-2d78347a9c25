#!/usr/bin/env python3
"""
Machine Learning Model Assessment
=================================

Evaluates the ML components of the Oracle system:
- Fisher Information calculations and calibration needs
- Grammar Bridge pattern recognition training requirements
- Three-Oracle consensus mechanism parameter tuning
- RG Scaler optimal scale detection model improvements
"""

import sys
import json
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add current directory to path
sys.path.append('.')

# Import Oracle components
from compartments.predict import PredictCompartment
from core_predictor.rg_scaler_production import RGScaler
from core_predictor.fisher_information_monitor import FisherInformationMonitor

class MLModelAssessment:
    """Comprehensive ML model evaluation for Oracle system"""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'fisher_information': {},
            'grammar_bridge': {},
            'three_oracle_consensus': {},
            'rg_scaler_model': {},
            'recommendations': []
        }
    
    def assess_fisher_information(self) -> Dict[str, Any]:
        """Evaluate Fisher Information calculations and calibration needs"""
        print("🔬 ASSESSING FISHER INFORMATION MODEL")
        print("=" * 60)
        
        fisher_results = {}
        
        try:
            # Test Fisher Information Monitor
            fisher_monitor = FisherInformationMonitor()
            predict_comp = PredictCompartment()
            
            # Load Grammar Bridge data for testing
            grammar_file = Path("grammar_bridge/cascade_events.json")
            if grammar_file.exists():
                with open(grammar_file, 'r') as f:
                    grammar_data = json.load(f)
                
                events = grammar_data.get('unified_cascade_events', [])
                
                # Test with different session samples
                test_sessions = ['NYAM_Lvl-1_2025_08_04_REAL', 'NYAM_Lvl-1_2025_08_05_COMPLETE', 'NYAM_Lvl-1_2025_08_06']
                fisher_scores = []
                
                for session_id in test_sessions:
                    session_events = [e for e in events if e.get('session_id') == session_id]
                    if session_events:
                        # Test Fisher calculation
                        adapted_data = predict_comp._adapt_cascade_events_to_rg_scaler(session_events)
                        adapted_events = adapted_data.get('micro_timing_analysis', {}).get('cascade_events', [])
                        
                        if adapted_events:
                            # Calculate Fisher Information
                            timestamps = [e.get('timestamp_minutes', 0) for e in adapted_events]
                            densities = [len(adapted_events) / (max(timestamps) - min(timestamps)) if max(timestamps) > min(timestamps) else 0]
                            
                            # Test Fisher estimation
                            fisher_score = predict_comp._estimate_fisher_information(adapted_events)
                            fisher_scores.append(fisher_score)
                            
                            print(f"   Session {session_id}: Fisher={fisher_score:.1f}, Events={len(adapted_events)}")
                
                if fisher_scores:
                    fisher_results = {
                        'status': 'operational',
                        'sample_scores': fisher_scores,
                        'average_score': sum(fisher_scores) / len(fisher_scores),
                        'score_range': [min(fisher_scores), max(fisher_scores)],
                        'threshold_analysis': {
                            'current_threshold': 200.0,
                            'scores_above_threshold': sum(1 for s in fisher_scores if s > 200.0),
                            'deterministic_mode_triggered': all(s > 200.0 for s in fisher_scores)
                        },
                        'calibration_needed': False  # Scores are consistent
                    }
                    
                    avg_score = fisher_results['average_score']
                    print(f"   ✅ Fisher Information: Average {avg_score:.1f}, Range {fisher_results['score_range']}")
                    
                    # Check if recalibration needed
                    if avg_score < 50:
                        fisher_results['calibration_needed'] = True
                        fisher_results['recommendation'] = "Increase sensitivity - scores too low"
                    elif avg_score > 1000:
                        fisher_results['calibration_needed'] = True
                        fisher_results['recommendation'] = "Decrease sensitivity - scores too high"
                    else:
                        fisher_results['recommendation'] = "Fisher Information calibration is optimal"
                        
                else:
                    fisher_results = {'status': 'no_data', 'calibration_needed': True}
                    print(f"   ❌ No Fisher scores calculated")
            else:
                fisher_results = {'status': 'no_grammar_data', 'calibration_needed': True}
                print(f"   ❌ No Grammar Bridge data available")
                
        except Exception as e:
            fisher_results = {'status': 'error', 'error': str(e), 'calibration_needed': True}
            print(f"   ❌ Fisher Information error: {e}")
        
        self.results['fisher_information'] = fisher_results
        return fisher_results
    
    def assess_grammar_bridge_patterns(self) -> Dict[str, Any]:
        """Evaluate Grammar Bridge pattern recognition training requirements"""
        print("\n🌉 ASSESSING GRAMMAR BRIDGE PATTERN RECOGNITION")
        print("=" * 60)
        
        grammar_results = {}
        
        try:
            predict_comp = PredictCompartment()
            
            # Load Grammar Bridge data
            grammar_file = Path("grammar_bridge/cascade_events.json")
            if grammar_file.exists():
                with open(grammar_file, 'r') as f:
                    grammar_data = json.load(f)
                
                events = grammar_data.get('unified_cascade_events', [])
                
                # Analyze event type distribution
                event_types = [e.get('event_type', 'unknown') for e in events]
                event_type_counts = {}
                for event_type in event_types:
                    event_type_counts[event_type] = event_type_counts.get(event_type, 0) + 1
                
                # Test pattern recognition on different sample sizes
                sample_sizes = [50, 100, 200, 400]
                pattern_results = []
                
                for size in sample_sizes:
                    if len(events) >= size:
                        sample_events = events[:size]
                        pattern_analysis = predict_comp._analyze_grammar_patterns(sample_events)
                        
                        pattern_results.append({
                            'sample_size': size,
                            'completion_probability': pattern_analysis.get('completion_probability', 0),
                            'pattern_type': pattern_analysis.get('pattern_type', 'unknown'),
                            'high_value_events': pattern_analysis.get('high_value_events', 0)
                        })
                        
                        print(f"   Sample {size}: Completion={pattern_analysis.get('completion_probability', 0):.3f}, Pattern={pattern_analysis.get('pattern_type', 'unknown')}")
                
                # Analyze pattern recognition quality
                completion_probs = [r['completion_probability'] for r in pattern_results]
                unique_patterns = len(set(r['pattern_type'] for r in pattern_results))
                
                grammar_results = {
                    'status': 'operational',
                    'total_events': len(events),
                    'unique_event_types': len(event_type_counts),
                    'event_type_distribution': dict(sorted(event_type_counts.items(), key=lambda x: x[1], reverse=True)[:10]),
                    'pattern_recognition_quality': {
                        'average_completion': sum(completion_probs) / len(completion_probs) if completion_probs else 0,
                        'completion_range': [min(completion_probs), max(completion_probs)] if completion_probs else [0, 0],
                        'unique_patterns_detected': unique_patterns,
                        'sample_results': pattern_results
                    },
                    'training_needed': False
                }
                
                avg_completion = grammar_results['pattern_recognition_quality']['average_completion']
                
                # Determine if additional training needed
                if avg_completion < 0.5:
                    grammar_results['training_needed'] = True
                    grammar_results['recommendation'] = "CRITICAL: Pattern recognition too low - needs additional training data"
                elif unique_patterns < 3:
                    grammar_results['training_needed'] = True
                    grammar_results['recommendation'] = "MEDIUM: Limited pattern diversity - expand training patterns"
                elif avg_completion > 0.95:
                    grammar_results['training_needed'] = False
                    grammar_results['recommendation'] = "WARNING: Pattern recognition may be overfitted - validate with new data"
                else:
                    grammar_results['training_needed'] = False
                    grammar_results['recommendation'] = "Pattern recognition is well-calibrated"
                
                print(f"   ✅ Pattern Recognition: {avg_completion:.3f} avg completion, {unique_patterns} unique patterns")
                
            else:
                grammar_results = {'status': 'no_data', 'training_needed': True}
                print(f"   ❌ No Grammar Bridge data available")
                
        except Exception as e:
            grammar_results = {'status': 'error', 'error': str(e), 'training_needed': True}
            print(f"   ❌ Grammar Bridge error: {e}")
        
        self.results['grammar_bridge'] = grammar_results
        return grammar_results
    
    def assess_three_oracle_consensus(self) -> Dict[str, Any]:
        """Evaluate Three-Oracle consensus mechanism parameter tuning"""
        print("\n🏛️ ASSESSING THREE-ORACLE CONSENSUS MECHANISM")
        print("=" * 60)
        
        consensus_results = {}
        
        try:
            predict_comp = PredictCompartment()
            
            # Run multiple predictions to analyze consensus behavior
            prediction_runs = []
            oracle_choices = []
            confidences = []
            
            for i in range(5):
                result = predict_comp._perform_real_predictions({})
                predictions = result.get('predictions', [])
                
                if predictions:
                    for pred in predictions:
                        oracle_choice = pred.get('oracle_choice', 'unknown')
                        confidence = pred.get('confidence', 0.0)
                        
                        oracle_choices.append(oracle_choice)
                        confidences.append(confidence)
                        
                        prediction_runs.append({
                            'oracle_choice': oracle_choice,
                            'confidence': confidence,
                            'timing_window': pred.get('timing_window', 'unknown'),
                            'virgin_prediction': pred.get('virgin_prediction', 0.0),
                            'contaminated_prediction': pred.get('contaminated_prediction', 0.0)
                        })
            
            if prediction_runs:
                # Analyze consensus patterns
                oracle_distribution = {}
                for choice in oracle_choices:
                    oracle_distribution[choice] = oracle_distribution.get(choice, 0) + 1
                
                consensus_results = {
                    'status': 'operational',
                    'total_predictions': len(prediction_runs),
                    'oracle_distribution': oracle_distribution,
                    'confidence_stats': {
                        'average': sum(confidences) / len(confidences),
                        'min': min(confidences),
                        'max': max(confidences),
                        'std_dev': np.std(confidences) if len(confidences) > 1 else 0
                    },
                    'consensus_quality': {
                        'dominant_oracle': max(oracle_distribution.items(), key=lambda x: x[1])[0],
                        'oracle_diversity': len(oracle_distribution),
                        'confidence_consistency': np.std(confidences) < 0.1 if len(confidences) > 1 else True
                    },
                    'parameter_tuning_needed': False
                }
                
                # Determine if parameter tuning needed
                dominant_oracle_pct = max(oracle_distribution.values()) / len(prediction_runs)
                avg_confidence = consensus_results['confidence_stats']['average']
                
                if dominant_oracle_pct > 0.9:
                    consensus_results['parameter_tuning_needed'] = True
                    consensus_results['recommendation'] = "MEDIUM: Consensus too uniform - adjust oracle thresholds for diversity"
                elif avg_confidence < 0.7:
                    consensus_results['parameter_tuning_needed'] = True
                    consensus_results['recommendation'] = "HIGH: Low confidence predictions - tune consensus parameters"
                elif len(oracle_distribution) == 1:
                    consensus_results['parameter_tuning_needed'] = True
                    consensus_results['recommendation'] = "HIGH: No oracle diversity - check threshold calibration"
                else:
                    consensus_results['parameter_tuning_needed'] = False
                    consensus_results['recommendation'] = "Three-Oracle consensus is well-balanced"
                
                print(f"   ✅ Consensus: {avg_confidence:.3f} avg confidence, {len(oracle_distribution)} oracle types")
                print(f"   📊 Distribution: {oracle_distribution}")
                
            else:
                consensus_results = {'status': 'no_predictions', 'parameter_tuning_needed': True}
                print(f"   ❌ No predictions generated for consensus analysis")
                
        except Exception as e:
            consensus_results = {'status': 'error', 'error': str(e), 'parameter_tuning_needed': True}
            print(f"   ❌ Three-Oracle consensus error: {e}")
        
        self.results['three_oracle_consensus'] = consensus_results
        return consensus_results

    def assess_rg_scaler_model(self) -> Dict[str, Any]:
        """Evaluate RG Scaler optimal scale detection model improvements"""
        print("\n⚖️ ASSESSING RG SCALER MODEL")
        print("=" * 60)

        rg_results = {}

        try:
            rg_scaler = RGScaler()
            predict_comp = PredictCompartment()

            # Load Grammar Bridge data for testing
            grammar_file = Path("grammar_bridge/cascade_events.json")
            if grammar_file.exists():
                with open(grammar_file, 'r') as f:
                    grammar_data = json.load(f)

                events = grammar_data.get('unified_cascade_events', [])

                # Test RG Scaler with different sessions
                test_sessions = ['NYAM_Lvl-1_2025_08_04_REAL', 'NYAM_Lvl-1_2025_08_05_COMPLETE', 'NYAM_Lvl-1_2025_08_06']
                rg_results_list = []

                for session_id in test_sessions:
                    session_events = [e for e in events if e.get('session_id') == session_id]
                    if session_events:
                        # Test RG Scaler
                        adapted_data = predict_comp._adapt_cascade_events_to_rg_scaler(session_events)

                        if adapted_data.get('micro_timing_analysis', {}).get('cascade_events'):
                            result = rg_scaler.transform_session_data(adapted_data)

                            if result:
                                rg_results_list.append({
                                    'session_id': session_id,
                                    'optimal_scale': result.optimal_scale,
                                    'event_density': result.event_density,
                                    'scaling_confidence': result.scaling_confidence,
                                    'density_regime': result.density_regime,
                                    'events_count': len(session_events)
                                })

                                print(f"   Session {session_id}: Scale={result.optimal_scale:.1f}, Density={result.event_density:.3f}, Confidence={result.scaling_confidence:.3f}")

                if rg_results_list:
                    scales = [r['optimal_scale'] for r in rg_results_list]
                    densities = [r['event_density'] for r in rg_results_list]
                    confidences = [r['scaling_confidence'] for r in rg_results_list]

                    rg_results = {
                        'status': 'operational',
                        'session_results': rg_results_list,
                        'scale_analysis': {
                            'average_scale': sum(scales) / len(scales),
                            'scale_range': [min(scales), max(scales)],
                            'scale_consistency': np.std(scales) < 2.0  # Scales should be relatively consistent
                        },
                        'density_analysis': {
                            'average_density': sum(densities) / len(densities),
                            'density_range': [min(densities), max(densities)],
                            'density_variation': np.std(densities)
                        },
                        'confidence_analysis': {
                            'average_confidence': sum(confidences) / len(confidences),
                            'min_confidence': min(confidences),
                            'all_high_confidence': all(c > 0.8 for c in confidences)
                        },
                        'model_improvement_needed': False
                    }

                    avg_confidence = rg_results['confidence_analysis']['average_confidence']
                    scale_consistency = rg_results['scale_analysis']['scale_consistency']

                    # Determine if model improvements needed
                    if avg_confidence < 0.7:
                        rg_results['model_improvement_needed'] = True
                        rg_results['recommendation'] = "HIGH: Low scaling confidence - model needs retraining"
                    elif not scale_consistency:
                        rg_results['model_improvement_needed'] = True
                        rg_results['recommendation'] = "MEDIUM: Inconsistent scale detection - refine model parameters"
                    elif max(densities) - min(densities) > 1.0:
                        rg_results['model_improvement_needed'] = True
                        rg_results['recommendation'] = "MEDIUM: High density variation - consider density normalization"
                    else:
                        rg_results['model_improvement_needed'] = False
                        rg_results['recommendation'] = "RG Scaler model is performing optimally"

                    print(f"   ✅ RG Scaler: {avg_confidence:.3f} avg confidence, {scale_consistency} scale consistency")

                else:
                    rg_results = {'status': 'no_results', 'model_improvement_needed': True}
                    print(f"   ❌ No RG Scaler results generated")
            else:
                rg_results = {'status': 'no_data', 'model_improvement_needed': True}
                print(f"   ❌ No Grammar Bridge data available")

        except Exception as e:
            rg_results = {'status': 'error', 'error': str(e), 'model_improvement_needed': True}
            print(f"   ❌ RG Scaler error: {e}")

        self.results['rg_scaler_model'] = rg_results
        return rg_results

    def generate_ml_recommendations(self) -> List[str]:
        """Generate ML-specific recommendations"""
        recommendations = []

        # Fisher Information recommendations
        fisher = self.results.get('fisher_information', {})
        if fisher.get('calibration_needed'):
            recommendations.append("🔬 FISHER: Recalibrate Fisher Information thresholds")

        # Grammar Bridge recommendations
        grammar = self.results.get('grammar_bridge', {})
        if grammar.get('training_needed'):
            recommendations.append("🌉 GRAMMAR: Additional pattern training data required")

        # Three-Oracle recommendations
        consensus = self.results.get('three_oracle_consensus', {})
        if consensus.get('parameter_tuning_needed'):
            recommendations.append("🏛️ CONSENSUS: Tune Three-Oracle threshold parameters")

        # RG Scaler recommendations
        rg_scaler = self.results.get('rg_scaler_model', {})
        if rg_scaler.get('model_improvement_needed'):
            recommendations.append("⚖️ RG_SCALER: Model retraining or parameter refinement needed")

        # Overall ML health
        if not recommendations:
            recommendations.append("✅ ML_MODELS: All machine learning components are optimally calibrated")

        self.results['recommendations'] = recommendations
        return recommendations

    def run_complete_assessment(self) -> Dict[str, Any]:
        """Run complete ML model assessment"""
        print("🤖 MACHINE LEARNING MODEL ASSESSMENT")
        print("=" * 80)
        print(f"Timestamp: {self.results['timestamp']}")

        # Run all assessment phases
        self.assess_fisher_information()
        self.assess_grammar_bridge_patterns()
        self.assess_three_oracle_consensus()
        self.assess_rg_scaler_model()

        # Generate recommendations
        recommendations = self.generate_ml_recommendations()

        # Summary
        print("\n📋 ML MODEL ASSESSMENT SUMMARY")
        print("=" * 40)

        for rec in recommendations:
            print(f"   {rec}")

        # Overall ML health
        critical_ml_issues = [r for r in recommendations if any(word in r for word in ['CRITICAL', 'HIGH'])]

        if not critical_ml_issues:
            ml_status = "PRODUCTION_READY"
        else:
            ml_status = "NEEDS_ML_IMPROVEMENTS"

        self.results['overall_ml_status'] = ml_status

        print(f"\n🎯 OVERALL ML STATUS: {ml_status}")

        return self.results

if __name__ == "__main__":
    assessor = MLModelAssessment()
    results = assessor.run_complete_assessment()

    # Save results
    with open('ml_model_assessment.json', 'w') as f:
        json.dump(results, f, indent=2)

    print(f"\n💾 Results saved to ml_model_assessment.json")
