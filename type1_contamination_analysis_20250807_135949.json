{"analysis_timestamp": "2025-08-07T13:59:49.642464", "contamination_summary": {"total_patterns": 29, "clean_type2_patterns": 0, "contaminated_patterns": 29, "pda_feasible": false, "hybrid_required": true}, "performance_analysis": {"original_target_speedup": 5.5, "effective_speedup": 1.206896551724138, "performance_retention": 0.219435736677116, "max_memory_requirement": 5}, "contaminated_patterns": [{"signature": "CONSOLIDATION → EXPANSION → REDELIVERY", "memory_requirement": "unbounded", "memory_span": 3, "contamination_level": "severe", "context_dependencies": [["CONSOLIDATION", "CONSOLIDATION EXPANSION REDELIVERY", ""], ["CONTEXT_A CONSOLIDATION", "CONSOLIDATION EXPANSION REDELIVERY", "CONTEXT_C"], ["CONTEXT_B CONSOLIDATION", "CONSOLIDATION EXPANSION REDELIVERY", "CONTEXT_D"]]}, {"signature": "FPFVG → FPFVG → FPFVG", "memory_requirement": "bounded", "memory_span": 3, "contamination_level": "mild", "context_dependencies": [["", "FPFVG FPFVG FPFVG", "FPFVG"], ["FPFVG", "FPFVG FPFVG FPFVG", ""], ["CONTEXT_A", "FPFVG FPFVG FPFVG", "FPFVG CONTEXT_C"], ["CONTEXT_A FPFVG", "FPFVG FPFVG FPFVG", "CONTEXT_C"], ["CONTEXT_B", "FPFVG FPFVG FPFVG", "FPFVG CONTEXT_D"], ["CONTEXT_B FPFVG", "FPFVG FPFVG FPFVG", "CONTEXT_D"], ["", "FPFVG FPFVG FPFVG", "FPFVG FPFVG"], ["FPFVG", "FPFVG FPFVG FPFVG", "FPFVG"], ["FPFVG FPFVG", "FPFVG FPFVG FPFVG", ""], ["CONTEXT_A", "FPFVG FPFVG FPFVG", "FPFVG FPFVG"], ["CONTEXT_A FPFVG", "FPFVG FPFVG FPFVG", "FPFVG CONTEXT_C"], ["FPFVG FPFVG", "FPFVG FPFVG FPFVG", "CONTEXT_C"], ["CONTEXT_B", "FPFVG FPFVG FPFVG", "FPFVG FPFVG"], ["CONTEXT_B FPFVG", "FPFVG FPFVG FPFVG", "FPFVG CONTEXT_D"], ["FPFVG FPFVG", "FPFVG FPFVG FPFVG", "CONTEXT_D"]]}, {"signature": "CONSOLIDATION → FPFVG", "memory_requirement": "unbounded", "memory_span": 2, "contamination_level": "severe", "context_dependencies": []}, {"signature": "FPFVG → FPFVG → FPFVG → FPFVG", "memory_requirement": "bounded", "memory_span": 4, "contamination_level": "mild", "context_dependencies": [["", "FPFVG FPFVG FPFVG FPFVG", "FPFVG"], ["FPFVG", "FPFVG FPFVG FPFVG FPFVG", ""], ["CONTEXT_A", "FPFVG FPFVG FPFVG FPFVG", "FPFVG CONTEXT_C"], ["CONTEXT_A FPFVG", "FPFVG FPFVG FPFVG FPFVG", "CONTEXT_C"], ["CONTEXT_B", "FPFVG FPFVG FPFVG FPFVG", "FPFVG CONTEXT_D"], ["CONTEXT_B FPFVG", "FPFVG FPFVG FPFVG FPFVG", "CONTEXT_D"]]}, {"signature": "REDELIVERY → FPFVG", "memory_requirement": "unbounded", "memory_span": 2, "contamination_level": "severe", "context_dependencies": []}, {"signature": "FPFVG → INTERACTION", "memory_requirement": "unbounded", "memory_span": 2, "contamination_level": "severe", "context_dependencies": []}, {"signature": "CONSOLIDATION → FPFVG [2-event]", "memory_requirement": "unbounded", "memory_span": 2, "contamination_level": "severe", "context_dependencies": []}, {"signature": "REDELIVERY → FPFVG [2-event]", "memory_requirement": "unbounded", "memory_span": 2, "contamination_level": "severe", "context_dependencies": []}, {"signature": "FPFVG → INTERACTION [2-event]", "memory_requirement": "unbounded", "memory_span": 2, "contamination_level": "severe", "context_dependencies": []}, {"signature": "FPFVG → FPFVG → FPFVG → FPFVG → FPFVG", "memory_requirement": "unbounded", "memory_span": 5, "contamination_level": "severe", "context_dependencies": []}, {"signature": "FPFVG → EXPANSION_HIGH → CONSOLIDATION", "memory_requirement": "unbounded", "memory_span": 3, "contamination_level": "severe", "context_dependencies": [["", "FPFVG EXPANSION_HIGH CONSOLIDATION", "EXPANSION_LOW CONSOLIDATION"], ["CONTEXT_A", "FPFVG EXPANSION_HIGH CONSOLIDATION", "EXPANSION_LOW CONSOLIDATION"], ["CONTEXT_B", "FPFVG EXPANSION_HIGH CONSOLIDATION", "EXPANSION_LOW CONSOLIDATION"]]}, {"signature": "FPFVG → EXPANSION_HIGH → CONSOLIDATION → EXPANSION_LOW", "memory_requirement": "unbounded", "memory_span": 4, "contamination_level": "severe", "context_dependencies": [["", "FPFVG EXPANSION_HIGH CONSOLIDATION EXPANSION_LOW", "CONSOLIDATION"], ["CONTEXT_A", "FPFVG EXPANSION_HIGH CONSOLIDATION EXPANSION_LOW", "CONSOLIDATION CONTEXT_C"], ["CONTEXT_B", "FPFVG EXPANSION_HIGH CONSOLIDATION EXPANSION_LOW", "CONSOLIDATION CONTEXT_D"]]}, {"signature": "FPFVG → EXPANSION_HIGH → CONSOLIDATION → EXPANSION_LOW → CONSOLIDATION", "memory_requirement": "unbounded", "memory_span": 5, "contamination_level": "severe", "context_dependencies": []}, {"signature": "EXPANSION_HIGH → CONSOLIDATION → EXPANSION_LOW", "memory_requirement": "unbounded", "memory_span": 3, "contamination_level": "severe", "context_dependencies": [["FPFVG", "EXPANSION_HIGH CONSOLIDATION EXPANSION_LOW", "CONSOLIDATION"], ["CONTEXT_A FPFVG", "EXPANSION_HIGH CONSOLIDATION EXPANSION_LOW", "CONSOLIDATION CONTEXT_C"], ["CONTEXT_B FPFVG", "EXPANSION_HIGH CONSOLIDATION EXPANSION_LOW", "CONSOLIDATION CONTEXT_D"], ["", "EXPANSION_HIGH CONSOLIDATION EXPANSION_LOW", "CONSOLIDATION"], ["CONTEXT_A", "EXPANSION_HIGH CONSOLIDATION EXPANSION_LOW", "CONSOLIDATION CONTEXT_C"], ["CONTEXT_B", "EXPANSION_HIGH CONSOLIDATION EXPANSION_LOW", "CONSOLIDATION CONTEXT_D"]]}, {"signature": "EXPANSION_HIGH → CONSOLIDATION → EXPANSION_LOW → CONSOLIDATION", "memory_requirement": "unbounded", "memory_span": 4, "contamination_level": "severe", "context_dependencies": []}, {"signature": "CONSOLIDATION → EXPANSION_LOW → CONSOLIDATION", "memory_requirement": "unbounded", "memory_span": 3, "contamination_level": "severe", "context_dependencies": []}, {"signature": "CONSOLIDATION → CONSOLIDATION → EXPANSION", "memory_requirement": "unbounded", "memory_span": 3, "contamination_level": "severe", "context_dependencies": []}, {"signature": "CONSOLIDATION → CONSOLIDATION → EXPANSION → REDELIVERY", "memory_requirement": "unbounded", "memory_span": 4, "contamination_level": "severe", "context_dependencies": []}, {"signature": "CONSOLIDATION → EXPANSION → REDELIVERY → EXPANSION_HIGH", "memory_requirement": "unbounded", "memory_span": 4, "contamination_level": "severe", "context_dependencies": []}, {"signature": "CONSOLIDATION → EXPANSION → REDELIVERY → EXPANSION_HIGH → REVERSAL", "memory_requirement": "unbounded", "memory_span": 5, "contamination_level": "severe", "context_dependencies": []}, {"signature": "EXPANSION → REDELIVERY → EXPANSION_HIGH", "memory_requirement": "unbounded", "memory_span": 3, "contamination_level": "severe", "context_dependencies": []}, {"signature": "EXPANSION → REDELIVERY → EXPANSION_HIGH → REVERSAL", "memory_requirement": "unbounded", "memory_span": 4, "contamination_level": "severe", "context_dependencies": []}, {"signature": "REDELIVERY → EXPANSION_HIGH → REVERSAL", "memory_requirement": "unbounded", "memory_span": 3, "contamination_level": "severe", "context_dependencies": []}, {"signature": "EXPANSION_HIGH → REVERSAL", "memory_requirement": "unbounded", "memory_span": 2, "contamination_level": "severe", "context_dependencies": []}, {"signature": "CONSOLIDATION → EXPANSION → REDELIVERY → FPFVG", "memory_requirement": "unbounded", "memory_span": 4, "contamination_level": "severe", "context_dependencies": []}, {"signature": "EXPANSION → REDELIVERY → FPFVG", "memory_requirement": "unbounded", "memory_span": 3, "contamination_level": "severe", "context_dependencies": []}, {"signature": "FPFVG → INTERACTION → EXPANSION_HIGH", "memory_requirement": "unbounded", "memory_span": 3, "contamination_level": "severe", "context_dependencies": []}, {"signature": "REDELIVERY → REDELIVERY → EXPANSION_LOW", "memory_requirement": "bounded", "memory_span": 3, "contamination_level": "mild", "context_dependencies": []}, {"signature": "EXPANSION_HIGH → REVERSAL [2-event]", "memory_requirement": "unbounded", "memory_span": 2, "contamination_level": "severe", "context_dependencies": []}], "fallback_patterns": ["CONSOLIDATION → EXPANSION → REDELIVERY", "CONSOLIDATION → FPFVG", "REDELIVERY → FPFVG", "FPFVG → INTERACTION", "CONSOLIDATION → FPFVG [2-event]", "REDELIVERY → FPFVG [2-event]", "FPFVG → INTERACTION [2-event]", "FPFVG → FPFVG → FPFVG → FPFVG → FPFVG", "FPFVG → EXPANSION_HIGH → CONSOLIDATION", "FPFVG → EXPANSION_HIGH → CONSOLIDATION → EXPANSION_LOW", "FPFVG → EXPANSION_HIGH → CONSOLIDATION → EXPANSION_LOW → CONSOLIDATION", "EXPANSION_HIGH → CONSOLIDATION → EXPANSION_LOW", "EXPANSION_HIGH → CONSOLIDATION → EXPANSION_LOW → CONSOLIDATION", "CONSOLIDATION → EXPANSION_LOW → CONSOLIDATION", "CONSOLIDATION → CONSOLIDATION → EXPANSION", "CONSOLIDATION → CONSOLIDATION → EXPANSION → REDELIVERY", "CONSOLIDATION → EXPANSION → REDELIVERY → EXPANSION_HIGH", "CONSOLIDATION → EXPANSION → REDELIVERY → EXPANSION_HIGH → REVERSAL", "EXPANSION → REDELIVERY → EXPANSION_HIGH", "EXPANSION → REDELIVERY → EXPANSION_HIGH → REVERSAL", "REDELIVERY → EXPANSION_HIGH → REVERSAL", "EXPANSION_HIGH → REVERSAL", "CONSOLIDATION → EXPANSION → REDELIVERY → FPFVG", "EXPANSION → REDELIVERY → FPFVG", "FPFVG → INTERACTION → EXPANSION_HIGH", "EXPANSION_HIGH → REVERSAL [2-event]"], "implementation_recommendation": {"proceed_with_pda": false, "hybrid_architecture": true, "expected_performance": "1.2x"}}