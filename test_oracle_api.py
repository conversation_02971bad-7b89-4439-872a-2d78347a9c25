#!/usr/bin/env python3
"""
Test Oracle API Client
======================

Test client for the FastAPI Oracle prediction system.
Validates all endpoints and functionality.
"""

import requests
import json
import time
from datetime import datetime

# API base URL
BASE_URL = "http://localhost:8001"

def test_api_endpoints():
    """Test all Oracle API endpoints"""
    
    print("🧪 TESTING ORACLE PREDICTION API")
    print("=" * 60)
    
    # Test 1: Root endpoint
    print("\n🔬 TEST 1: Root Endpoint")
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Root endpoint: {data['service']} v{data['version']}")
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Root endpoint error: {e}")
        return False
    
    # Test 2: Health check
    print("\n🔬 TEST 2: Health Check")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            health = response.json()
            print(f"✅ Health status: {health['status']}")
            print(f"   Uptime: {health['uptime_seconds']:.1f} seconds")
            print(f"   Components: {health['components']}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False
    
    # Test 3: System stats
    print("\n🔬 TEST 3: System Statistics")
    try:
        response = requests.get(f"{BASE_URL}/stats")
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ System stats:")
            print(f"   Total predictions: {stats['total_predictions']}")
            print(f"   Grammar Bridge events: {stats['grammar_bridge_events']}")
            print(f"   Active sessions: {stats['active_sessions']}")
        else:
            print(f"❌ Stats endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Stats endpoint error: {e}")
        return False
    
    # Test 4: Prediction endpoint
    print("\n🔬 TEST 4: Prediction Endpoint")
    try:
        # Create test session data
        session_data = {
            "session_id": "TEST_NYAM_2025_08_09",
            "session_type": "NYAM",
            "session_date": "2025-08-09",
            "price_movements": [
                {
                    "timestamp": "09:30:00",
                    "price_level": 23500.0,
                    "movement_type": "expansion_high"
                }
            ],
            "liquidity_events": [
                {
                    "timestamp": "09:31:00",
                    "event_type": "liquidity_sweep",
                    "confidence": 0.8
                }
            ],
            "use_grammar_bridge": True
        }
        
        start_time = time.time()
        response = requests.post(f"{BASE_URL}/predict", json=session_data)
        latency = (time.time() - start_time) * 1000
        
        if response.status_code == 200:
            prediction = response.json()
            print(f"✅ Prediction successful:")
            print(f"   Prediction ID: {prediction['prediction_id']}")
            print(f"   Oracle Choice: {prediction['oracle_choice']}")
            print(f"   Final Prediction: {prediction['final_prediction']}")
            print(f"   Confidence: {prediction['prediction_confidence']:.3f}")
            print(f"   Pattern Type: {prediction['pattern_type']}")
            print(f"   API Latency: {latency:.2f}ms")
            print(f"   System Latency: {prediction['latency_ms']:.2f}ms")
        else:
            print(f"❌ Prediction failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Prediction error: {e}")
        return False
    
    # Test 5: Existing session prediction (if available)
    print("\n🔬 TEST 5: Existing Session Prediction")
    try:
        # Try to predict an existing session
        session_id = "NYAM_Lvl-1_2025_08_04_REAL"
        response = requests.post(f"{BASE_URL}/predict/session/{session_id}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Existing session prediction successful:")
            print(f"   Session ID: {result['session_id']}")
            print(f"   Predictions: {len(result['prediction_result'].get('predictions', []))}")
        elif response.status_code == 404:
            print(f"⚠️ Session {session_id} not found (expected if no enhanced sessions)")
        else:
            print(f"❌ Existing session prediction failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Existing session error: {e}")
    
    print(f"\n🎉 API TESTING COMPLETED")
    return True

def benchmark_api_performance():
    """Benchmark API performance with multiple requests"""
    
    print("\n⚡ BENCHMARKING API PERFORMANCE")
    print("=" * 40)
    
    session_data = {
        "session_id": "BENCHMARK_TEST",
        "session_type": "NYAM",
        "session_date": "2025-08-09",
        "use_grammar_bridge": True
    }
    
    num_requests = 5
    latencies = []
    
    for i in range(num_requests):
        try:
            start_time = time.time()
            response = requests.post(f"{BASE_URL}/predict", json=session_data)
            latency = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                latencies.append(latency)
                print(f"Request {i+1}: {latency:.2f}ms")
            else:
                print(f"Request {i+1}: FAILED ({response.status_code})")
                
        except Exception as e:
            print(f"Request {i+1}: ERROR - {e}")
    
    if latencies:
        avg_latency = sum(latencies) / len(latencies)
        min_latency = min(latencies)
        max_latency = max(latencies)
        
        print(f"\n📊 Performance Results:")
        print(f"   Successful requests: {len(latencies)}/{num_requests}")
        print(f"   Average latency: {avg_latency:.2f}ms")
        print(f"   Min latency: {min_latency:.2f}ms")
        print(f"   Max latency: {max_latency:.2f}ms")
        
        if avg_latency < 100:
            print(f"   ✅ Performance: EXCELLENT (< 100ms)")
        elif avg_latency < 500:
            print(f"   ✅ Performance: GOOD (< 500ms)")
        else:
            print(f"   ⚠️ Performance: NEEDS OPTIMIZATION (> 500ms)")

if __name__ == "__main__":
    print("🚀 Starting Oracle API Tests")
    print("Make sure the API server is running: uvicorn oracle_api:app --reload")
    print()
    
    # Wait for user confirmation
    input("Press Enter when the API server is ready...")
    
    # Run tests
    success = test_api_endpoints()
    
    if success:
        benchmark_api_performance()
    
    print("\n✅ Testing completed!")
