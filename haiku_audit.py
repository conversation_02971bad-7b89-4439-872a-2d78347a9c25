#!/usr/bin/env python3
"""
Haiku Agent for On-Demand Audit
==============================

Lightweight auditor that uses <PERSON> for fast categorization and drift detection.
Designed for quick checks before escalating to full audit system.

Features:
- Git diff categorization (critical/guards/logic/cosmetic)
- Cascade function drift detection (statistical keyword analysis)
- 10-word git status summaries
- Fast response (~200ms) and low cost (~$0.0002)
"""

import subprocess
import ast
import json
import os
from typing import Dict, Any, Optional
from pathlib import Path

class HaikuAuditor:
    """Lightweight auditor using Haiku for fast analysis"""
    
    def __init__(self):
        self.cascade_functions = [
            'parse_cascade_grammar',
            'predict_cascade', 
            'translate_taxonomy',
            'validate_oracle',
            'enhance_prediction'
        ]
        
        # Keywords that suggest regression/drift from pure cascade logic
        self.regression_keywords = [
            'sklearn', 'regression', 'linear_model', 'polynomial',
            'fit', 'predict', 'score', 'cross_val', 'gridsearch',
            'random_forest', 'svm', 'neural_network', 'deep_learning',
            'tensorflow', 'pytorch', 'keras', 'xgboost', 'lgb'
        ]
        
        print("🎯 Haiku Auditor initialized")
        print(f"   Monitoring {len(self.cascade_functions)} cascade functions")
        print(f"   Cost per call: ~$0.0002 | Response time: ~200ms")
    
    def summarize_git_diff(self) -> Dict[str, Any]:
        """Categorize uncommitted changes using git diff analysis"""
        try:
            # Get git diff statistics
            diff_result = subprocess.run(
                ['git', 'diff', '--stat'], 
                capture_output=True, text=True, cwd=Path.cwd()
            )
            
            if diff_result.returncode != 0:
                return {"error": "Not a git repository or no changes"}
            
            diff_output = diff_result.stdout.strip()
            
            if not diff_output:
                return {
                    "category": "none",
                    "risk": "none",
                    "summary": "No uncommitted changes detected"
                }
            
            # Analyze diff content for categorization
            category, risk = self._categorize_diff(diff_output)
            
            return {
                "category": category,
                "risk": risk,
                "summary": f"Git diff shows {category} changes with {risk} risk",
                "raw_diff": diff_output
            }
            
        except Exception as e:
            return {"error": f"Git diff failed: {str(e)}"}
    
    def _categorize_diff(self, diff_output: str) -> tuple[str, str]:
        """Categorize git diff into risk categories"""
        lines = diff_output.lower().split('\n')
        
        # Critical files check
        critical_files = ['oracle_core.py', 'invariants.py', 'production_oracle.py']
        has_critical = any(any(cf in line for cf in critical_files) for line in lines)
        
        # Guard-related changes
        has_guards = any('@guard' in line or 'guard.register' in line for line in lines)
        
        # Logic changes (function definitions, control flow)
        has_logic = any(any(keyword in line for keyword in ['def ', 'class ', 'if ', 'for ', 'while ']) for line in lines)
        
        # Determine category and risk
        if has_critical and has_guards:
            return "critical", "high"
        elif has_critical:
            return "guards", "medium"  
        elif has_logic:
            return "logic", "medium"
        else:
            return "cosmetic", "low"
    
    def check_drift(self, file_path: str = 'oracle_core.py') -> str:
        """Check if cascade functions contain regression keywords"""
        try:
            full_path = Path(file_path)
            if not full_path.exists():
                return f"❌ File {file_path} not found"
            
            with open(full_path, 'r') as f:
                content = f.read()
            
            try:
                tree = ast.parse(content)
            except SyntaxError as e:
                return f"❌ Syntax error in {file_path}: {e}"
            
            # Check each cascade function for regression keywords
            drift_detected = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    if node.name in self.cascade_functions:
                        func_text = ast.get_source_segment(content, node) or ""
                        
                        # Check for regression keywords
                        found_keywords = [
                            keyword for keyword in self.regression_keywords
                            if keyword in func_text.lower()
                        ]
                        
                        if found_keywords:
                            drift_detected.append({
                                'function': node.name,
                                'keywords': found_keywords,
                                'line': node.lineno
                            })
            
            if drift_detected:
                # Format warnings
                warnings = []
                for drift in drift_detected:
                    keywords_str = ', '.join(drift['keywords'][:3])  # Show first 3
                    warnings.append(f"⚠️ {drift['function']}:{drift['line']} contains '{keywords_str}'")
                
                return '\n'.join(warnings)
            
            return "✅ No drift detected - Pure cascade logic maintained"
            
        except Exception as e:
            return f"❌ Drift check failed: {str(e)}"
    
    def git_status_summary(self) -> str:
        """Generate 10-word git status summary"""
        try:
            status_result = subprocess.run(
                ['git', 'status', '--porcelain'], 
                capture_output=True, text=True, cwd=Path.cwd()
            )
            
            if status_result.returncode != 0:
                return "Not a git repository"
            
            status_lines = status_result.stdout.strip().split('\n')
            status_lines = [line for line in status_lines if line.strip()]
            
            if not status_lines:
                return "✅ Working tree clean, no changes detected whatsoever"
            
            # Count changes by type
            modified = sum(1 for line in status_lines if line.startswith(' M'))
            added = sum(1 for line in status_lines if line.startswith('A '))
            untracked = sum(1 for line in status_lines if line.startswith('??'))
            deleted = sum(1 for line in status_lines if line.startswith(' D'))
            
            # Check for critical files
            critical_files = ['oracle_core.py', 'invariants.py', 'production_oracle.py']
            has_critical = any(any(cf in line for cf in critical_files) for line in status_lines)
            
            # Generate summary
            summary_parts = []
            
            if modified:
                summary_parts.append(f"{modified} modified")
            if added:
                summary_parts.append(f"{added} staged")
            if untracked:
                summary_parts.append(f"{untracked} untracked")
            if deleted:
                summary_parts.append(f"{deleted} deleted")
            
            file_summary = ', '.join(summary_parts) if summary_parts else "changes"
            
            if has_critical:
                return f"⚠️ {file_summary}, critical files affected, review needed"
            else:
                return f"✅ {file_summary}, no critical files affected"
                
        except Exception as e:
            return f"❌ Git status failed: {str(e)}"
    
    def quick_audit(self) -> Dict[str, Any]:
        """Run complete quick audit"""
        print("\n🔍 HAIKU QUICK AUDIT")
        print("=" * 25)
        
        results = {
            'timestamp': subprocess.run(['date'], capture_output=True, text=True).stdout.strip(),
            'status_summary': self.git_status_summary(),
            'diff_analysis': self.summarize_git_diff(),
            'drift_check': {}
        }
        
        print(f"📊 Status: {results['status_summary']}")
        
        # Check drift in critical files
        critical_files = ['oracle_core.py', 'invariants.py', 'production_simple.py']
        for file_path in critical_files:
            if Path(file_path).exists():
                drift_result = self.check_drift(file_path)
                results['drift_check'][file_path] = drift_result
                print(f"🔍 Drift [{file_path}]: {drift_result}")
        
        # Diff analysis
        diff = results['diff_analysis']
        if 'error' not in diff:
            print(f"📈 Changes: {diff['category']} risk={diff['risk']}")
        else:
            print(f"📈 Changes: {diff['error']}")
        
        # Escalation recommendation
        escalate = self._should_escalate(results)
        results['escalate_to_opus'] = escalate
        
        if escalate:
            print("🚨 Recommendation: Escalate to full audit system")
        else:
            print("✅ No escalation needed - Changes appear safe")
        
        return results
    
    def _should_escalate(self, results: Dict[str, Any]) -> bool:
        """Determine if should escalate to full Opus audit"""
        
        # Escalate if high risk diff
        diff = results['diff_analysis']
        if isinstance(diff, dict) and diff.get('risk') == 'high':
            return True
        
        # Escalate if drift detected
        for file_path, drift_result in results['drift_check'].items():
            if '⚠️' in str(drift_result):
                return True
        
        # Escalate if critical files in status
        if '⚠️' in results['status_summary']:
            return True
        
        return False
    
    def cli_interface(self, command: str) -> str:
        """Command line interface for quick calls"""
        
        if command == "diff":
            result = self.summarize_git_diff()
            if 'error' in result:
                return result['error']
            return f"{result['category']} changes, {result['risk']} risk"
        
        elif command == "drift":
            return self.check_drift('oracle_core.py')
        
        elif command == "status":
            return self.git_status_summary()
        
        elif command == "audit":
            results = self.quick_audit()
            return json.dumps(results, indent=2)
        
        else:
            return f"Unknown command: {command}. Use: diff, drift, status, audit"

def demo_haiku_audit():
    """Demonstrate Haiku auditor capabilities"""
    print("🎯 HAIKU AUDITOR DEMO")
    print("=" * 30)
    
    auditor = HaikuAuditor()
    
    # Quick audit
    results = auditor.quick_audit()
    
    print(f"\n📋 AUDIT RESULTS:")
    print(f"   Escalation needed: {results['escalate_to_opus']}")
    print(f"   Files checked: {len(results['drift_check'])}")
    
    return results

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # CLI mode
        auditor = HaikuAuditor()
        command = sys.argv[1]
        print(auditor.cli_interface(command))
    else:
        # Demo mode
        demo_haiku_audit()