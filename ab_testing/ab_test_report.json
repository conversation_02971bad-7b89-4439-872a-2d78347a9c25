{"test_name": "ab_test_1754676745", "variants": [{"variant_name": "current_xgboost", "accuracy": 0.9701492537313433, "precision": 0.9732914375490967, "recall": 0.9701492537313433, "f1_score": 0.9706738416759737, "latency_ms": 1.9431114196777344, "sample_size": 67, "predictions": [0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0], "confidence_interval": [0.8975344443664799, 0.991775305161102]}, {"variant_name": "alternative_gradient_boosting", "accuracy": 0.9701492537313433, "precision": 0.9732914375490967, "recall": 0.9701492537313433, "f1_score": 0.9706738416759737, "latency_ms": 1.394033432006836, "sample_size": 67, "predictions": [0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0, 0, 0, 1, 0, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0], "confidence_interval": [0.8975344443664799, 0.991775305161102]}], "statistical_comparisons": [{"variant_a": "current_xgboost", "variant_b": "alternative_gradient_boosting", "metric": "accuracy", "effect_size": 0.0, "effect_size_interpretation": "negligible", "p_value": 1.0, "statistically_significant": false, "confidence_interval": [-0.05762654217659679, 0.05762654217659679], "test_statistic": 0, "test_method": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "power": 0.0, "sample_size_adequate": true}, {"variant_a": "current_xgboost", "variant_b": "alternative_gradient_boosting", "metric": "f1_score", "effect_size": 0.0, "effect_size_interpretation": "negligible", "p_value": 1.0, "statistically_significant": false, "confidence_interval": [-0.05713338268550952, 0.05713338268550952], "test_statistic": 0.0, "test_method": "t-test", "power": 0.0, "sample_size_adequate": true}, {"variant_a": "current_xgboost", "variant_b": "alternative_gradient_boosting", "metric": "latency_ms", "effect_size": 0.5490779876708984, "effect_size_interpretation": "negligible", "p_value": 0.01, "statistically_significant": true, "confidence_interval": [0.43926239013671875, 0.6588935852050781], "test_statistic": 0.2825766871165644, "test_method": "relative_difference", "power": 0.8, "sample_size_adequate": true}], "winner": null, "deployment_recommendation": "INCONCLUSIVE", "confidence_level": 0.95, "total_sample_size": 67, "test_duration_seconds": 0.027322053909301758, "recommendations": ["LOW STATISTICAL POWER detected in 2 comparisons. Results may miss true differences between variants."]}