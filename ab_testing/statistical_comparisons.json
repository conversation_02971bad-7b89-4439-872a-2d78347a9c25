[{"variant_a": "current_xgboost", "variant_b": "alternative_gradient_boosting", "metric": "accuracy", "effect_size": 0.0, "effect_size_interpretation": "negligible", "p_value": 1.0, "statistically_significant": false, "confidence_interval": [-0.05762654217659679, 0.05762654217659679], "test_statistic": 0, "test_method": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "power": 0.0, "sample_size_adequate": true}, {"variant_a": "current_xgboost", "variant_b": "alternative_gradient_boosting", "metric": "f1_score", "effect_size": 0.0, "effect_size_interpretation": "negligible", "p_value": 1.0, "statistically_significant": false, "confidence_interval": [-0.05713338268550952, 0.05713338268550952], "test_statistic": 0.0, "test_method": "t-test", "power": 0.0, "sample_size_adequate": true}, {"variant_a": "current_xgboost", "variant_b": "alternative_gradient_boosting", "metric": "latency_ms", "effect_size": 0.5490779876708984, "effect_size_interpretation": "negligible", "p_value": 0.01, "statistically_significant": true, "confidence_interval": [0.43926239013671875, 0.6588935852050781], "test_statistic": 0.2825766871165644, "test_method": "relative_difference", "power": 0.8, "sample_size_adequate": true}]