{"analysis_timestamp": "2025-08-07T14:01:42.927042", "pumping_lemma_validation": {"total_patterns": 29, "context_free_patterns": 27, "non_context_free_patterns": 2, "patterns_too_short": 8, "grammar_is_context_free": true, "estimated_pumping_length": 3, "pda_implementation_feasible": true}, "test_results": [{"pattern_signature": "CONSOLIDATION → EXPANSION → REDELIVERY", "event_sequence": ["CONSOLIDATION", "EXPANSION", "REDELIVERY"], "is_context_free": true, "pumping_length": 3, "valid_decompositions_count": 8, "test_details": {"sequence_length": 3, "decompositions_tested": 16, "valid_decompositions_count": 8, "pumping_tests_passed": 8}, "failure_reason": null}, {"pattern_signature": "FPFVG → FPFVG → FPFVG", "event_sequence": ["FPFVG", "FPFVG", "FPFVG"], "is_context_free": true, "pumping_length": 3, "valid_decompositions_count": 12, "test_details": {"sequence_length": 3, "decompositions_tested": 16, "valid_decompositions_count": 12, "pumping_tests_passed": 12}, "failure_reason": null}, {"pattern_signature": "CONSOLIDATION → FPFVG", "event_sequence": ["CONSOLIDATION", "FPFVG"], "is_context_free": true, "pumping_length": 2, "valid_decompositions_count": 0, "test_details": {"reason": "too_short"}, "failure_reason": "Pattern too short for pumping lemma test"}, {"pattern_signature": "FPFVG → FPFVG → FPFVG → FPFVG", "event_sequence": ["FPFVG", "FPFVG", "FPFVG", "FPFVG"], "is_context_free": true, "pumping_length": 3, "valid_decompositions_count": 57, "test_details": {"sequence_length": 4, "decompositions_tested": 30, "valid_decompositions_count": 57, "pumping_tests_passed": 57}, "failure_reason": null}, {"pattern_signature": "REDELIVERY → FPFVG", "event_sequence": ["REDELIVERY", "FPFVG"], "is_context_free": true, "pumping_length": 2, "valid_decompositions_count": 0, "test_details": {"reason": "too_short"}, "failure_reason": "Pattern too short for pumping lemma test"}, {"pattern_signature": "FPFVG → INTERACTION", "event_sequence": ["FPFVG", "INTERACTION"], "is_context_free": true, "pumping_length": 2, "valid_decompositions_count": 0, "test_details": {"reason": "too_short"}, "failure_reason": "Pattern too short for pumping lemma test"}, {"pattern_signature": "CONSOLIDATION → FPFVG [2-event]", "event_sequence": ["CONSOLIDATION", "FPFVG"], "is_context_free": true, "pumping_length": 2, "valid_decompositions_count": 0, "test_details": {"reason": "too_short"}, "failure_reason": "Pattern too short for pumping lemma test"}, {"pattern_signature": "REDELIVERY → FPFVG [2-event]", "event_sequence": ["REDELIVERY", "FPFVG"], "is_context_free": true, "pumping_length": 2, "valid_decompositions_count": 0, "test_details": {"reason": "too_short"}, "failure_reason": "Pattern too short for pumping lemma test"}, {"pattern_signature": "FPFVG → INTERACTION [2-event]", "event_sequence": ["FPFVG", "INTERACTION"], "is_context_free": true, "pumping_length": 2, "valid_decompositions_count": 0, "test_details": {"reason": "too_short"}, "failure_reason": "Pattern too short for pumping lemma test"}, {"pattern_signature": "FPFVG → FPFVG → FPFVG → FPFVG → FPFVG", "event_sequence": ["FPFVG", "FPFVG", "FPFVG", "FPFVG", "FPFVG"], "is_context_free": true, "pumping_length": 3, "valid_decompositions_count": 152, "test_details": {"sequence_length": 5, "decompositions_tested": 50, "valid_decompositions_count": 152, "pumping_tests_passed": 152}, "failure_reason": null}, {"pattern_signature": "FPFVG → EXPANSION_HIGH → CONSOLIDATION", "event_sequence": ["FPFVG", "EXPANSION_HIGH", "CONSOLIDATION"], "is_context_free": true, "pumping_length": 3, "valid_decompositions_count": 12, "test_details": {"sequence_length": 3, "decompositions_tested": 16, "valid_decompositions_count": 12, "pumping_tests_passed": 12}, "failure_reason": null}, {"pattern_signature": "FPFVG → EXPANSION_HIGH → CONSOLIDATION → EXPANSION_LOW", "event_sequence": ["FPFVG", "EXPANSION_HIGH", "CONSOLIDATION", "EXPANSION_LOW"], "is_context_free": true, "pumping_length": 3, "valid_decompositions_count": 57, "test_details": {"sequence_length": 4, "decompositions_tested": 30, "valid_decompositions_count": 57, "pumping_tests_passed": 57}, "failure_reason": null}, {"pattern_signature": "FPFVG → EXPANSION_HIGH → CONSOLIDATION → EXPANSION_LOW → CONSOLIDATION", "event_sequence": ["FPFVG", "EXPANSION_HIGH", "CONSOLIDATION", "EXPANSION_LOW", "CONSOLIDATION"], "is_context_free": true, "pumping_length": 3, "valid_decompositions_count": 152, "test_details": {"sequence_length": 5, "decompositions_tested": 50, "valid_decompositions_count": 152, "pumping_tests_passed": 152}, "failure_reason": null}, {"pattern_signature": "EXPANSION_HIGH → CONSOLIDATION → EXPANSION_LOW", "event_sequence": ["EXPANSION_HIGH", "CONSOLIDATION", "EXPANSION_LOW"], "is_context_free": true, "pumping_length": 3, "valid_decompositions_count": 12, "test_details": {"sequence_length": 3, "decompositions_tested": 16, "valid_decompositions_count": 12, "pumping_tests_passed": 12}, "failure_reason": null}, {"pattern_signature": "EXPANSION_HIGH → CONSOLIDATION → EXPANSION_LOW → CONSOLIDATION", "event_sequence": ["EXPANSION_HIGH", "CONSOLIDATION", "EXPANSION_LOW", "CONSOLIDATION"], "is_context_free": true, "pumping_length": 3, "valid_decompositions_count": 57, "test_details": {"sequence_length": 4, "decompositions_tested": 30, "valid_decompositions_count": 57, "pumping_tests_passed": 57}, "failure_reason": null}, {"pattern_signature": "CONSOLIDATION → EXPANSION_LOW → CONSOLIDATION", "event_sequence": ["CONSOLIDATION", "EXPANSION_LOW", "CONSOLIDATION"], "is_context_free": true, "pumping_length": 3, "valid_decompositions_count": 12, "test_details": {"sequence_length": 3, "decompositions_tested": 16, "valid_decompositions_count": 12, "pumping_tests_passed": 12}, "failure_reason": null}, {"pattern_signature": "CONSOLIDATION → CONSOLIDATION → EXPANSION", "event_sequence": ["CONSOLIDATION", "CONSOLIDATION", "EXPANSION"], "is_context_free": true, "pumping_length": 3, "valid_decompositions_count": 12, "test_details": {"sequence_length": 3, "decompositions_tested": 16, "valid_decompositions_count": 12, "pumping_tests_passed": 12}, "failure_reason": null}, {"pattern_signature": "CONSOLIDATION → CONSOLIDATION → EXPANSION → REDELIVERY", "event_sequence": ["CONSOLIDATION", "CONSOLIDATION", "EXPANSION", "REDELIVERY"], "is_context_free": true, "pumping_length": 3, "valid_decompositions_count": 48, "test_details": {"sequence_length": 4, "decompositions_tested": 30, "valid_decompositions_count": 48, "pumping_tests_passed": 48}, "failure_reason": null}, {"pattern_signature": "CONSOLIDATION → EXPANSION → REDELIVERY → EXPANSION_HIGH", "event_sequence": ["CONSOLIDATION", "EXPANSION", "REDELIVERY", "EXPANSION_HIGH"], "is_context_free": true, "pumping_length": 3, "valid_decompositions_count": 47, "test_details": {"sequence_length": 4, "decompositions_tested": 30, "valid_decompositions_count": 47, "pumping_tests_passed": 47}, "failure_reason": null}, {"pattern_signature": "CONSOLIDATION → EXPANSION → REDELIVERY → EXPANSION_HIGH → REVERSAL", "event_sequence": ["CONSOLIDATION", "EXPANSION", "REDELIVERY", "EXPANSION_HIGH", "REVERSAL"], "is_context_free": true, "pumping_length": 3, "valid_decompositions_count": 122, "test_details": {"sequence_length": 5, "decompositions_tested": 50, "valid_decompositions_count": 122, "pumping_tests_passed": 122}, "failure_reason": null}, {"pattern_signature": "EXPANSION → REDELIVERY → EXPANSION_HIGH", "event_sequence": ["EXPANSION", "REDELIVERY", "EXPANSION_HIGH"], "is_context_free": true, "pumping_length": 3, "valid_decompositions_count": 7, "test_details": {"sequence_length": 3, "decompositions_tested": 16, "valid_decompositions_count": 7, "pumping_tests_passed": 7}, "failure_reason": null}, {"pattern_signature": "EXPANSION → REDELIVERY → EXPANSION_HIGH → REVERSAL", "event_sequence": ["EXPANSION", "REDELIVERY", "EXPANSION_HIGH", "REVERSAL"], "is_context_free": true, "pumping_length": 3, "valid_decompositions_count": 44, "test_details": {"sequence_length": 4, "decompositions_tested": 30, "valid_decompositions_count": 44, "pumping_tests_passed": 44}, "failure_reason": null}, {"pattern_signature": "REDELIVERY → EXPANSION_HIGH → REVERSAL", "event_sequence": ["REDELIVERY", "EXPANSION_HIGH", "REVERSAL"], "is_context_free": false, "pumping_length": 0, "valid_decompositions_count": 0, "test_details": {"sequence_length": 3, "decompositions_tested": 16, "valid_decompositions_count": 0, "pumping_tests_passed": 0}, "failure_reason": "No valid pumping decomposition found"}, {"pattern_signature": "EXPANSION_HIGH → REVERSAL", "event_sequence": ["EXPANSION_HIGH", "REVERSAL"], "is_context_free": true, "pumping_length": 2, "valid_decompositions_count": 0, "test_details": {"reason": "too_short"}, "failure_reason": "Pattern too short for pumping lemma test"}, {"pattern_signature": "CONSOLIDATION → EXPANSION → REDELIVERY → FPFVG", "event_sequence": ["CONSOLIDATION", "EXPANSION", "REDELIVERY", "FPFVG"], "is_context_free": true, "pumping_length": 3, "valid_decompositions_count": 47, "test_details": {"sequence_length": 4, "decompositions_tested": 30, "valid_decompositions_count": 47, "pumping_tests_passed": 47}, "failure_reason": null}, {"pattern_signature": "EXPANSION → REDELIVERY → FPFVG", "event_sequence": ["EXPANSION", "REDELIVERY", "FPFVG"], "is_context_free": true, "pumping_length": 3, "valid_decompositions_count": 7, "test_details": {"sequence_length": 3, "decompositions_tested": 16, "valid_decompositions_count": 7, "pumping_tests_passed": 7}, "failure_reason": null}, {"pattern_signature": "FPFVG → INTERACTION → EXPANSION_HIGH", "event_sequence": ["FPFVG", "INTERACTION", "EXPANSION_HIGH"], "is_context_free": true, "pumping_length": 3, "valid_decompositions_count": 12, "test_details": {"sequence_length": 3, "decompositions_tested": 16, "valid_decompositions_count": 12, "pumping_tests_passed": 12}, "failure_reason": null}, {"pattern_signature": "REDELIVERY → REDELIVERY → EXPANSION_LOW", "event_sequence": ["REDELIVERY", "REDELIVERY", "EXPANSION_LOW"], "is_context_free": false, "pumping_length": 0, "valid_decompositions_count": 0, "test_details": {"sequence_length": 3, "decompositions_tested": 16, "valid_decompositions_count": 0, "pumping_tests_passed": 0}, "failure_reason": "No valid pumping decomposition found"}, {"pattern_signature": "EXPANSION_HIGH → REVERSAL [2-event]", "event_sequence": ["EXPANSION_HIGH", "REVERSAL"], "is_context_free": true, "pumping_length": 2, "valid_decompositions_count": 0, "test_details": {"reason": "too_short"}, "failure_reason": "Pattern too short for pumping lemma test"}], "mathematical_proof": {"method": "Pumping Lemma for Context-Free Languages", "rigor_level": "Formal mathematical proof", "conclusion": "Context-free"}}