"""Density-Adaptive Renormalization Group (RG) Scaler - Critical Linchpin Component

Implementation of Gemini's key discovery: s(d) = 15 - 5*log₁₀(d)
Inverse correlation coefficient: -0.9197 between cascade density and optimal detection scale

This module bridges event density measurement to optimal Hawkes analysis scale,
ensuring the oracle always views market data with the correct "magnification."

Mathematical Foundation: Experiments 5 & 6 from project_oracle_research_report.md
Domain Constraints: Preserves existing RG graphs framework (24% theory weight)
"""

import numpy as np
import math
from decimal import Decimal, getcontext
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
import logging

# Import constraints and existing RG system
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core_predictor'))
from constraints import RGConstants, BusinessRules, ValidationRules

# Set high precision for calculations
getcontext().prec = 50

@dataclass
class DensityMeasurement:
    """Event density measurement result"""
    raw_density: Decimal
    normalized_density: Decimal
    time_window_minutes: int
    event_count: int
    density_classification: str  # 'low', 'medium', 'high', 'extreme'

@dataclass
class ScalingResult:
    """RG scaling calculation result"""
    optimal_scale: Decimal
    density_input: Decimal
    formula_output: Decimal
    bounded_scale: Decimal
    confidence_score: float
    scaling_rationale: str

class DensityAdaptiveRGScaler:
    """
    Production-ready Density-Adaptive RG Scaler
    
    Implements Gemini's validated formula: s(d) = 15 - 5*log₁₀(d)
    Preserves existing RG graphs integration (24% theory weight)
    Enforces mathematical constraints from domain validation
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize with validated constants and configuration"""
        
        # Load mathematical constants (IMMUTABLE)
        self.scaling_coefficient_a = RGConstants.SCALING_COEFFICIENT_A  # 15
        self.scaling_coefficient_b = RGConstants.SCALING_COEFFICIENT_B  # -5
        self.min_scale = RGConstants.MIN_SCALE  # 5 minutes
        self.max_scale = RGConstants.MAX_SCALE  # 15 minutes
        self.percolation_critical = RGConstants.PERCOLATION_CRITICAL  # 0.4565
        
        # Density classification thresholds
        self.density_thresholds = {
            'low': Decimal('0.1'),      # < 0.1 events/min
            'medium': Decimal('1.0'),   # 0.1 - 1.0 events/min  
            'high': Decimal('10.0'),    # 1.0 - 10.0 events/min
            'extreme': Decimal('100.0') # > 10.0 events/min
        }
        
        # Configuration
        self.config = config or {}
        self.enable_validation = self.config.get('enable_validation', True)
        self.log_level = self.config.get('log_level', 'INFO')
        
        # Set up logging
        logging.basicConfig(level=getattr(logging, self.log_level))
        self.logger = logging.getLogger(__name__)
        
        self.logger.info("🔬 DENSITY-ADAPTIVE RG SCALER: Initialized")
        self.logger.info(f"   Formula: s(d) = {self.scaling_coefficient_a} + {self.scaling_coefficient_b}*log₁₀(d)")
        self.logger.info(f"   Scale bounds: [{self.min_scale}, {self.max_scale}] minutes")
        self.logger.info(f"   Percolation critical: {self.percolation_critical}")
        
        # Validate initialization
        if self.enable_validation:
            self._validate_initialization()
    
    def _validate_initialization(self) -> None:
        """Validate mathematical constants during initialization"""
        
        # Test known fixed points from RG analysis
        test_cases = [
            (Decimal('1.0'), Decimal('15')),    # density=1.0 → scale=15
            (Decimal('10.0'), Decimal('10')),   # density=10.0 → scale=10  
            (Decimal('100.0'), Decimal('5'))    # density=100.0 → scale=5
        ]
        
        for density, expected_scale in test_cases:
            calculated_scale = self._calculate_raw_scale(density)
            if abs(calculated_scale - expected_scale) > Decimal('0.001'):
                raise ValueError(
                    f"RG scaling validation failed: density={density}, "
                    f"expected={expected_scale}, calculated={calculated_scale}"
                )
        
        self.logger.info("✅ RG Scaler mathematical validation passed")
    
    def compute_event_density(self, events: List[Dict], time_window_minutes: int = 60) -> DensityMeasurement:
        """
        Compute event density from market data
        
        Args:
            events: List of event dictionaries with 'timestamp' and event data
            time_window_minutes: Time window for density calculation
            
        Returns:
            DensityMeasurement: Comprehensive density analysis
        """
        
        if not events:
            return DensityMeasurement(
                raw_density=Decimal('0'),
                normalized_density=Decimal('0.1'),  # Minimum to avoid log(0)
                time_window_minutes=time_window_minutes,
                event_count=0,
                density_classification='low'
            )
        
        # Count events in time window
        event_count = len(events)
        raw_density = Decimal(str(event_count)) / Decimal(str(time_window_minutes))
        
        # Normalize density (minimum 0.1 to avoid log domain issues)
        normalized_density = max(raw_density, Decimal('0.1'))
        
        # Classify density
        if normalized_density < self.density_thresholds['low']:
            classification = 'low'
        elif normalized_density < self.density_thresholds['medium']:
            classification = 'medium'
        elif normalized_density < self.density_thresholds['high']:
            classification = 'high'
        else:
            classification = 'extreme'
        
        self.logger.debug(f"Event density: {event_count} events / {time_window_minutes} min = {raw_density:.4f}")
        self.logger.debug(f"Normalized density: {normalized_density:.4f} ({classification})")
        
        return DensityMeasurement(
            raw_density=raw_density,
            normalized_density=normalized_density,
            time_window_minutes=time_window_minutes,
            event_count=event_count,
            density_classification=classification
        )
    
    def _calculate_raw_scale(self, density: Decimal) -> Decimal:
        """
        Calculate raw scale using Gemini's formula: s(d) = 15 - 5*log₁₀(d)
        
        Args:
            density: Event density (events per minute)
            
        Returns:
            Decimal: Raw scale value (before bounds enforcement)
        """
        
        if density <= 0:
            raise ValueError(f"Invalid density for RG scaling: {density}")
        
        # Calculate log₁₀(density) with high precision
        log_density = Decimal(str(math.log10(float(density))))
        
        # Apply Gemini's formula: s(d) = a + b*log₁₀(d) 
        raw_scale = self.scaling_coefficient_a + self.scaling_coefficient_b * log_density
        
        return raw_scale
    
    def calculate_optimal_scale(self, density_measurement: DensityMeasurement) -> ScalingResult:
        """
        Calculate optimal analysis scale using density-adaptive RG formula
        
        Args:
            density_measurement: Result from compute_event_density()
            
        Returns:
            ScalingResult: Complete scaling analysis with bounds and validation
        """
        
        density = density_measurement.normalized_density
        
        # Calculate raw scale using Gemini's formula
        raw_scale = self._calculate_raw_scale(density)
        
        # Enforce bounds (critical for system stability)
        bounded_scale = max(self.min_scale, min(self.max_scale, raw_scale))
        
        # Calculate confidence score based on how close we are to bounds
        if raw_scale == bounded_scale:
            confidence_score = 1.0  # Perfect - no bounds enforcement needed
        else:
            # Reduced confidence when bounds enforcement is required
            bounds_distance = min(
                abs(float(raw_scale - self.min_scale)),
                abs(float(raw_scale - self.max_scale))
            )
            max_bounds_distance = float(self.max_scale - self.min_scale)
            confidence_score = max(0.5, 1.0 - bounds_distance / max_bounds_distance)
        
        # Generate scaling rationale
        if density_measurement.density_classification == 'low':
            rationale = f"Low density ({density:.3f}) → Coarse scale ({bounded_scale}) for broad pattern detection"
        elif density_measurement.density_classification == 'high':
            rationale = f"High density ({density:.3f}) → Fine scale ({bounded_scale}) for precise timing"
        elif density_measurement.density_classification == 'extreme':
            rationale = f"Extreme density ({density:.3f}) → Minimum scale ({bounded_scale}) for micro-pattern analysis"
        else:
            rationale = f"Medium density ({density:.3f}) → Balanced scale ({bounded_scale}) for standard analysis"
        
        if raw_scale != bounded_scale:
            rationale += f" [bounds enforced: {raw_scale:.2f} → {bounded_scale:.2f}]"
        
        self.logger.info(f"RG Scaling: {rationale}")
        self.logger.debug(f"   Confidence: {confidence_score:.3f}")
        
        return ScalingResult(
            optimal_scale=bounded_scale,
            density_input=density,
            formula_output=raw_scale,
            bounded_scale=bounded_scale,
            confidence_score=confidence_score,
            scaling_rationale=rationale
        )
    
    def transform_events(self, events: List[Dict], optimal_scale: Decimal) -> List[Dict]:
        """
        Transform event data to optimal time-binning scale
        
        Args:
            events: Raw event data
            optimal_scale: Optimal scale from calculate_optimal_scale()
            
        Returns:
            List[Dict]: Events binned at optimal scale
        """
        
        if not events:
            return []
        
        scale_minutes = float(optimal_scale)
        
        # Group events by time bins
        time_bins = {}
        for event in events:
            # Parse timestamp (assumes format like "10:30" or similar)
            timestamp = event.get('timestamp', '00:00')
            try:
                time_parts = timestamp.split(':')
                total_minutes = int(time_parts[0]) * 60 + int(time_parts[1])
            except (ValueError, IndexError):
                # Skip events with invalid timestamps
                continue
            
            # Assign to time bin
            bin_start = (total_minutes // scale_minutes) * scale_minutes
            bin_key = f"{int(bin_start // 60):02d}:{int(bin_start % 60):02d}"
            
            if bin_key not in time_bins:
                time_bins[bin_key] = []
            
            time_bins[bin_key].append(event)
        
        # Create binned events
        binned_events = []
        for bin_timestamp, bin_events in sorted(time_bins.items()):
            
            # Aggregate events in bin (preserve most important data)
            aggregated_event = {
                'timestamp': bin_timestamp,
                'event_count': len(bin_events),
                'scale_minutes': scale_minutes,
                'events': bin_events  # Preserve original events
            }
            
            # Aggregate numerical values if present
            if bin_events and 'price_level' in bin_events[0]:
                prices = [e.get('price_level', 0) for e in bin_events]
                aggregated_event.update({
                    'price_min': min(prices),
                    'price_max': max(prices),
                    'price_mean': sum(prices) / len(prices),
                    'price_volatility': np.std(prices) if len(prices) > 1 else 0
                })
            
            binned_events.append(aggregated_event)
        
        self.logger.debug(f"Event binning: {len(events)} → {len(binned_events)} at {scale_minutes}min scale")
        
        return binned_events
    
    def integrate_with_rg_graphs(self, scaling_result: ScalingResult, 
                                existing_rg_component: Any) -> Dict[str, Any]:
        """
        Integrate adaptive scaling with existing RG graphs component
        Preserves 24% theory weight in multi-theory framework
        
        Args:
            scaling_result: Result from calculate_optimal_scale()
            existing_rg_component: Existing RGGraphsComponent instance
            
        Returns:
            Dict: Enhanced RG analysis with adaptive scaling
        """
        
        integration_result = {
            'adaptive_scale': float(scaling_result.optimal_scale),
            'scaling_confidence': scaling_result.confidence_score,
            'density_classification': scaling_result.scaling_rationale,
            'rg_enhancement_active': True
        }
        
        # If existing RG component is available, enhance it
        if existing_rg_component and hasattr(existing_rg_component, 'time_scales'):
            
            # Update RG component time scales with adaptive scale
            original_scales = existing_rg_component.time_scales.copy()
            adaptive_scale_int = int(scaling_result.optimal_scale)
            
            # Replace one of the existing scales with adaptive scale
            if adaptive_scale_int not in original_scales:
                if len(original_scales) >= 2:
                    # Replace the scale closest to our adaptive scale
                    closest_scale = min(original_scales, 
                                      key=lambda x: abs(x - adaptive_scale_int))
                    original_scales[original_scales.index(closest_scale)] = adaptive_scale_int
                else:
                    original_scales.append(adaptive_scale_int)
            
            integration_result.update({
                'original_rg_scales': existing_rg_component.time_scales,
                'enhanced_rg_scales': original_scales,
                'rg_percolation_critical': existing_rg_component.critical_threshold
            })
            
            self.logger.info(f"RG Integration: Enhanced scales {original_scales} with adaptive scale {adaptive_scale_int}")
        
        return integration_result
    
    def validate_scaling_performance(self, historical_data: List[Dict], 
                                   ground_truth_scales: List[float]) -> Dict[str, float]:
        """
        Validate adaptive scaling performance against historical data
        
        Args:
            historical_data: Historical event data with known optimal scales
            ground_truth_scales: Known optimal scales for validation
            
        Returns:
            Dict: Performance metrics
        """
        
        if len(historical_data) != len(ground_truth_scales):
            raise ValueError("Historical data and ground truth scales must have same length")
        
        scale_errors = []
        confidence_scores = []
        
        for data, true_scale in zip(historical_data, ground_truth_scales):
            
            # Calculate predicted scale
            density_measurement = self.compute_event_density(data.get('events', []))
            scaling_result = self.calculate_optimal_scale(density_measurement)
            
            predicted_scale = float(scaling_result.optimal_scale)
            
            # Calculate error
            scale_error = abs(predicted_scale - true_scale) / true_scale
            scale_errors.append(scale_error)
            confidence_scores.append(scaling_result.confidence_score)
        
        # Calculate performance metrics
        mean_error = np.mean(scale_errors)
        median_error = np.median(scale_errors)
        max_error = np.max(scale_errors)
        mean_confidence = np.mean(confidence_scores)
        
        performance = {
            'mean_relative_error': mean_error,
            'median_relative_error': median_error,
            'max_relative_error': max_error,
            'mean_confidence': mean_confidence,
            'accuracy_within_10pct': sum(1 for e in scale_errors if e < 0.1) / len(scale_errors),
            'accuracy_within_20pct': sum(1 for e in scale_errors if e < 0.2) / len(scale_errors)
        }
        
        self.logger.info(f"Scaling validation: {performance['accuracy_within_10pct']:.1%} within 10% accuracy")
        
        return performance


def create_production_rg_scaler(config: Optional[Dict] = None) -> DensityAdaptiveRGScaler:
    """
    Factory function to create production-ready RG scaler
    
    Args:
        config: Optional configuration overrides
        
    Returns:
        DensityAdaptiveRGScaler: Configured instance ready for use
    """
    
    production_config = {
        'enable_validation': True,
        'log_level': 'INFO',
        'bounds_enforcement': True,
        'precision_mode': True
    }
    
    if config:
        production_config.update(config)
    
    return DensityAdaptiveRGScaler(production_config)


if __name__ == "__main__":
    """
    Test the RG Scaler with validation scenarios
    """
    
    print("🔬 DENSITY-ADAPTIVE RG SCALER: Testing & Validation")
    print("=" * 60)
    
    # Create scaler instance
    scaler = create_production_rg_scaler({'log_level': 'DEBUG'})
    
    # Corrected test scenarios based on actual formula behavior: s(d) = 15 - 5*log₁₀(d)
    test_scenarios = [
        {
            'name': 'Low Density → Bounded to Max Scale',
            'events': [{'timestamp': f'10:{30+i*10}', 'price_level': 23500} for i in range(6)],  # 6 events = 0.1 events/min
            'expected_scale_range': (14.5, 15.0),  # Bounded to max scale
            'bounds_enforced': True
        },
        {
            'name': 'Medium Density → Natural Scale',
            'events': [{'timestamp': f'10:{30+i//10}', 'price_level': 23500+i} for i in range(600)],  # 600 events = 10.0 events/min
            'expected_scale_range': (9.5, 10.5),  # Natural scale ~10
            'bounds_enforced': False
        },
        {
            'name': 'High Density → Natural Fine Scale',
            'events': [{'timestamp': f'10:{30+i//50}', 'price_level': 23500+i} for i in range(3000)],  # 3000 events = 50.0 events/min
            'expected_scale_range': (6.0, 7.0),  # Natural scale ~6.5
            'bounds_enforced': False
        }
    ]
    
    all_tests_passed = True
    
    for scenario in test_scenarios:
        print(f"\n📊 Testing: {scenario['name']}")
        
        # Compute density
        density = scaler.compute_event_density(scenario['events'])
        print(f"   Event density: {density.normalized_density:.3f} events/min ({density.density_classification})")
        
        # Calculate optimal scale
        scaling = scaler.calculate_optimal_scale(density)
        print(f"   Optimal scale: {scaling.optimal_scale} minutes")
        print(f"   Confidence: {scaling.confidence_score:.3f}")
        print(f"   Rationale: {scaling.scaling_rationale}")
        
        # Validate scale is in expected range
        scale_value = float(scaling.optimal_scale)
        expected_min, expected_max = scenario['expected_scale_range']
        
        if expected_min <= scale_value <= expected_max:
            print(f"   ✅ PASS: Scale {scale_value} within expected range [{expected_min}, {expected_max}]")
        else:
            print(f"   ❌ FAIL: Scale {scale_value} outside expected range [{expected_min}, {expected_max}]")
            all_tests_passed = False
        
        # Test event transformation
        transformed = scaler.transform_events(scenario['events'], scaling.optimal_scale)
        print(f"   Event binning: {len(scenario['events'])} → {len(transformed)} bins")
    
    print("\n" + "=" * 60)
    
    if all_tests_passed:
        print("🎉 All RG Scaler tests passed!")
        print("✅ Ready for integration with Hawkes Engine")
        print("✅ Gemini's density-adaptive formula validated")
        print("✅ Mathematical constraints preserved")
    else:
        print("🚨 RG Scaler validation failed!")
        print("❌ Review test scenarios and fix issues before proceeding")
        exit(1)
