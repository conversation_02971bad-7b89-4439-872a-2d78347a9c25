# Processing Compartments DAG Configuration
# Defines dependencies, gates, and execution order for compartments

compartments:
  lvl1_enhance:
    description: "Transform Level-1 JSONs to enhanced dual-layer format"
    dependencies: []
    gates: {}
    
  htf_context:
    description: "Generate Higher Time Frame context for enhanced sessions"
    dependencies: ["lvl1_enhance"]
    gates: {}
    
  ml_update:
    description: "Train/update ML models with acceptance gates"
    dependencies: ["lvl1_enhance"]
    gates:
      real_non_cascades_min: 30
      temporal_cv_std_max: 0.04
      out_of_time_accuracy_min: 0.914
      
  calibration:
    description: "Calibrate Hawkes/VQE parameters with validation"
    dependencies: ["ml_update"]
    gates:
      hawkes_mae_max: 0.15
      invariants_pass: true
      
  predict:
    description: "Run Three-Oracle predictions with subprocess isolation"
    dependencies: ["calibration"]
    gates:
      health_check_pass: true
      latency_sli_max_ms: 5000

  accuracy_validation:
    description: "Comprehensive accuracy validation with statistical testing"
    dependencies: ["ml_update"]
    gates:
      accuracy_baseline_min: 0.911
      statistical_significance: true
      temporal_stability_min: 0.90

  ab_testing:
    description: "A/B testing framework for model variant comparison"
    dependencies: ["accuracy_validation"]
    gates:
      deployment_recommendation: "DEPLOY"

  production_validation:
    description: "End-to-end production validation pipeline"
    dependencies: []
    gates:
      overall_status: "PASS"
      deployment_ready: true

# Predefined sequences for common workflows
sequences:
  data_only: ["lvl1_enhance", "htf_context"]
  ml_pipeline: ["lvl1_enhance", "ml_update", "calibration"]
  validation_pipeline: ["lvl1_enhance", "ml_update", "accuracy_validation", "ab_testing"]
  production_pipeline: ["production_validation"]
  full_pipeline: ["lvl1_enhance", "htf_context", "ml_update", "calibration", "predict"]
  
# Global configuration
config:
  artifacts_manifest: "artifacts_manifest.json"
  max_parallel: 1  # Sequential execution for now
  timeout_seconds: 300
