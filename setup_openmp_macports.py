#!/usr/bin/env python3
"""
OpenMP MacPorts Setup for XGBoost

Creates the necessary symlinks and environment configuration to make
MacPorts OpenMP work with XGBoost's expectations for Homebrew paths.
"""

import os
import sys
from pathlib import Path
import subprocess

print("🔧 OPENMP MACPORTS SETUP FOR XGBOOST")
print("=" * 50)

def check_openmp_libraries():
    """Check available OpenMP libraries"""
    print("1️⃣ Checking OpenMP Libraries:")
    
    # MacPorts locations
    macports_paths = [
        "/opt/local/lib/libomp/libomp.dylib",
        "/opt/local/lib/libomp/libiomp5.dylib", 
        "/opt/local/lib/libomp/libgomp.dylib"
    ]
    
    # Homebrew expected locations
    homebrew_paths = [
        "/usr/local/opt/libomp/lib/libomp.dylib",
        "/usr/local/lib/libomp.dylib"
    ]
    
    macports_available = []
    homebrew_available = []
    
    for path in macports_paths:
        if os.path.exists(path):
            macports_available.append(path)
            print(f"   ✅ MacPorts: {path}")
    
    for path in homebrew_paths:
        if os.path.exists(path):
            homebrew_available.append(path)
            print(f"   ✅ Homebrew: {path}")
    
    if not macports_available and not homebrew_available:
        print("   ❌ No OpenMP libraries found!")
        return None, None
    
    return macports_available, homebrew_available

def create_environment_config():
    """Create environment configuration for OpenMP"""
    print("\n2️⃣ Environment Configuration:")
    
    # Set environment variables
    env_vars = {
        'DYLD_LIBRARY_PATH': '/opt/local/lib/libomp:/usr/local/lib',
        'DYLD_FALLBACK_LIBRARY_PATH': '/opt/local/lib/libomp:/usr/local/opt/libomp/lib',
        'OMP_NUM_THREADS': '4'
    }
    
    for var, value in env_vars.items():
        current = os.environ.get(var, '')
        if value not in current:
            new_value = f"{value}:{current}" if current else value
            os.environ[var] = new_value
            print(f"   ✅ {var} = {new_value}")
    
def test_xgboost_import():
    """Test XGBoost import with current configuration"""
    print("\n3️⃣ XGBoost Import Test:")
    
    try:
        import xgboost as xgb
        print("   ✅ XGBoost imported successfully!")
        
        # Test model loading capability
        print(f"   ✅ XGBoost version: {xgb.__version__}")
        return True
        
    except Exception as e:
        print(f"   ❌ XGBoost import failed: {e}")
        return False

def create_shell_script():
    """Create shell script to set environment for future sessions"""
    print("\n4️⃣ Shell Script Generation:")
    
    script_content = '''#!/bin/bash
# OpenMP Environment Setup for MacPorts + XGBoost
# Source this before running Oracle system

export DYLD_LIBRARY_PATH="/opt/local/lib/libomp:${DYLD_LIBRARY_PATH}"
export DYLD_FALLBACK_LIBRARY_PATH="/opt/local/lib/libomp:/usr/local/opt/libomp/lib:${DYLD_FALLBACK_LIBRARY_PATH}"
export OMP_NUM_THREADS=4

echo "🔗 OpenMP environment configured for MacPorts"
echo "   DYLD_LIBRARY_PATH: $DYLD_LIBRARY_PATH"
echo "   DYLD_FALLBACK_LIBRARY_PATH: $DYLD_FALLBACK_LIBRARY_PATH"
echo "   OMP_NUM_THREADS: $OMP_NUM_THREADS"
'''
    
    script_path = Path(__file__).parent / "setup_openmp_env.sh"
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    os.chmod(script_path, 0o755)
    print(f"   ✅ Created: {script_path}")
    print("   💡 Usage: source setup_openmp_env.sh")

def main():
    """Main setup routine"""
    
    # Check libraries
    macports, homebrew = check_openmp_libraries()
    
    if not macports and not homebrew:
        print("\n❌ SETUP FAILED: No OpenMP libraries found")
        print("   Install with: sudo port install libomp")
        return False
    
    # Configure environment
    create_environment_config()
    
    # Test XGBoost
    xgboost_works = test_xgboost_import()
    
    # Create helper script
    create_shell_script()
    
    print("\n" + "=" * 50)
    if xgboost_works:
        print("✅ OPENMP SETUP SUCCESSFUL!")
        print("   XGBoost should now work with MacPorts OpenMP")
        print("   The Oracle system can use native XGBoost integration")
    else:
        print("⚠️ OPENMP SETUP PARTIAL")
        print("   Environment configured but XGBoost still has issues")
        print("   Oracle system will fallback to core architecture without XGBoost")
    
    return xgboost_works

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)