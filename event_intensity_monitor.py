#!/usr/bin/env python3
"""
Event Intensity Monitor - Sparse Data Fisher Information Replacement
===================================================================

Phase 1: Mathematical Foundation Replacement
Based on successful linguistic validation, replace Fisher Information Monitor
with Event Intensity Monitor that operates on sparse event grammar instead
of continuous probability density functions.

Key Mathematical Transformation:
OLD: F = E[(∂log L/∂θ)²] from continuous tick data
NEW: I_sparse = Σ(event_magnitude²/inter_event_interval) from recorded events

This maintains the crystallization detection capability while working with
your linguistically structured manual recordings.
"""

import numpy as np
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import logging

from event_grammar_analyzer import MarketEvent

@dataclass
class EventIntensityResult:
    """Results from sparse event intensity calculation"""
    intensity_score: float
    crystallization_detected: bool
    alert_level: str
    time_to_cascade_minutes: float
    dominant_event_pattern: str
    pattern_confidence: float
    inter_event_stats: Dict[str, float]
    linguistic_indicators: Dict[str, Any]

@dataclass
class EventCluster:
    """Cluster of events for intensity calculation"""
    events: List[MarketEvent]
    start_time: float
    end_time: float
    cluster_intensity: float
    dominant_pattern: str

class EventIntensityMonitor:
    """
    Event Intensity Monitor - Sparse Data Replacement for Fisher Information
    
    Computes crystallization readiness from event grammar patterns rather than
    continuous probability density functions. Maintains the same alert system
    as Fisher Monitor but operates on linguistically structured sparse data.
    """
    
    def __init__(self, 
                 intensity_threshold: float = 0.5,
                 crystallization_threshold: float = 1.0,
                 alert_thresholds: Dict[str, float] = None):
        """
        Initialize Event Intensity Monitor
        
        Args:
            intensity_threshold: Baseline intensity for normal market activity
            crystallization_threshold: Intensity level indicating crystallization
            alert_thresholds: Custom alert level thresholds
        """
        
        self.intensity_threshold = intensity_threshold
        self.crystallization_threshold = crystallization_threshold
        
        # Alert levels adapted from Fisher Monitor
        self.alert_thresholds = alert_thresholds or {
            'NORMAL': 0.3,          # Low event intensity
            'ELEVATED': 0.5,        # Moderate event clustering  
            'HIGH': 1.0,           # High event density
            'RED_ALERT': 1.5,       # Crystallization imminent
            'CRITICAL': 2.0         # Cascade execution likely
        }
        
        # Event intensity multipliers based on linguistic analysis
        self.event_intensity_weights = {
            'FPFVG': 2.0,           # High predictive value
            'REDELIVERY': 2.5,      # Very high cascade correlation
            'EXPANSION': 1.8,       # Strong momentum indicator
            'EXPANSION_HIGH': 2.2,  # Peak momentum
            'CONSOLIDATION': 1.0,   # Baseline reference
            'REVERSAL': 3.0,        # Rare but highly significant
            'INTERACTION': 1.5,     # Moderate significance
            'LIQUIDITY_GRAB': 2.0,  # High cascade potential
            'OPEN': 0.5,           # Session marker
            'UNKNOWN': 0.1         # Low weight for unclassified
        }
        
        # Pattern multipliers from linguistic validation
        self.pattern_multipliers = {
            'CONSOLIDATION → EXPANSION → REDELIVERY': 3.0,    # 100% success rate
            'FPFVG → REDELIVERY': 2.8,                       # High frequency + success
            'EXPANSION → REVERSAL': 2.5,                     # Strong momentum shift
            'CONSOLIDATION → FPFVG': 2.2,                    # Breakout pattern
            'REDELIVERY → FPFVG': 2.0                        # Cascade continuation
        }
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("⚡ EVENT INTENSITY MONITOR: Initialized for sparse data")
        self.logger.info(f"   Intensity Threshold: {intensity_threshold}")
        self.logger.info(f"   Crystallization Threshold: {crystallization_threshold}")
        self.logger.info(f"   Alert Levels: {len(self.alert_thresholds)}")
    
    def calculate_event_intensity(self, events: List[MarketEvent], 
                                time_window_minutes: float = 60.0) -> EventIntensityResult:
        """
        Calculate sparse event intensity using linguistic grammar
        
        Args:
            events: List of market events from session
            time_window_minutes: Time window for intensity calculation
            
        Returns:
            EventIntensityResult with intensity analysis
        """
        
        if not events:
            return self._create_default_result("No events provided")
        
        self.logger.info(f"⚡ CALCULATING EVENT INTENSITY: {len(events)} events")
        
        # Step 1: Calculate base event intensity
        base_intensity = self._calculate_base_intensity(events, time_window_minutes)
        
        # Step 2: Apply linguistic pattern multipliers
        pattern_intensity = self._calculate_pattern_intensity(events)
        
        # Step 3: Calculate inter-event dynamics
        inter_event_intensity = self._calculate_inter_event_intensity(events)
        
        # Step 4: Combine intensity components
        total_intensity = (base_intensity * 0.4 + 
                          pattern_intensity * 0.4 + 
                          inter_event_intensity * 0.2)
        
        self.logger.info(f"   Base Intensity: {base_intensity:.3f}")
        self.logger.info(f"   Pattern Intensity: {pattern_intensity:.3f}")  
        self.logger.info(f"   Inter-Event Intensity: {inter_event_intensity:.3f}")
        self.logger.info(f"   Total Intensity: {total_intensity:.3f}")
        
        # Step 5: Determine alert level and crystallization
        alert_level = self._determine_alert_level(total_intensity)
        crystallization_detected = total_intensity >= self.crystallization_threshold
        
        # Step 6: Estimate cascade timing
        time_to_cascade = self._estimate_cascade_timing(events, total_intensity)
        
        # Step 7: Identify dominant patterns
        dominant_pattern, pattern_confidence = self._identify_dominant_pattern(events)
        
        # Step 8: Calculate inter-event statistics
        inter_event_stats = self._calculate_inter_event_stats(events)
        
        # Step 9: Generate linguistic indicators
        linguistic_indicators = self._generate_linguistic_indicators(events, total_intensity)
        
        result = EventIntensityResult(
            intensity_score=total_intensity,
            crystallization_detected=crystallization_detected,
            alert_level=alert_level,
            time_to_cascade_minutes=time_to_cascade,
            dominant_event_pattern=dominant_pattern,
            pattern_confidence=pattern_confidence,
            inter_event_stats=inter_event_stats,
            linguistic_indicators=linguistic_indicators
        )
        
        # Log results
        self._log_intensity_results(result)
        
        return result
    
    def _calculate_base_intensity(self, events: List[MarketEvent], time_window: float) -> float:
        """
        Calculate base event intensity: I_sparse = Σ(event_magnitude²/inter_event_interval)
        
        This is the core mathematical replacement for Fisher Information
        """
        
        if len(events) < 2:
            return 0.0
        
        # Sort events by time
        sorted_events = sorted(events, key=lambda e: e.session_time_minutes)
        
        intensity_sum = 0.0
        
        for i in range(1, len(sorted_events)):
            current_event = sorted_events[i]
            previous_event = sorted_events[i-1]
            
            # Calculate inter-event interval (prevent division by zero)
            interval = max(0.1, current_event.session_time_minutes - previous_event.session_time_minutes)
            
            # Get event type weight
            event_weight = self.event_intensity_weights.get(current_event.event_type, 1.0)
            
            # Calculate weighted magnitude
            weighted_magnitude = current_event.magnitude * event_weight
            
            # Apply sparse Fisher Information formula
            event_intensity = (weighted_magnitude ** 2) / interval
            
            intensity_sum += event_intensity
        
        # Normalize by time window
        normalized_intensity = intensity_sum / time_window
        
        return normalized_intensity
    
    def _calculate_pattern_intensity(self, events: List[MarketEvent]) -> float:
        """Calculate intensity from linguistic pattern recognition"""
        
        if len(events) < 2:
            return 0.0
        
        pattern_intensity = 0.0
        
        # Build event type sequence
        event_types = [e.event_type for e in events]
        sequence_str = ' → '.join(event_types)
        
        # Check for known high-intensity patterns
        for pattern, multiplier in self.pattern_multipliers.items():
            if pattern in sequence_str:
                pattern_intensity = max(pattern_intensity, multiplier)
        
        # Additional pattern checks
        
        # FPFVG clustering (multiple FPFVGs indicate high intensity)
        fpfvg_count = event_types.count('FPFVG')
        if fpfvg_count >= 3:
            pattern_intensity = max(pattern_intensity, 2.5)
        elif fpfvg_count >= 2:
            pattern_intensity = max(pattern_intensity, 1.8)
        
        # Expansion sequences
        expansion_types = ['EXPANSION', 'EXPANSION_HIGH', 'EXPANSION_LOW']
        expansion_count = sum(1 for et in event_types if et in expansion_types)
        if expansion_count >= 3:
            pattern_intensity = max(pattern_intensity, 2.2)
        
        # Reversal presence (rare but high impact)
        if 'REVERSAL' in event_types:
            pattern_intensity = max(pattern_intensity, 2.8)
        
        # Consolidation breakout patterns
        if 'CONSOLIDATION' in event_types and len(set(event_types)) >= 3:
            pattern_intensity = max(pattern_intensity, 1.5)
        
        return pattern_intensity
    
    def _calculate_inter_event_intensity(self, events: List[MarketEvent]) -> float:
        """Calculate intensity from inter-event timing dynamics"""
        
        if len(events) < 3:
            return 0.0
        
        # Sort by time
        sorted_events = sorted(events, key=lambda e: e.session_time_minutes)
        
        # Calculate inter-event intervals
        intervals = []
        for i in range(1, len(sorted_events)):
            interval = sorted_events[i].session_time_minutes - sorted_events[i-1].session_time_minutes
            intervals.append(max(0.1, interval))  # Prevent zero intervals
        
        if not intervals:
            return 0.0
        
        # Calculate clustering intensity
        mean_interval = np.mean(intervals)
        interval_variance = np.var(intervals)
        
        # High clustering = low mean interval + high variance (bursts)
        clustering_intensity = 1.0 / mean_interval
        
        # Burst detection (variance significantly higher than mean)
        if interval_variance > mean_interval * 2:
            clustering_intensity *= 1.5
        
        # Very tight clustering bonus
        if mean_interval < 2.0:  # Events within 2 minutes average
            clustering_intensity *= 2.0
        
        return min(3.0, clustering_intensity)  # Cap at 3.0
    
    def _determine_alert_level(self, intensity: float) -> str:
        """Determine alert level based on intensity score"""
        
        for alert_level in ['CRITICAL', 'RED_ALERT', 'HIGH', 'ELEVATED', 'NORMAL']:
            if intensity >= self.alert_thresholds[alert_level]:
                return alert_level
        
        return 'NORMAL'
    
    def _estimate_cascade_timing(self, events: List[MarketEvent], intensity: float) -> float:
        """Estimate time to cascade based on intensity and event patterns"""
        
        if intensity < self.intensity_threshold:
            return 0.0  # No cascade expected
        
        # Base timing from intensity (higher intensity = sooner cascade)
        base_time = max(0.5, 10.0 - (intensity * 5.0))
        
        # Pattern-specific adjustments
        event_types = [e.event_type for e in events]
        
        # Immediate cascade indicators
        if 'REVERSAL' in event_types:
            base_time *= 0.3  # Reversals lead to quick cascades
        
        if 'REDELIVERY' in event_types and 'FPFVG' in event_types:
            base_time *= 0.5  # FPFVG redeliveries are fast
        
        # Multi-expansion indicates building momentum
        expansion_count = sum(1 for et in event_types if 'EXPANSION' in et)
        if expansion_count >= 2:
            base_time *= 0.7
        
        return base_time
    
    def _identify_dominant_pattern(self, events: List[MarketEvent]) -> Tuple[str, float]:
        """Identify the dominant pattern and confidence"""
        
        if len(events) < 2:
            return "Insufficient events", 0.0
        
        event_types = [e.event_type for e in events]
        sequence_str = ' → '.join(event_types)
        
        # Check for known patterns
        best_pattern = "Unknown"
        best_confidence = 0.0
        
        for pattern, multiplier in self.pattern_multipliers.items():
            if pattern in sequence_str:
                # Confidence based on pattern strength and frequency
                confidence = min(1.0, multiplier / 3.0)
                if confidence > best_confidence:
                    best_pattern = pattern
                    best_confidence = confidence
        
        # If no specific pattern found, describe general structure
        if best_pattern == "Unknown":
            if len(set(event_types)) == 1:
                best_pattern = f"Repeating {event_types[0]}"
                best_confidence = 0.3
            else:
                # Most frequent event type
                from collections import Counter
                most_common = Counter(event_types).most_common(1)[0]
                best_pattern = f"{most_common[0]}-dominant sequence"
                best_confidence = 0.4
        
        return best_pattern, best_confidence
    
    def _calculate_inter_event_stats(self, events: List[MarketEvent]) -> Dict[str, float]:
        """Calculate comprehensive inter-event statistics"""
        
        if len(events) < 2:
            return {'mean_interval': 0.0, 'interval_variance': 0.0, 'event_rate_per_hour': 0.0}
        
        # Sort by time
        sorted_events = sorted(events, key=lambda e: e.session_time_minutes)
        
        # Calculate intervals
        intervals = []
        for i in range(1, len(sorted_events)):
            interval = sorted_events[i].session_time_minutes - sorted_events[i-1].session_time_minutes
            intervals.append(max(0.01, interval))
        
        mean_interval = np.mean(intervals)
        interval_variance = np.var(intervals)
        
        # Calculate event rate per hour
        total_time = sorted_events[-1].session_time_minutes - sorted_events[0].session_time_minutes
        if total_time > 0:
            event_rate_per_hour = (len(events) - 1) * 60.0 / total_time
        else:
            event_rate_per_hour = 0.0
        
        return {
            'mean_interval_minutes': mean_interval,
            'interval_variance': interval_variance,
            'event_rate_per_hour': event_rate_per_hour,
            'total_time_span_minutes': total_time if len(events) > 1 else 0.0,
            'clustering_coefficient': interval_variance / max(0.01, mean_interval)
        }
    
    def _generate_linguistic_indicators(self, events: List[MarketEvent], intensity: float) -> Dict[str, Any]:
        """Generate linguistic analysis indicators"""
        
        event_types = [e.event_type for e in events]
        
        # Event type diversity
        unique_types = len(set(event_types))
        total_types = len(event_types)
        diversity_ratio = unique_types / max(1, total_types)
        
        # Pattern complexity
        sequence_str = ' → '.join(event_types)
        
        # Linguistic features
        indicators = {
            'event_type_diversity': diversity_ratio,
            'sequence_length': len(events),
            'unique_event_types': unique_types,
            'sequence_complexity': len(sequence_str.split(' → ')),
            'contains_high_value_events': any(et in ['FPFVG', 'REDELIVERY', 'REVERSAL'] for et in event_types),
            'expansion_dominated': sum(1 for et in event_types if 'EXPANSION' in et) > len(event_types) * 0.4,
            'consolidation_breakout': 'CONSOLIDATION' in event_types and len(set(event_types)) >= 3,
            'cascade_readiness_score': min(1.0, intensity / 2.0)
        }
        
        return indicators
    
    def _log_intensity_results(self, result: EventIntensityResult):
        """Log intensity calculation results"""
        
        if result.crystallization_detected:
            self.logger.critical(f"⚡⚡⚡ CRYSTALLIZATION DETECTED ⚡⚡⚡")
            self.logger.critical(f"   Event Intensity: {result.intensity_score:.3f}")
            self.logger.critical(f"   Alert Level: {result.alert_level}")
            self.logger.critical(f"   Cascade ETA: {result.time_to_cascade_minutes:.1f} minutes")
            self.logger.critical(f"   Dominant Pattern: {result.dominant_event_pattern}")
            
        elif result.alert_level in ['RED_ALERT', 'HIGH']:
            self.logger.warning(f"⚡ HIGH EVENT INTENSITY DETECTED")
            self.logger.warning(f"   Intensity: {result.intensity_score:.3f}")
            self.logger.warning(f"   Alert Level: {result.alert_level}")
            
        else:
            self.logger.info(f"⚡ Event Intensity Analysis Complete:")
            self.logger.info(f"   Intensity: {result.intensity_score:.3f}")
            self.logger.info(f"   Alert Level: {result.alert_level}")
            self.logger.info(f"   Dominant Pattern: {result.dominant_event_pattern}")
    
    def _create_default_result(self, reason: str) -> EventIntensityResult:
        """Create default result for edge cases"""
        
        return EventIntensityResult(
            intensity_score=0.0,
            crystallization_detected=False,
            alert_level='NORMAL',
            time_to_cascade_minutes=0.0,
            dominant_event_pattern=reason,
            pattern_confidence=0.0,
            inter_event_stats={'mean_interval_minutes': 0.0, 'interval_variance': 0.0, 'event_rate_per_hour': 0.0},
            linguistic_indicators={'sequence_length': 0}
        )

def test_event_intensity_monitor():
    """Test the Event Intensity Monitor with sample data"""
    
    print("⚡ TESTING EVENT INTENSITY MONITOR")
    print("=" * 45)
    
    # Create sample events based on discovered patterns
    sample_events = [
        MarketEvent("09:30:00", "CONSOLIDATION", 23500.0, 0.5, "session_start", 0.0),
        MarketEvent("09:32:00", "EXPANSION", 23520.0, 1.2, "expansion_start", 2.0),
        MarketEvent("09:35:00", "REDELIVERY", 23515.0, 2.1, "fpfvg_redelivery", 5.0),
        MarketEvent("09:37:00", "FPFVG", 23530.0, 1.8, "fpfvg_formation", 7.0),
        MarketEvent("09:38:00", "REVERSAL", 23505.0, 3.2, "major_reversal", 8.0)
    ]
    
    # Initialize monitor
    monitor = EventIntensityMonitor()
    
    # Calculate intensity
    result = monitor.calculate_event_intensity(sample_events)
    
    # Display results
    print(f"\n📊 INTENSITY ANALYSIS RESULTS:")
    print(f"   Intensity Score: {result.intensity_score:.3f}")
    print(f"   Crystallization: {'✅ DETECTED' if result.crystallization_detected else 'Not detected'}")
    print(f"   Alert Level: {result.alert_level}")
    print(f"   Cascade ETA: {result.time_to_cascade_minutes:.1f} minutes")
    print(f"   Dominant Pattern: {result.dominant_event_pattern}")
    print(f"   Pattern Confidence: {result.pattern_confidence:.3f}")
    
    print(f"\n📈 Inter-Event Statistics:")
    for stat, value in result.inter_event_stats.items():
        print(f"   {stat}: {value:.2f}")
    
    print(f"\n🧠 Linguistic Indicators:")
    for indicator, value in result.linguistic_indicators.items():
        print(f"   {indicator}: {value}")
    
    print(f"\n✅ Event Intensity Monitor validation complete")
    
    return result

if __name__ == "__main__":
    test_result = test_event_intensity_monitor()