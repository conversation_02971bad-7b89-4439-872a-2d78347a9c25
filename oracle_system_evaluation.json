{"timestamp": "2025-08-09T02:06:28.020924", "data_flow": {"enhanced_sessions": {"available": 69, "status": "healthy", "sample_sessions": ["enhanced_ASIA_Lvl-1_2025_07_23", "pipeline_metrics", "enhanced_MIDNIGHT_Lvl-1_2025_08_07", "enhanced_ASIA_Lvl-1_2025_08_06", "_enhance_marker"]}, "grammar_bridge": {"total_events": 799, "unique_sessions": 29, "status": "healthy", "event_types": 82, "time_span_minutes": 1437.0}, "rg_scaler": {"status": "healthy", "optimal_scale": 15.0, "event_density": 0.34782608695652173, "scaling_confidence": 1.0, "test_session": "NYAM_Lvl-1_2025_08_04_REAL", "events_processed": 52}, "three_oracle": {"status": "healthy", "predictions_generated": 5, "latency_ms": 82.40985870361328, "oracle_choice": "grammar_deterministic", "confidence": 0.95, "timing_window": "0.5min"}}, "component_integration": {"calibration": {"status": "error", "error": "CalibrationCompartment.run() missing 1 required positional argument: 'input_manifest'"}, "predict": {"status": "healthy", "latency_ms": 64.67700004577637, "predictions_count": 5, "used_calibration": false, "stats": {"total_predictions": 5, "avg_latency_ms": 0.9256839752197266, "p95_latency_ms": 2.331972122192383, "success_rate": 1.0, "health_status": "OK", "calibration_loaded": false, "oracle_system_health": "OK"}}, "end_to_end": {"status": "error", "error": "CalibrationCompartment.run() missing 1 required positional argument: 'input_manifest'"}}, "performance": {"latency": {"average_ms": 80.06043434143066, "min_ms": 68.06182861328125, "max_ms": 111.32121086120605, "std_dev_ms": 15.853890159503093, "samples": 5}, "memory": {"status": "psutil_not_available"}, "accuracy": {"predictions_count": 5, "average_confidence": 0.95, "min_confidence": 0.95, "max_confidence": 0.95, "oracle_distribution": {"grammar_deterministic": 5}, "high_confidence_predictions": 5}}, "error_handling": {"missing_data": {"handles_empty_events": true, "graceful_degradation": true, "status": "healthy"}, "invalid_session": {"returns_empty_list": true, "no_exception_thrown": true, "status": "healthy"}, "component_failure": {"has_fallback_mechanism": true, "fallback_predictions_count": 3, "maintains_api_contract": true, "status": "healthy"}}, "recommendations": ["⚙️ HIGH: Fix calibration component issues", "🔄 CRITICAL: Fix end-to-end pipeline integration"], "overall_status": "NEEDS_FIXES"}