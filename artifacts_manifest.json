{"generated_by": "run_compartments.py", "timestamp": "{\"timestamp\": \"now\"}", "artifacts": [{"compartment": "grammar_bridge", "output": {"compartment": "grammar_bridge", "artifacts": {"cascade_events_file": "grammar_bridge/cascade_events.json", "grammar_analysis_file": "grammar_bridge/grammar_analysis.json", "output_dir": "grammar_bridge"}, "runtime_seconds": 0.0562741756439209, "health_ok": true, "status": "COMPLETED", "metrics": {"sessions_processed": 67, "total_cascade_events": 799, "grammar_patterns_found": 2, "avg_events_per_session": 11.925373134328359}}}, {"compartment": "predict", "output": {"compartment": "predict", "artifacts": {"predictions_dir": "predictions", "predictions_file": "predictions/predictions.json", "stats_file": "predictions/service_stats.json", "health_ok": false}, "runtime_seconds": 0.092, "health_ok": false, "status": "DEGRADED"}}]}