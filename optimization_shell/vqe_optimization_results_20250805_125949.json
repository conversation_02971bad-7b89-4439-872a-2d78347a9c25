{"optimization_metadata": {"timestamp": "2025-08-05T12:59:49.336221", "method": "COBYLA", "configuration": {"max_iterations": 100, "tolerance": 1e-06, "domain_constraints": true}}, "optimization_results": {"success": true, "final_mae_minutes": 5.128707639047453, "iterations": 9, "execution_time_seconds": 0.0031731128692626953, "performance_improvement_pct": 0.0, "constraints_satisfied": true}, "optimized_parameters": {"total_parameters": 8, "dimensions": 4, "alphas": [8.8775, 8.8775, 8.8775, 8.8775], "decays": [0.00442, 0.004862000000000001, 0.0053040000000000006, 0.005746]}, "convergence_analysis": {"initial_mae": 5.128849333019275, "final_mae": 5.128707639047453, "convergence_history": [5.128849333019275, Infinity, 5.128848799927937, Infinity, 5.128848269525027, Infinity, 5.128847741793284, Infinity, 5.128707639047453], "iterations_to_convergence": 9}}